# Pre-commit hooks配置
# 在提交代码前自动运行代码质量检查

repos:
  # 基础代码检查
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
        description: 移除行尾空白字符
      - id: end-of-file-fixer
        description: 确保文件以换行符结尾
      - id: check-yaml
        description: 检查YAML文件语法
      - id: check-toml
        description: 检查TOML文件语法
      - id: check-json
        description: 检查JSON文件语法
      - id: check-added-large-files
        description: 检查大文件
        args: ['--maxkb=1000']
      - id: check-merge-conflict
        description: 检查合并冲突标记
      - id: debug-statements
        description: 检查调试语句
      - id: check-docstring-first
        description: 检查文档字符串位置
      - id: check-executables-have-shebangs
        description: 检查可执行文件的shebang
      - id: mixed-line-ending
        description: 检查混合行结束符

  # Python代码格式化
  - repo: https://github.com/psf/black
    rev: 23.7.0
    hooks:
      - id: black
        description: 使用Black格式化Python代码
        language_version: python3
        args: [--line-length=100]

  # 导入排序
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        description: 排序Python导入
        args: [--profile=black, --line-length=100]

  # 代码质量检查
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        description: 使用Flake8检查代码质量
        additional_dependencies:
          - flake8-docstrings
          - flake8-import-order
          - flake8-bugbear
        args: [--max-line-length=100]

  # 类型检查
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.5.1
    hooks:
      - id: mypy
        description: 使用MyPy进行类型检查
        additional_dependencies: [types-PyYAML, types-requests]
        args: [--ignore-missing-imports]

  # 安全检查
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        description: 使用Bandit进行安全检查
        args: [-r, src]
        exclude: tests/

  # 依赖安全检查
  - repo: https://github.com/pyupio/safety
    rev: 2.3.4
    hooks:
      - id: safety
        description: 检查依赖包安全漏洞
        args: [--json]

  # 文档检查
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        description: 检查文档字符串风格
        args: [--convention=google]

  # 配置文件检查
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.32.0
    hooks:
      - id: yamllint
        description: 检查YAML文件格式
        args: [-d, relaxed]

  # 提交信息检查
  - repo: https://github.com/commitizen-tools/commitizen
    rev: 3.6.0
    hooks:
      - id: commitizen
        description: 检查提交信息格式
        stages: [commit-msg]

  # 许可证检查
  - repo: https://github.com/Lucas-C/pre-commit-hooks
    rev: v1.5.4
    hooks:
      - id: insert-license
        description: 插入许可证头
        files: \.py$
        args:
          - --license-filepath
          - LICENSE_HEADER.txt
          - --comment-style
          - "#"

# 全局配置
default_stages: [commit]
fail_fast: false
minimum_pre_commit_version: 3.0.0

# CI配置
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
