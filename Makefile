# Hey-AIBI 智能语音助手系统 Makefile
# 提供常用的开发、测试、部署命令

.PHONY: help install install-dev test test-unit test-integration test-performance
.PHONY: lint format type-check security-check quality-check
.PHONY: clean clean-cache clean-logs clean-models clean-all
.PHONY: docs docs-serve run run-dev debug
.PHONY: build package docker-build docker-run
.PHONY: setup-env check-env

# 默认目标
.DEFAULT_GOAL := help

# 变量定义
PYTHON := python
PIP := pip
PYTEST := pytest
BLACK := black
FLAKE8 := flake8
MYPY := mypy
BANDIT := bandit

# 项目目录
SRC_DIR := src
TEST_DIR := tests
DOCS_DIR := docs
CONFIG_DIR := config
MODELS_DIR := models
LOGS_DIR := logs

# 帮助信息
help: ## 显示帮助信息
	@echo "Hey-AIBI 智能语音助手系统 - 开发工具"
	@echo ""
	@echo "可用命令:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# 环境设置
setup-env: ## 设置开发环境
	@echo "🚀 设置开发环境..."
	conda env create -f environment.yml || conda env update -f environment.yml
	@echo "✅ 开发环境设置完成"

check-env: ## 检查环境依赖
	@echo "🔍 检查环境依赖..."
	@$(PYTHON) --version
	@$(PIP) --version
	@echo "✅ 环境检查完成"

# 安装依赖
install: ## 安装生产依赖
	@echo "📦 安装生产依赖..."
	$(PIP) install -e .
	@echo "✅ 生产依赖安装完成"

install-dev: ## 安装开发依赖
	@echo "📦 安装开发依赖..."
	$(PIP) install -e ".[dev,test,docs]"
	@echo "✅ 开发依赖安装完成"

# 代码质量检查
lint: ## 运行代码检查
	@echo "🔍 运行代码检查..."
	$(FLAKE8) $(SRC_DIR) $(TEST_DIR)
	@echo "✅ 代码检查完成"

format: ## 格式化代码
	@echo "🎨 格式化代码..."
	$(BLACK) $(SRC_DIR) $(TEST_DIR)
	isort $(SRC_DIR) $(TEST_DIR)
	@echo "✅ 代码格式化完成"

type-check: ## 类型检查
	@echo "🔍 运行类型检查..."
	$(MYPY) $(SRC_DIR)
	@echo "✅ 类型检查完成"

security-check: ## 安全检查
	@echo "🔒 运行安全检查..."
	$(BANDIT) -r $(SRC_DIR)
	@echo "✅ 安全检查完成"

quality-check: lint type-check security-check ## 运行所有质量检查
	@echo "✅ 所有质量检查完成"

# 测试
test: ## 运行所有测试
	@echo "🧪 运行所有测试..."
	$(PYTEST) $(TEST_DIR) -v
	@echo "✅ 所有测试完成"

test-unit: ## 运行单元测试
	@echo "🧪 运行单元测试..."
	$(PYTEST) $(TEST_DIR)/unit -v -m "unit"
	@echo "✅ 单元测试完成"

test-integration: ## 运行集成测试
	@echo "🧪 运行集成测试..."
	$(PYTEST) $(TEST_DIR)/integration -v -m "integration"
	@echo "✅ 集成测试完成"

test-performance: ## 运行性能测试
	@echo "🧪 运行性能测试..."
	$(PYTEST) $(TEST_DIR)/performance -v -m "performance"
	@echo "✅ 性能测试完成"

test-coverage: ## 运行测试覆盖率检查
	@echo "🧪 运行测试覆盖率检查..."
	$(PYTEST) $(TEST_DIR) --cov=$(SRC_DIR) --cov-report=html --cov-report=term
	@echo "✅ 测试覆盖率检查完成"

# 清理
clean-cache: ## 清理缓存文件
	@echo "🧹 清理缓存文件..."
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true
	find . -type f -name "*.pyo" -delete 2>/dev/null || true
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	rm -rf .pytest_cache .mypy_cache .coverage htmlcov
	@echo "✅ 缓存清理完成"

clean-logs: ## 清理日志文件
	@echo "🧹 清理日志文件..."
	rm -rf $(LOGS_DIR)/*.log $(LOGS_DIR)/*.log.*
	@echo "✅ 日志清理完成"

clean-models: ## 清理模型缓存
	@echo "🧹 清理模型缓存..."
	find $(MODELS_DIR) -name "*.cache" -delete 2>/dev/null || true
	@echo "✅ 模型缓存清理完成"

clean: clean-cache clean-logs ## 清理所有临时文件
	@echo "✅ 清理完成"

clean-all: clean clean-models ## 清理所有文件（包括模型）
	@echo "✅ 完全清理完成"

# 运行
run: ## 运行应用程序
	@echo "🚀 启动应用程序..."
	$(PYTHON) -m src.main

run-dev: ## 运行开发模式
	@echo "🚀 启动开发模式..."
	$(PYTHON) -m src.main --config $(CONFIG_DIR)/config.dev.yaml --debug

debug: ## 调试模式运行
	@echo "🐛 启动调试模式..."
	$(PYTHON) -m pdb -m src.main --config $(CONFIG_DIR)/config.dev.yaml

# 文档
docs: ## 生成文档
	@echo "📚 生成文档..."
	cd $(DOCS_DIR) && make html
	@echo "✅ 文档生成完成"

docs-serve: docs ## 启动文档服务器
	@echo "📚 启动文档服务器..."
	cd $(DOCS_DIR)/_build/html && $(PYTHON) -m http.server 8000

# 构建和打包
build: clean ## 构建项目
	@echo "🔨 构建项目..."
	$(PYTHON) -m build
	@echo "✅ 项目构建完成"

package: build ## 打包项目
	@echo "📦 打包项目..."
	$(PYTHON) -m twine check dist/*
	@echo "✅ 项目打包完成"

# Docker
docker-build: ## 构建Docker镜像
	@echo "🐳 构建Docker镜像..."
	docker build -t hey-aibi:latest .
	@echo "✅ Docker镜像构建完成"

docker-run: ## 运行Docker容器
	@echo "🐳 运行Docker容器..."
	docker run -it --rm -p 8080:8080 hey-aibi:latest
	@echo "✅ Docker容器运行完成"

# 开发工作流
dev-setup: setup-env install-dev ## 完整开发环境设置
	@echo "✅ 开发环境完全设置完成"

dev-check: quality-check test-unit ## 开发检查流程
	@echo "✅ 开发检查完成"

ci-check: quality-check test-coverage ## CI检查流程
	@echo "✅ CI检查完成"

# 项目信息
info: ## 显示项目信息
	@echo "📋 项目信息:"
	@echo "  名称: Hey-AIBI 智能语音助手系统"
	@echo "  版本: 1.0.0"
	@echo "  Python版本: $(shell $(PYTHON) --version)"
	@echo "  项目目录: $(shell pwd)"
	@echo "  源码目录: $(SRC_DIR)"
	@echo "  测试目录: $(TEST_DIR)"
	@echo "  配置目录: $(CONFIG_DIR)"

# 快速开始
quickstart: dev-setup dev-check ## 快速开始开发
	@echo "🎉 快速开始完成！现在可以开始开发了。"
	@echo ""
	@echo "常用命令:"
	@echo "  make run-dev    # 运行开发模式"
	@echo "  make test-unit  # 运行单元测试"
	@echo "  make format     # 格式化代码"
	@echo "  make lint       # 代码检查"
