#!/usr/bin/env python3
"""
测试运行脚本
展示重组后的测试目录结构和运行方式
"""

import subprocess
import sys
from pathlib import Path


def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"🧪 {description}")
    print(f"{'='*60}")
    print(f"命令: {cmd}")
    print("-" * 60)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=120)
        if result.returncode == 0:
            print("✅ 测试通过")
            if result.stdout:
                # 只显示关键信息
                lines = result.stdout.split('\n')
                summary_lines = [line for line in lines if 'passed' in line or 'failed' in line or 'error' in line]
                if summary_lines:
                    print("结果摘要:")
                    for line in summary_lines[-3:]:  # 显示最后几行摘要
                        if line.strip():
                            print(f"  {line}")
        else:
            print("❌ 测试失败")
            if result.stderr:
                print("错误信息:")
                print(result.stderr[:500])  # 只显示前500字符
    except subprocess.TimeoutExpired:
        print("⏰ 测试超时")
    except Exception as e:
        print(f"❌ 运行错误: {e}")


def main():
    """主函数"""
    print("🎯 Hey-AIBI 测试目录重组验证")
    print("=" * 60)
    
    # 检查当前目录
    if not Path("tests").exists():
        print("❌ 错误: 请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 显示新的目录结构
    print("\n📁 新的测试目录结构:")
    print("-" * 30)
    try:
        result = subprocess.run("find tests -type f -name '*.py' | head -20", 
                              shell=True, capture_output=True, text=True)
        if result.stdout:
            for line in result.stdout.strip().split('\n'):
                print(f"  {line}")
    except:
        print("  无法显示目录结构")
    
    # 激活conda环境的前缀
    conda_prefix = "conda activate aibi && "
    
    # 测试命令列表
    test_commands = [
        {
            "cmd": f"{conda_prefix}python -m pytest tests/unit/test_state_machine.py -v --tb=short",
            "desc": "运行状态机单元测试"
        },
        {
            "cmd": f"{conda_prefix}python -m pytest tests/performance/test_system_performance.py::TestSystemPerformance::test_state_transition_performance -v",
            "desc": "运行状态转换性能测试"
        },
        {
            "cmd": f"{conda_prefix}python -m pytest tests/e2e/ --collect-only",
            "desc": "检查端到端测试收集"
        },
        {
            "cmd": f"{conda_prefix}python -m pytest tests/manual/ --collect-only",
            "desc": "检查手动测试收集"
        },
        {
            "cmd": f"{conda_prefix}python -m pytest --collect-only -q | grep -E '(unit|integration|e2e|performance|manual)' | wc -l",
            "desc": "统计各类测试数量"
        }
    ]
    
    # 运行测试
    for test in test_commands:
        run_command(test["cmd"], test["desc"])
    
    # 显示测试标记使用示例
    print(f"\n{'='*60}")
    print("🏷️  测试标记使用示例")
    print(f"{'='*60}")
    
    examples = [
        "pytest -m unit                    # 只运行单元测试",
        "pytest -m integration             # 只运行集成测试", 
        "pytest -m performance             # 只运行性能测试",
        "pytest -m 'not slow'              # 排除慢速测试",
        "pytest tests/unit/                # 运行单元测试目录",
        "pytest tests/performance/         # 运行性能测试目录",
        "pytest --co -q                    # 查看所有测试",
    ]
    
    for example in examples:
        print(f"  {example}")
    
    print(f"\n{'='*60}")
    print("✅ 测试目录重组验证完成")
    print("📖 详细说明请查看: tests/README.md")
    print(f"{'='*60}")


if __name__ == "__main__":
    main()
