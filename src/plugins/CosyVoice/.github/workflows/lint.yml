name: Lint

on:
  pull_request:
  push:

jobs:
  quick-checks:
    runs-on: ubuntu-latest
    steps:
      - name: <PERSON><PERSON> CosyVoice
        uses: actions/checkout@v1
      - name: Checkout PR tip
        run: |
          set -eux
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            # We are on a PR, so actions/checkout leaves us on a merge commit.
            # Check out the actual tip of the branch.
            git checkout ${{ github.event.pull_request.head.sha }}
          fi
          echo ::set-output name=commit_sha::$(git rev-parse HEAD)
        id: get_pr_tip
      - name: Ensure no tabs
        run: |
          (! git grep -I -l $'\t' -- . ':(exclude)*.txt' ':(exclude)*.svg' ':(exclude)**Makefile' ':(exclude)**/contrib/**' ':(exclude)third_party' ':(exclude).gitattributes' ':(exclude).gitmodules' || (echo "The above files have tabs; please convert them to spaces"; false))
      - name: Ensure no trailing whitespace
        run: |
          (! git grep -I -n $' $' -- . ':(exclude)*.txt' ':(exclude)third_party' ':(exclude).gitattributes' ':(exclude).gitmodules' || (echo "The above files have trailing whitespace; please remove them"; false))

  flake8-py3:
    runs-on: ubuntu-latest
    steps:
      - name: Setup Python
        uses: actions/setup-python@v1
        with:
          python-version: 3.9
          architecture: x64
      - name: Fetch CosyVoice
        uses: actions/checkout@v1
      - name: Checkout PR tip
        run: |
          set -eux
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            # We are on a PR, so actions/checkout leaves us on a merge commit.
            # Check out the actual tip of the branch.
            git checkout ${{ github.event.pull_request.head.sha }}
          fi
          echo ::set-output name=commit_sha::$(git rev-parse HEAD)
        id: get_pr_tip
      - name: Run flake8
        run: |
          set -eux
          pip install flake8==3.8.2 flake8-bugbear flake8-comprehensions flake8-executable flake8-pyi==20.5.0 mccabe pycodestyle==2.6.0 pyflakes==2.2.0
          flake8 --version
          flake8 --max-line-length 180 --ignore B006,B008,B905,C408,E402,E731,E741,W503,W504,F401,F403,F405,F841 --exclude ./third_party/,./runtime/python/grpc/cosyvoice_pb2*py
          if [ $? != 0 ]; then exit 1; fi