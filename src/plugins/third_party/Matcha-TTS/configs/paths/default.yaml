# path to root directory
# this requires PROJECT_ROOT environment variable to exist
# you can replace it with "." if you want the root to be the current working directory
root_dir: ${oc.env:PROJECT_ROOT}

# path to data directory
data_dir: ${paths.root_dir}/data/

# path to logging directory
log_dir: ${paths.root_dir}/logs/

# path to output directory, created dynamically by hydra
# path generation pattern is specified in `configs/hydra/default.yaml`
# use it to store all files generated during the run, like ckpts and metrics
output_dir: ${hydra:runtime.output_dir}

# path to working directory
work_dir: ${hydra:runtime.cwd}
