[build-system]
requires = ["setuptools", "wheel", "cython==0.29.35", "numpy==1.24.3", "packaging"]

[tool.black]
line-length = 120
target-version = ['py310']
exclude = '''

(
  /(
      \.eggs         # exclude a few common directories in the
    | \.git          # root of the project
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
  )/
  | foo.py           # also separately exclude a file named foo.py in
                     # the root of the project
)
'''

[tool.pytest.ini_options]
addopts = [
  "--color=yes",
  "--durations=0",
  "--strict-markers",
  "--doctest-modules",
]
filterwarnings = [
  "ignore::DeprecationWarning",
  "ignore::UserWarning",
]
log_cli = "True"
markers = [
  "slow: slow tests",
]
minversion = "6.0"
testpaths = "tests/"

[tool.coverage.report]
exclude_lines = [
    "pragma: nocover",
    "raise NotImplementedError",
    "raise NotImplementedError()",
    "if __name__ == .__main__.:",
]
