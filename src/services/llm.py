"""
Dify平台LLM服务实现
支持异步调用、错误处理、重试机制
"""

import asyncio
import json
import time
import uuid
from typing import Optional, Dict, Any, List, AsyncGenerator
import aiohttp
import logging
from dataclasses import dataclass, field

from ..interfaces.llm_interface import (
    LLMInterface, LLMProvider, LLMRequest, LLMResponse, 
    LLMStreamChunk, LLMMessage, ConversationInterface
)


@dataclass
class DifyConfig:
    """Dify配置"""
    api_url: str
    api_key: str
    timeout: int = 30
    retry_attempts: int = 3
    retry_delay: float = 1.0
    max_retry_delay: float = 10.0


@dataclass
class ConversationContext:
    """对话上下文"""
    conversation_id: str
    user_id: str
    messages: List[LLMMessage] = field(default_factory=list)
    created_at: float = field(default_factory=time.time)
    last_activity: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)


class DifyLLMService(LLMInterface, ConversationInterface):
    """Dify平台LLM服务"""
    
    def __init__(self):
        self.config: Optional[DifyConfig] = None
        self.session: Optional[aiohttp.ClientSession] = None
        self.logger = logging.getLogger(__name__)
        self.conversations: Dict[str, ConversationContext] = {}
        self.is_initialized = False
        
    async def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化LLM服务"""
        try:
            self.config = DifyConfig(
                api_url=config.get("api_url", "https://api.dify.ai/v1"),
                api_key=config.get("api_key", ""),
                timeout=config.get("timeout", 30),
                retry_attempts=config.get("retry_attempts", 3),
                retry_delay=config.get("retry_delay", 1.0),
                max_retry_delay=config.get("max_retry_delay", 10.0)
            )
            
            if not self.config.api_key:
                raise ValueError("API密钥不能为空")
            
            # 创建HTTP会话
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
            
            # 测试连接
            health_ok = await self.health_check()
            if not health_ok:
                self.logger.warning("健康检查失败，但继续初始化")
            
            self.is_initialized = True
            self.logger.info("Dify LLM服务初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"初始化失败: {e}")
            return False
    
    async def chat(self, request: LLMRequest) -> Optional[LLMResponse]:
        """同步对话"""
        if not self.is_initialized or not self.session:
            raise RuntimeError("服务未初始化")
        
        start_time = time.time()
        
        try:
            # 构建请求数据
            data = self._build_request_data(request, stream=False)
            
            # 发送请求（带重试）
            response_data = await self._send_request_with_retry(data)
            
            # 解析响应
            processing_time = time.time() - start_time
            return self._parse_response(response_data, processing_time)
            
        except Exception as e:
            self.logger.error(f"对话请求失败: {e}")
            return None
    
    async def chat_stream(self, request: LLMRequest) -> AsyncGenerator[LLMStreamChunk, None]:
        """流式对话"""
        if not self.is_initialized or not self.session:
            raise RuntimeError("服务未初始化")
        
        try:
            # 构建请求数据
            data = self._build_request_data(request, stream=True)
            
            # 发送流式请求
            async for chunk in self._send_stream_request(data):
                yield chunk
                
        except Exception as e:
            self.logger.error(f"流式对话失败: {e}")
            # 返回错误块
            yield LLMStreamChunk(
                content=f"错误: {str(e)}",
                is_final=True,
                chunk_id=str(uuid.uuid4())
            )
    
    async def health_check(self) -> bool:
        """健康检查"""
        if not self.session:
            return False
        
        try:
            # 发送简单的测试请求
            test_request = LLMRequest(
                messages=[LLMMessage(role="user", content="hello", timestamp=time.time())],
                max_tokens=10
            )
            
            response = await self.chat(test_request)
            return response is not None
            
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            return False
    
    def get_provider(self) -> LLMProvider:
        """获取提供商"""
        return LLMProvider.DIFY
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "provider": "dify",
            "api_url": self.config.api_url if self.config else None,
            "initialized": self.is_initialized,
            "conversations_count": len(self.conversations)
        }
    
    def _build_request_data(self, request: LLMRequest, stream: bool = False) -> Dict[str, Any]:
        """构建请求数据"""
        # 获取最后一条用户消息作为query
        user_messages = [msg for msg in request.messages if msg.role == "user"]
        if not user_messages:
            raise ValueError("请求中没有用户消息")
        
        query = user_messages[-1].content
        
        # 构建基础数据 - 根据Dify API要求调整
        data = {
            "inputs": {
                # 根据Dify应用要求，提供必需的参数
                "query": query,
                "k": 2  # Dify应用要求的数值参数，设置为2
            },
            "query": query,
            "response_mode": "streaming" if stream else "blocking",
            "user": request.metadata.get("user_id", "default_user") if request.metadata else "default_user"
        }
        
        # 添加对话ID（如果存在）
        if request.metadata and "conversation_id" in request.metadata:
            data["conversation_id"] = request.metadata["conversation_id"]
        else:
            data["conversation_id"] = ""
        
        # 如果元数据中有自定义inputs，使用它们
        if request.metadata and "inputs" in request.metadata:
            data["inputs"].update(request.metadata["inputs"])
        
        return data
    
    async def _send_request_with_retry(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """发送请求（带重试机制）"""
        last_exception = None
        delay = self.config.retry_delay
        
        for attempt in range(self.config.retry_attempts):
            try:
                headers = {
                    "Authorization": f"Bearer {self.config.api_key}",
                    "Content-Type": "application/json"
                }
                
                url = f"{self.config.api_url}/chat-messages"
                
                async with self.session.post(url, headers=headers, json=data) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        error_text = await response.text()
                        raise aiohttp.ClientResponseError(
                            request_info=response.request_info,
                            history=response.history,
                            status=response.status,
                            message=f"HTTP {response.status}: {error_text}"
                        )
                        
            except Exception as e:
                last_exception = e
                if attempt < self.config.retry_attempts - 1:
                    self.logger.warning(f"请求失败，{delay:.1f}秒后重试 (尝试 {attempt + 1}/{self.config.retry_attempts}): {e}")
                    await asyncio.sleep(delay)
                    delay = min(delay * 2, self.config.max_retry_delay)  # 指数退避
                else:
                    self.logger.error(f"所有重试都失败了: {e}")
        
        raise last_exception
    
    async def _send_stream_request(self, data: Dict[str, Any]) -> AsyncGenerator[LLMStreamChunk, None]:
        """发送流式请求"""
        headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }
        
        url = f"{self.config.api_url}/chat-messages"
        
        async with self.session.post(url, headers=headers, json=data) as response:
            if response.status != 200:
                error_text = await response.text()
                raise aiohttp.ClientResponseError(
                    request_info=response.request_info,
                    history=response.history,
                    status=response.status,
                    message=f"HTTP {response.status}: {error_text}"
                )
            
            chunk_id = 0
            async for line in response.content:
                line = line.decode('utf-8').strip()
                if line.startswith('data: '):
                    try:
                        json_data = json.loads(line[6:])  # 移除 'data: ' 前缀
                        
                        if json_data.get('event') == 'message':
                            content = json_data.get('answer', '')
                            if content:
                                yield LLMStreamChunk(
                                    content=content,
                                    is_final=False,
                                    chunk_id=str(chunk_id)
                                )
                                chunk_id += 1
                        elif json_data.get('event') == 'message_end':
                            yield LLMStreamChunk(
                                content="",
                                is_final=True,
                                chunk_id=str(chunk_id)
                            )
                            break
                            
                    except json.JSONDecodeError:
                        continue
    
    def _parse_response(self, response_data: Dict[str, Any], processing_time: float) -> LLMResponse:
        """解析响应数据"""
        return LLMResponse(
            content=response_data.get("answer", ""),
            usage={
                "prompt_tokens": response_data.get("metadata", {}).get("usage", {}).get("prompt_tokens", 0),
                "completion_tokens": response_data.get("metadata", {}).get("usage", {}).get("completion_tokens", 0),
                "total_tokens": response_data.get("metadata", {}).get("usage", {}).get("total_tokens", 0)
            },
            model=response_data.get("model", "unknown"),
            finish_reason=response_data.get("metadata", {}).get("retriever_resources", "completed"),
            processing_time=processing_time
        )

    # ConversationInterface 实现
    async def start_conversation(self, user_id: str) -> str:
        """开始对话"""
        conversation_id = str(uuid.uuid4())
        self.conversations[conversation_id] = ConversationContext(
            conversation_id=conversation_id,
            user_id=user_id
        )
        self.logger.info(f"开始新对话: {conversation_id} (用户: {user_id})")
        return conversation_id

    async def add_message(self, conversation_id: str, message: LLMMessage) -> None:
        """添加消息"""
        if conversation_id in self.conversations:
            self.conversations[conversation_id].messages.append(message)
            self.conversations[conversation_id].last_activity = time.time()
        else:
            raise ValueError(f"对话不存在: {conversation_id}")

    async def get_history(self, conversation_id: str, limit: int = 10) -> List[LLMMessage]:
        """获取对话历史"""
        if conversation_id in self.conversations:
            messages = self.conversations[conversation_id].messages
            return messages[-limit:] if limit > 0 else messages
        else:
            return []

    async def clear_history(self, conversation_id: str) -> None:
        """清除对话历史"""
        if conversation_id in self.conversations:
            self.conversations[conversation_id].messages.clear()

    async def end_conversation(self, conversation_id: str) -> None:
        """结束对话"""
        if conversation_id in self.conversations:
            del self.conversations[conversation_id]
            self.logger.info(f"结束对话: {conversation_id}")

    def is_conversation_active(self, conversation_id: str) -> bool:
        """检查对话是否活跃"""
        return conversation_id in self.conversations

    async def cleanup(self):
        """清理资源"""
        if self.session:
            await self.session.close()
            self.session = None
        self.conversations.clear()
        self.is_initialized = False
        self.logger.info("LLM服务资源已清理")
