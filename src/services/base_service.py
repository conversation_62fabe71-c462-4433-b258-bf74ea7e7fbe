"""
服务基类
定义所有服务的通用接口和行为
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from enum import Enum
from dataclasses import dataclass
import time


class ServiceStatus(Enum):
    """服务状态"""
    UNINITIALIZED = "uninitialized"
    INITIALIZING = "initializing"
    READY = "ready"
    RUNNING = "running"
    ERROR = "error"
    STOPPING = "stopping"
    STOPPED = "stopped"


@dataclass
class ServiceMetrics:
    """服务指标"""
    requests_total: int = 0
    requests_success: int = 0
    requests_error: int = 0
    avg_response_time: float = 0.0
    last_error: Optional[str] = None
    uptime: float = 0.0


class BaseService(ABC):
    """服务基类"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{name}")
        
        # 服务状态
        self.status = ServiceStatus.UNINITIALIZED
        self.start_time = None
        
        # 服务指标
        self.metrics = ServiceMetrics()
        
        # 健康检查
        self.last_health_check = None
        self.health_check_interval = config.get('health_check_interval', 60)
        
        # 错误处理
        self.max_retries = config.get('max_retries', 3)
        self.retry_delay = config.get('retry_delay', 1.0)
    
    async def initialize(self) -> bool:
        """初始化服务"""
        try:
            self.status = ServiceStatus.INITIALIZING
            self.logger.info(f"初始化服务: {self.name}")
            
            # 调用子类的初始化方法
            success = await self._initialize_impl()
            
            if success:
                self.status = ServiceStatus.READY
                self.logger.info(f"服务 {self.name} 初始化成功")
            else:
                self.status = ServiceStatus.ERROR
                self.logger.error(f"服务 {self.name} 初始化失败")
            
            return success
            
        except Exception as e:
            self.status = ServiceStatus.ERROR
            self.logger.error(f"服务 {self.name} 初始化异常: {e}")
            return False
    
    async def start(self) -> bool:
        """启动服务"""
        if self.status != ServiceStatus.READY:
            self.logger.error(f"服务 {self.name} 状态不正确，无法启动: {self.status}")
            return False
        
        try:
            self.status = ServiceStatus.RUNNING
            self.start_time = time.time()
            self.logger.info(f"启动服务: {self.name}")
            
            # 调用子类的启动方法
            success = await self._start_impl()
            
            if not success:
                self.status = ServiceStatus.ERROR
                self.logger.error(f"服务 {self.name} 启动失败")
            
            return success
            
        except Exception as e:
            self.status = ServiceStatus.ERROR
            self.logger.error(f"服务 {self.name} 启动异常: {e}")
            return False
    
    async def stop(self) -> bool:
        """停止服务"""
        try:
            self.status = ServiceStatus.STOPPING
            self.logger.info(f"停止服务: {self.name}")
            
            # 调用子类的停止方法
            success = await self._stop_impl()
            
            self.status = ServiceStatus.STOPPED
            self.logger.info(f"服务 {self.name} 已停止")
            
            return success
            
        except Exception as e:
            self.status = ServiceStatus.ERROR
            self.logger.error(f"服务 {self.name} 停止异常: {e}")
            return False
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            # 调用子类的健康检查方法
            is_healthy = await self._health_check_impl()
            self.last_health_check = time.time()
            
            if not is_healthy:
                self.logger.warning(f"服务 {self.name} 健康检查失败")
            
            return is_healthy
            
        except Exception as e:
            self.logger.error(f"服务 {self.name} 健康检查异常: {e}")
            return False
    
    async def restart(self) -> bool:
        """重启服务"""
        self.logger.info(f"重启服务: {self.name}")
        
        # 停止服务
        await self.stop()
        
        # 等待一段时间
        await asyncio.sleep(1.0)
        
        # 重新初始化和启动
        if await self.initialize():
            return await self.start()
        
        return False
    
    def update_metrics(self, success: bool, response_time: float, error: Optional[str] = None) -> None:
        """更新服务指标"""
        self.metrics.requests_total += 1
        
        if success:
            self.metrics.requests_success += 1
        else:
            self.metrics.requests_error += 1
            if error:
                self.metrics.last_error = error
        
        # 更新平均响应时间
        if self.metrics.requests_total == 1:
            self.metrics.avg_response_time = response_time
        else:
            self.metrics.avg_response_time = (
                (self.metrics.avg_response_time * (self.metrics.requests_total - 1) + response_time) 
                / self.metrics.requests_total
            )
        
        # 更新运行时间
        if self.start_time:
            self.metrics.uptime = time.time() - self.start_time
    
    def get_status(self) -> ServiceStatus:
        """获取服务状态"""
        return self.status
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取服务指标"""
        return {
            "name": self.name,
            "status": self.status.value,
            "metrics": {
                "requests_total": self.metrics.requests_total,
                "requests_success": self.metrics.requests_success,
                "requests_error": self.metrics.requests_error,
                "success_rate": (
                    self.metrics.requests_success / self.metrics.requests_total 
                    if self.metrics.requests_total > 0 else 0.0
                ),
                "avg_response_time": self.metrics.avg_response_time,
                "last_error": self.metrics.last_error,
                "uptime": self.metrics.uptime
            },
            "last_health_check": self.last_health_check
        }
    
    def is_healthy(self) -> bool:
        """检查服务是否健康"""
        return (
            self.status == ServiceStatus.RUNNING and
            (self.last_health_check is None or 
             time.time() - self.last_health_check < self.health_check_interval * 2)
        )
    
    # 抽象方法，子类必须实现
    @abstractmethod
    async def _initialize_impl(self) -> bool:
        """子类实现的初始化方法"""
        pass
    
    @abstractmethod
    async def _start_impl(self) -> bool:
        """子类实现的启动方法"""
        pass
    
    @abstractmethod
    async def _stop_impl(self) -> bool:
        """子类实现的停止方法"""
        pass
    
    @abstractmethod
    async def _health_check_impl(self) -> bool:
        """子类实现的健康检查方法"""
        pass
