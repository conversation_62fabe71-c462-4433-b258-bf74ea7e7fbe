"""
事件总线系统
支持异步事件分发、优先级处理、错误隔离
"""

import asyncio
import logging
from typing import Dict, List, Callable, Any, Optional, Set
from dataclasses import dataclass, field
from enum import Enum
import time
import uuid
from collections import defaultdict, deque


class EventPriority(Enum):
    """事件优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class Event:
    """事件对象"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    data: Dict[str, Any] = field(default_factory=dict)
    priority: EventPriority = EventPriority.NORMAL
    timestamp: float = field(default_factory=time.time)
    source: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3


@dataclass
class EventHandler:
    """事件处理器"""
    id: str
    callback: Callable
    priority: EventPriority = EventPriority.NORMAL
    error_handler: Optional[Callable] = None
    max_retries: int = 3
    timeout: float = 30.0


@dataclass
class EventStats:
    """事件统计"""
    total_events: int = 0
    processed_events: int = 0
    failed_events: int = 0
    avg_processing_time: float = 0.0
    last_error: Optional[str] = None


class EventBus:
    """事件总线"""
    
    def __init__(self, config_or_max_queue_size = 10000):
        self.logger = logging.getLogger(__name__)

        # 支持字典配置或直接传递max_queue_size
        if isinstance(config_or_max_queue_size, dict):
            config = config_or_max_queue_size
            self.max_queue_size = config.get('max_queue_size', 10000)
            self.num_workers = config.get('worker_count', 4)
            self.batch_size = config.get('batch_size', 10)
            self.batch_timeout = config.get('batch_timeout', 0.1)
        else:
            self.max_queue_size = config_or_max_queue_size
            self.num_workers = 4
            self.batch_size = 10
            self.batch_timeout = 0.1

        # 事件队列（按优先级分组）
        self.event_queues: Dict[EventPriority, deque] = {
            priority: deque() for priority in EventPriority
        }
        
        # 事件处理器
        self.handlers: Dict[str, List[EventHandler]] = defaultdict(list)
        self.handler_counter = 0
        
        # 运行状态
        self.is_running = False
        self.worker_tasks: List[asyncio.Task] = []
        
        # 统计信息
        self.stats = EventStats()
        
        # 错误处理
        self.dead_letter_queue: deque = deque(maxlen=1000)
        self.circuit_breakers: Dict[str, Dict[str, Any]] = {}
        
        # 事件过滤
        self.event_filters: List[Callable] = []
        
        # 中间件
        self.middlewares: List[Callable] = []
    
    async def initialize(self) -> bool:
        """初始化事件总线"""
        try:
            self.logger.info("初始化事件总线...")
            
            # 启动工作线程
            await self._start_workers()
            
            self.is_running = True
            self.logger.info("事件总线初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"事件总线初始化失败: {e}")
            return False
    
    async def _start_workers(self) -> None:
        """启动工作线程"""
        for i in range(self.num_workers):
            task = asyncio.create_task(self._worker(f"worker-{i}"))
            self.worker_tasks.append(task)
        
        self.logger.info(f"启动了 {self.num_workers} 个事件处理工作线程")
    
    async def _worker(self, worker_name: str) -> None:
        """事件处理工作线程"""
        self.logger.debug(f"事件处理工作线程 {worker_name} 启动")
        
        while self.is_running:
            try:
                # 按优先级获取事件
                event = await self._get_next_event()
                
                if event:
                    await self._process_event(event, worker_name)
                else:
                    # 没有事件时短暂休眠
                    await asyncio.sleep(0.01)
                    
            except Exception as e:
                self.logger.error(f"工作线程 {worker_name} 处理事件异常: {e}")
                await asyncio.sleep(0.1)
        
        self.logger.debug(f"事件处理工作线程 {worker_name} 停止")
    
    async def _get_next_event(self) -> Optional[Event]:
        """按优先级获取下一个事件"""
        # 按优先级顺序检查队列
        for priority in [EventPriority.CRITICAL, EventPriority.HIGH, 
                        EventPriority.NORMAL, EventPriority.LOW]:
            queue = self.event_queues[priority]
            if queue:
                return queue.popleft()
        
        return None
    
    async def _process_event(self, event: Event, worker_name: str) -> None:
        """处理单个事件"""
        start_time = time.time()
        
        try:
            # 应用事件过滤器
            if not await self._apply_filters(event):
                self.logger.debug(f"事件被过滤器拒绝: {event.name}")
                return
            
            # 应用中间件
            await self._apply_middlewares(event, "before")
            
            # 获取事件处理器
            handlers = self.handlers.get(event.name, [])
            if not handlers:
                self.logger.debug(f"没有找到事件处理器: {event.name}")
                return
            
            # 按优先级排序处理器
            handlers.sort(key=lambda h: h.priority.value, reverse=True)
            
            # 执行处理器
            for handler in handlers:
                await self._execute_handler(event, handler, worker_name)
            
            # 应用中间件
            await self._apply_middlewares(event, "after")
            
            # 更新统计
            processing_time = time.time() - start_time
            self._update_stats(True, processing_time)
            
            self.logger.debug(f"事件处理完成: {event.name} (耗时: {processing_time:.3f}s)")
            
        except Exception as e:
            processing_time = time.time() - start_time
            self._update_stats(False, processing_time, str(e))
            
            self.logger.error(f"事件处理失败: {event.name}, 错误: {e}")
            
            # 重试逻辑
            if event.retry_count < event.max_retries:
                event.retry_count += 1
                await self._enqueue_event(event)
                self.logger.info(f"事件重试: {event.name} (第{event.retry_count}次)")
            else:
                # 移到死信队列
                self.dead_letter_queue.append(event)
                self.logger.error(f"事件处理失败，移入死信队列: {event.name}")
    
    async def _execute_handler(self, event: Event, handler: EventHandler, worker_name: str) -> None:
        """执行事件处理器"""
        try:
            # 检查熔断器
            if self._is_circuit_breaker_open(handler.id):
                self.logger.warning(f"处理器 {handler.id} 熔断器开启，跳过执行")
                return
            
            # 执行处理器（带超时）
            await asyncio.wait_for(
                handler.callback(event.data),
                timeout=handler.timeout
            )
            
            # 重置熔断器
            self._reset_circuit_breaker(handler.id)
            
        except asyncio.TimeoutError:
            self.logger.error(f"处理器 {handler.id} 执行超时")
            self._record_handler_failure(handler.id)
            
        except Exception as e:
            self.logger.error(f"处理器 {handler.id} 执行异常: {e}")
            self._record_handler_failure(handler.id)
            
            # 调用错误处理器
            if handler.error_handler:
                try:
                    await handler.error_handler(event, e)
                except Exception as error_handler_e:
                    self.logger.error(f"错误处理器执行失败: {error_handler_e}")
    
    async def _apply_filters(self, event: Event) -> bool:
        """应用事件过滤器"""
        for filter_func in self.event_filters:
            try:
                if not await filter_func(event):
                    return False
            except Exception as e:
                self.logger.error(f"事件过滤器执行异常: {e}")
                return False
        
        return True
    
    async def _apply_middlewares(self, event: Event, phase: str) -> None:
        """应用中间件"""
        for middleware in self.middlewares:
            try:
                await middleware(event, phase)
            except Exception as e:
                self.logger.error(f"中间件执行异常: {e}")
    
    def _is_circuit_breaker_open(self, handler_id: str) -> bool:
        """检查熔断器是否开启"""
        breaker = self.circuit_breakers.get(handler_id)
        if not breaker:
            return False
        
        # 检查是否在熔断期内
        if time.time() - breaker['last_failure'] < breaker['timeout']:
            return breaker['failure_count'] >= breaker['threshold']
        
        return False
    
    def _record_handler_failure(self, handler_id: str) -> None:
        """记录处理器失败"""
        if handler_id not in self.circuit_breakers:
            self.circuit_breakers[handler_id] = {
                'failure_count': 0,
                'last_failure': 0,
                'threshold': 5,
                'timeout': 60
            }
        
        breaker = self.circuit_breakers[handler_id]
        breaker['failure_count'] += 1
        breaker['last_failure'] = time.time()
    
    def _reset_circuit_breaker(self, handler_id: str) -> None:
        """重置熔断器"""
        if handler_id in self.circuit_breakers:
            self.circuit_breakers[handler_id]['failure_count'] = 0
    
    def _update_stats(self, success: bool, processing_time: float, error: Optional[str] = None) -> None:
        """更新统计信息"""
        self.stats.total_events += 1
        
        if success:
            self.stats.processed_events += 1
        else:
            self.stats.failed_events += 1
            if error:
                self.stats.last_error = error
        
        # 更新平均处理时间
        if self.stats.total_events == 1:
            self.stats.avg_processing_time = processing_time
        else:
            self.stats.avg_processing_time = (
                (self.stats.avg_processing_time * (self.stats.total_events - 1) + processing_time) 
                / self.stats.total_events
            )
    
    async def emit(self, event_name: str, data: Dict[str, Any], 
                  priority: EventPriority = EventPriority.NORMAL,
                  source: Optional[str] = None) -> str:
        """发送事件"""
        event = Event(
            name=event_name,
            data=data,
            priority=priority,
            source=source
        )
        
        await self._enqueue_event(event)
        
        self.logger.debug(f"事件已发送: {event_name} (ID: {event.id})")
        return event.id
    
    async def _enqueue_event(self, event: Event) -> None:
        """将事件加入队列"""
        queue = self.event_queues[event.priority]
        
        # 检查队列大小
        if len(queue) >= self.max_queue_size:
            # 移除最旧的低优先级事件
            if event.priority in [EventPriority.CRITICAL, EventPriority.HIGH]:
                # 高优先级事件，移除低优先级事件
                for low_priority in [EventPriority.LOW, EventPriority.NORMAL]:
                    low_queue = self.event_queues[low_priority]
                    if low_queue:
                        dropped_event = low_queue.popleft()
                        self.logger.warning(f"队列满，丢弃低优先级事件: {dropped_event.name}")
                        break
            else:
                self.logger.warning(f"队列满，丢弃事件: {event.name}")
                return
        
        queue.append(event)
    
    async def subscribe(self, event_name: str, callback: Callable,
                       priority: EventPriority = EventPriority.NORMAL,
                       error_handler: Optional[Callable] = None,
                       max_retries: int = 3,
                       timeout: float = 30.0) -> str:
        """订阅事件"""
        handler_id = f"handler-{self.handler_counter}"
        self.handler_counter += 1
        
        handler = EventHandler(
            id=handler_id,
            callback=callback,
            priority=priority,
            error_handler=error_handler,
            max_retries=max_retries,
            timeout=timeout
        )
        
        self.handlers[event_name].append(handler)
        
        self.logger.debug(f"订阅事件: {event_name} (处理器ID: {handler_id})")
        return handler_id
    
    async def unsubscribe(self, event_name: str, handler_id: str) -> bool:
        """取消订阅"""
        handlers = self.handlers.get(event_name, [])
        
        for i, handler in enumerate(handlers):
            if handler.id == handler_id:
                del handlers[i]
                self.logger.debug(f"取消订阅: {event_name} (处理器ID: {handler_id})")
                return True
        
        return False
    
    def add_filter(self, filter_func: Callable) -> None:
        """添加事件过滤器"""
        self.event_filters.append(filter_func)
    
    def add_middleware(self, middleware_func: Callable) -> None:
        """添加中间件"""
        self.middlewares.append(middleware_func)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        queue_sizes = {
            priority.name: len(queue) 
            for priority, queue in self.event_queues.items()
        }
        
        return {
            "stats": {
                "total_events": self.stats.total_events,
                "processed_events": self.stats.processed_events,
                "failed_events": self.stats.failed_events,
                "success_rate": (
                    self.stats.processed_events / self.stats.total_events 
                    if self.stats.total_events > 0 else 0.0
                ),
                "avg_processing_time": self.stats.avg_processing_time,
                "last_error": self.stats.last_error
            },
            "queue_sizes": queue_sizes,
            "dead_letter_queue_size": len(self.dead_letter_queue),
            "active_handlers": sum(len(handlers) for handlers in self.handlers.values()),
            "circuit_breakers": len(self.circuit_breakers)
        }
    
    async def shutdown(self) -> None:
        """关闭事件总线"""
        self.logger.info("正在关闭事件总线...")
        
        self.is_running = False
        
        # 等待工作线程完成
        if self.worker_tasks:
            await asyncio.gather(*self.worker_tasks, return_exceptions=True)
        
        self.logger.info("事件总线已关闭")
