"""
CosyVoice TTS服务实现
支持流式语音合成、多种语音模式、异步处理
"""

import asyncio
import os
import sys
import time
import tempfile
import threading
from typing import Optional, Dict, Any, AsyncGenerator, List
import numpy as np
import torch
import torchaudio
import logging
from pathlib import Path
from dataclasses import dataclass
from queue import Queue, Empty

from ..interfaces.speech_interface import TTSInterface, TTSResult
from .base_service import BaseService
from .event_bus import EventPriority


@dataclass
class TTSConfig:
    """TTS配置"""
    model_path: str
    device: str = "cpu"
    num_threads: int = 4
    streaming: bool = True
    voice: str = "default"
    speed: float = 1.0
    volume: float = 1.0
    sample_rate: int = 22050


@dataclass
class TTSRequest:
    """TTS请求"""
    text: str
    voice: Optional[str] = None
    speed: Optional[float] = None
    volume: Optional[float] = None
    streaming: bool = False


@dataclass
class TTSStreamChunk:
    """TTS流式响应块"""
    audio_data: np.ndarray
    sample_rate: int
    is_final: bool
    chunk_id: str


class CosyVoiceTTSService(BaseService, TTSInterface):
    """CosyVoice TTS服务"""

    def __init__(self, config: Dict[str, Any], event_bus):
        super().__init__("tts", config)
        self.event_bus = event_bus
        self.tts_config: Optional[TTSConfig] = None
        self.model = None
        self.is_loaded = False
        self.synthesis_count = 0
        self.total_synthesis_time = 0.0
        self.total_audio_duration = 0.0
        self.last_synthesis_time = None

        # 流式处理相关
        self.audio_queue = Queue()
        self.is_synthesizing = False
        self.synthesis_thread = None

        # 音频播放相关
        self.audio_manager = None

        # 打断检测相关
        self.interruption_enabled = config.get("enable_interruption", True)
        self.current_synthesis_id = None
        self.is_interrupted = False
        self.interruption_callbacks = []

        # 连续播放相关
        self.enable_continuous_playback = config.get("enable_continuous_playback", True)
        self.continuous_player = None
        self.audio_chunks_buffer = []
        self.synthesis_queue = asyncio.Queue()
        self.is_continuous_mode = False
        
    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            # 构建模型路径
            model_name = self.config.get("model_name", "CosyVoice-300M")
            model_path = os.path.join("models", "tts", model_name)

            if not os.path.exists(model_path):
                self.logger.error(f"TTS模型路径不存在: {model_path}")
                return False

            self.tts_config = TTSConfig(
                model_path=model_path,
                device=self.config.get("device", "cpu"),
                num_threads=self.config.get("num_threads", 4),
                streaming=self.config.get("streaming", True),
                voice=self.config.get("voice", "default"),
                speed=self.config.get("speed", 1.0),
                volume=self.config.get("volume", 1.0)
            )
            
            # 加载模型
            if not await self._load_model():
                return False
            
            # 订阅相关事件
            await self.event_bus.subscribe("text_to_synthesize", self._on_text_to_synthesize)
            await self.event_bus.subscribe("tts_interrupt", self._on_tts_interrupt)
            
            self.logger.info(f"CosyVoice TTS服务初始化完成 - 模型: {model_name}")
            return True

        except Exception as e:
            self.logger.error(f"TTS服务初始化失败: {e}")
            return False

    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            # 订阅打断检测事件
            if self.interruption_enabled:
                await self.event_bus.subscribe("tts_interrupt", self._on_tts_interrupt)
                await self.event_bus.subscribe("tts_volume_adjust", self._on_volume_adjust)
                await self.event_bus.subscribe("tts_resume", self._on_tts_resume)

            self.logger.info("TTS服务已启动")
            return True
        except Exception as e:
            self.logger.error(f"TTS服务启动失败: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            await self.cleanup()
            self.logger.info("TTS服务已停止")
            return True
        except Exception as e:
            self.logger.error(f"TTS服务停止失败: {e}")
            return False

    async def _health_check_impl(self) -> bool:
        """健康检查实现"""
        try:
            return self.is_loaded and self.model is not None
        except Exception as e:
            self.logger.error(f"TTS健康检查失败: {e}")
            return False
    
    async def _load_model(self) -> bool:
        """加载CosyVoice模型"""
        try:
            self.logger.info(f"正在加载CosyVoice模型: {self.tts_config.model_path}")

            # 设置环境变量
            os.environ["OMP_NUM_THREADS"] = str(self.tts_config.num_threads)

            # 检查模型路径是否存在
            model_path = Path(self.tts_config.model_path)
            if not model_path.exists():
                raise FileNotFoundError(f"模型路径不存在: {self.tts_config.model_path}")

            # 添加CosyVoice路径到sys.path - 使用src/plugins目录
            project_root = Path(__file__).parent.parent.parent
            cosyvoice_path = project_root / "src" / "plugins" / "CosyVoice"
            matcha_path = project_root / "src" / "plugins" / "third_party" / "Matcha-TTS"

            # 检查CosyVoice源码是否存在
            if not cosyvoice_path.exists():
                raise FileNotFoundError(f"CosyVoice源码路径不存在: {cosyvoice_path}")

            # 检查Matcha-TTS源码是否存在
            if not matcha_path.exists():
                raise FileNotFoundError(f"Matcha-TTS源码路径不存在: {matcha_path}")

            # 添加路径到sys.path
            if str(cosyvoice_path) not in sys.path:
                sys.path.insert(0, str(cosyvoice_path))
            if str(matcha_path) not in sys.path:
                sys.path.insert(0, str(matcha_path))

            # 导入CosyVoice
            try:
                from cosyvoice.cli.cosyvoice import CosyVoice
                from cosyvoice.utils.file_utils import load_wav

                self.logger.info("CosyVoice模块导入成功")

                # 初始化模型
                self.model = CosyVoice(
                    str(self.tts_config.model_path),
                    load_jit=False,
                    load_trt=False,
                    fp16=False
                )

                # 获取可用的说话人
                try:
                    self.available_speakers = self.model.list_available_spks()
                    self.logger.info(f"可用说话人: {self.available_speakers}")

                    # 如果没有可用说话人，检查是否有参考音频文件（zero-shot模式）
                    if not self.available_speakers:
                        self.logger.info("检测到基础模型，检查zero-shot支持")

                        # 检查是否有参考音频文件
                        prompt_audio_path = Path(self.tts_config.model_path) / "asset" / "zero_shot_prompt.wav"
                        if prompt_audio_path.exists():
                            self.logger.info("找到参考音频文件，将使用zero-shot模式")
                            self.available_speakers = ["zero_shot"]
                            self.use_zero_shot = True
                            self.use_instruct = False
                            self.prompt_audio_path = str(prompt_audio_path)
                        else:
                            self.logger.info("未找到参考音频文件，使用默认模式")
                            self.available_speakers = ["default"]
                            self.use_zero_shot = False
                            self.use_instruct = False
                    else:
                        self.use_zero_shot = False
                        self.use_instruct = False

                except Exception as e:
                    self.logger.warning(f"无法获取说话人列表: {e}")
                    self.available_speakers = ["default"]
                    self.use_zero_shot = False
                    self.use_instruct = False

                # 模型预热（不影响服务初始化）
                try:
                    await self._warmup_model()
                except Exception as e:
                    self.logger.warning(f"模型预热失败，但服务仍可使用: {e}")

                self.is_loaded = True
                self.logger.info("CosyVoice模型加载完成")
                return True

            except ImportError as e:
                self.logger.error(f"无法导入CosyVoice模块: {e}")
                self._provide_installation_guidance()
                return False

        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            self._provide_installation_guidance()
            return False
    
    def _preprocess_text(self, text: str) -> str:
        """预处理文本以提高合成质量"""
        # 去除首尾空白
        text = text.strip()

        # 最小化预处理，只添加必要的标点符号
        # 不改变用户的原始语义
        if text and text[-1] not in "。！？.!?，":
            text += "。"

        return text

    def _provide_installation_guidance(self):
        """提供CosyVoice安装指导"""
        self.logger.error("=" * 60)
        self.logger.error("CosyVoice TTS模型加载失败！")
        self.logger.error("=" * 60)
        self.logger.error("请按照以下步骤安装CosyVoice:")
        self.logger.error("")
        self.logger.error("1. 克隆CosyVoice仓库到src/plugins目录:")
        self.logger.error("   cd src/plugins")
        self.logger.error("   git clone --recursive https://github.com/FunAudioLLM/CosyVoice.git")
        self.logger.error("   cd CosyVoice")
        self.logger.error("   git submodule update --init --recursive")
        self.logger.error("")
        self.logger.error("2. 安装依赖:")
        self.logger.error("   conda install -y -c conda-forge pynini==2.1.5")
        self.logger.error("   pip install -r requirements.txt")
        self.logger.error("")
        self.logger.error("3. 确保模型文件存在:")
        self.logger.error(f"   模型路径: {self.tts_config.model_path}")
        self.logger.error("   如果模型不存在，请使用ModelScope下载:")
        self.logger.error("   from modelscope import snapshot_download")
        self.logger.error("   snapshot_download('iic/CosyVoice-300M', local_dir='models/tts/CosyVoice-300M')")
        self.logger.error("")
        self.logger.error("4. 重启服务")
        self.logger.error("=" * 60)
    
    async def _warmup_model(self):
        """模型预热"""
        try:
            self.logger.info("正在预热TTS模型...")

            # 使用简短文本进行预热
            warmup_text = "你好"

            # 根据模型模式选择正确的推理方法
            if hasattr(self, 'use_zero_shot') and self.use_zero_shot:
                # Zero-shot模式预热
                self.logger.info("使用zero-shot模式进行预热")

                # 加载参考音频
                from cosyvoice.utils.file_utils import load_wav
                prompt_speech_16k = load_wav(self.prompt_audio_path, 16000)
                prompt_text = "希望你以后能够做的比我还好呦。"

                # 执行zero-shot推理预热
                for i, result in enumerate(self.model.inference_zero_shot(warmup_text, prompt_text, prompt_speech_16k, stream=False)):
                    if i == 0:  # 只需要第一个结果
                        break

            elif self.available_speakers and len(self.available_speakers) > 0 and self.available_speakers[0] not in ["default", "zero_shot"]:
                # SFT模式预热
                speaker = self.available_speakers[0]
                self.logger.info(f"使用SFT模式进行预热，说话人: {speaker}")

                # 执行SFT推理预热
                for i, result in enumerate(self.model.inference_sft(warmup_text, speaker, stream=False)):
                    if i == 0:  # 只需要第一个结果
                        break
            else:
                # 默认模式，跳过预热
                self.logger.info("使用默认模式，跳过模型预热")
                return

            self.logger.info("TTS模型预热完成")

        except Exception as e:
            self.logger.warning(f"TTS模型预热失败: {e}")
            # 预热失败不应该阻止服务启动
            self.logger.info("预热失败，但服务仍可正常使用")
    
    async def initialize(self, model_name: str = None, config: Dict[str, Any] = None) -> bool:
        """初始化语音合成模型"""
        # 如果提供了参数，更新配置
        if model_name:
            self.config["model_name"] = model_name
        if config:
            self.config.update(config)

        # 调用父类的initialize方法，这会调用_initialize_impl
        return await super().initialize()

    def set_audio_manager(self, audio_manager):
        """设置音频管理器用于播放音频"""
        self.audio_manager = audio_manager
        self.logger.info("音频管理器已设置，将支持音频播放")

        # 如果启用连续播放，初始化连续播放器
        if self.enable_continuous_playback and not self.continuous_player:
            try:
                from ..infrastructure.sounddevice_audio_player import SoundDeviceAudioPlayer, AudioChunk
                from ..interfaces.audio_interface import AudioConfig

                audio_config = AudioConfig(
                    sample_rate=16000,
                    channels=1,
                    chunk_size=1024,
                    output_device_id=None
                )

                self.continuous_player = SoundDeviceAudioPlayer(audio_config)

                # 简化初始化逻辑 - 延迟到实际使用时初始化
                self.logger.info("SoundDevice连续音频播放器已创建，将在使用时初始化")

            except Exception as e:
                self.logger.error(f"初始化连续播放器失败: {e}")
                self.enable_continuous_playback = False
    
    async def synthesize(self, text: str) -> Optional[TTSResult]:
        """语音合成"""
        if not self.is_loaded or self.model is None:
            self.logger.error("TTS模型未加载，请检查CosyVoice安装")
            return None

        if not text.strip():
            self.logger.warning("输入文本为空")
            return None

        # 预处理文本
        processed_text = self._preprocess_text(text)
        self.logger.debug(f"原始文本: {text}")
        self.logger.debug(f"处理后文本: {processed_text}")

        try:
            start_time = time.time()

            # 使用CosyVoice进行合成
            audio_chunks = []

            if hasattr(self, 'use_zero_shot') and self.use_zero_shot:
                # 使用zero-shot模式
                try:
                    # 加载参考音频
                    import torchaudio
                    prompt_speech, sample_rate = torchaudio.load(self.prompt_audio_path)

                    # 重采样到16kHz（CosyVoice要求）
                    if sample_rate != 16000:
                        resampler = torchaudio.transforms.Resample(sample_rate, 16000)
                        prompt_speech_16k = resampler(prompt_speech)
                    else:
                        prompt_speech_16k = prompt_speech

                    # 根据官方示例，使用固定的简短提示文本
                    # 参考GitHub官方示例中的用法
                    prompt_text = "希望你以后能够做的比我还好呦。"

                    self.logger.info(f"使用zero-shot模式进行合成，提示文本: {prompt_text}")
                    for i, result in enumerate(self.model.inference_zero_shot(processed_text, prompt_text, prompt_speech_16k, stream=False)):
                        if 'tts_speech' in result:
                            audio_tensor = result['tts_speech']
                            # 确保音频数据是numpy数组
                            if hasattr(audio_tensor, 'numpy'):
                                audio_data = audio_tensor.numpy().flatten()
                            else:
                                audio_data = audio_tensor.flatten()
                            audio_chunks.append(audio_data)
                            break  # 只取第一个结果
                except Exception as e:
                    self.logger.error(f"Zero-shot模式合成失败: {e}")
                    import traceback
                    self.logger.error(f"详细错误: {traceback.format_exc()}")
                    return None
            elif hasattr(self, 'use_instruct') and self.use_instruct:
                # 使用instruct模式
                try:
                    # 对于instruct模式，使用第一个可用说话人和指令文本
                    spk_id = self.available_speakers[0] if self.available_speakers else "default"
                    instruct_text = "用自然的语调朗读"

                    for i, result in enumerate(self.model.inference_instruct(text, spk_id, instruct_text, stream=False)):
                        if 'tts_speech' in result:
                            audio_tensor = result['tts_speech']
                            # 确保音频数据是numpy数组
                            if hasattr(audio_tensor, 'numpy'):
                                audio_data = audio_tensor.numpy().flatten()
                            else:
                                audio_data = audio_tensor.flatten()
                            audio_chunks.append(audio_data)
                            break  # 只取第一个结果
                except Exception as e:
                    self.logger.error(f"Instruct模式合成失败: {e}")
                    return None
            elif self.available_speakers and len(self.available_speakers) > 0 and self.available_speakers[0] not in ["default", "zero_shot"]:
                # 使用SFT模式（有可用说话人）
                try:
                    speaker = self.tts_config.voice if self.tts_config.voice != "default" else self.available_speakers[0]
                    for i, result in enumerate(self.model.inference_sft(text, speaker, stream=False)):
                        if 'tts_speech' in result:
                            audio_tensor = result['tts_speech']
                            # 确保音频数据是numpy数组
                            if hasattr(audio_tensor, 'numpy'):
                                audio_data = audio_tensor.numpy().flatten()
                            else:
                                audio_data = audio_tensor.flatten()
                            audio_chunks.append(audio_data)
                            break  # 只取第一个结果
                except Exception as e:
                    self.logger.error(f"SFT模式合成失败: {e}")
                    return None
            else:
                self.logger.error("无法确定合适的推理模式")
                return None

            if not audio_chunks:
                self.logger.error("TTS合成失败：无音频输出")
                return None

            audio_data = audio_chunks[0]
            processing_time = time.time() - start_time
            duration = len(audio_data) / self.tts_config.sample_rate

            # 更新统计信息
            self.synthesis_count += 1
            self.total_synthesis_time += processing_time
            self.total_audio_duration += duration
            self.last_synthesis_time = time.time()

            # 播放音频（如果有音频管理器）
            if self.audio_manager:
                try:
                    # 将音频数据重采样到16kHz（AudioManager的标准采样率）
                    if self.tts_config.sample_rate != 16000:
                        import torchaudio
                        audio_tensor = torch.from_numpy(audio_data).float()
                        resampler = torchaudio.transforms.Resample(self.tts_config.sample_rate, 16000)
                        resampled_audio = resampler(audio_tensor).numpy()
                    else:
                        resampled_audio = audio_data

                    self.logger.info(f"🔊 播放TTS音频，时长: {duration:.2f}秒")
                    await self.audio_manager.play_audio(resampled_audio)

                except Exception as e:
                    self.logger.error(f"音频播放失败: {e}")

            # 发布TTS完成事件
            await self.event_bus.emit("tts_finished", {
                "text": text,
                "duration": duration,
                "processing_time": processing_time
            })

            return TTSResult(
                audio_data=audio_data,
                sample_rate=self.tts_config.sample_rate,
                duration=duration,
                processing_time=processing_time
            )

        except Exception as e:
            self.logger.error(f"语音合成失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return None
    
    async def synthesize_streaming(self, text: str) -> AsyncGenerator[TTSStreamChunk, None]:
        """流式语音合成"""
        if not self.is_loaded or self.model is None:
            self.logger.error("TTS模型未加载，请检查CosyVoice安装")
            return

        if not text.strip():
            self.logger.warning("输入文本为空")
            return

        # 预处理文本
        processed_text = self._preprocess_text(text)
        self.logger.debug(f"流式合成 - 原始文本: {text}")
        self.logger.debug(f"流式合成 - 处理后文本: {processed_text}")

        try:
            # 生成合成ID并重置打断状态
            import uuid
            self.current_synthesis_id = str(uuid.uuid4())
            self.is_interrupted = False

            # 发布TTS开始事件
            await self.event_bus.emit("tts_started", {
                "text": text,
                "session_id": self.current_synthesis_id,
                "timestamp": time.time()
            })

            chunk_id = 0

            # 使用CosyVoice流式合成
            if hasattr(self, 'use_zero_shot') and self.use_zero_shot:
                # 使用zero-shot模式
                try:
                    # 加载参考音频
                    import torchaudio
                    prompt_speech, sample_rate = torchaudio.load(self.prompt_audio_path)

                    # 重采样到16kHz
                    if sample_rate != 16000:
                        resampler = torchaudio.transforms.Resample(sample_rate, 16000)
                        prompt_speech_16k = resampler(prompt_speech)
                    else:
                        prompt_speech_16k = prompt_speech

                    # 使用与非流式合成相同的提示文本
                    prompt_text = "希望你以后能够做的比我还好呦。"

                    self.logger.info(f"使用zero-shot流式模式进行合成，提示文本: {prompt_text}")
                    for i, result in enumerate(self.model.inference_zero_shot(processed_text, prompt_text, prompt_speech_16k, stream=True)):
                        # 检查是否被打断
                        if self.is_interrupted:
                            self.logger.info("TTS合成被打断，停止生成")
                            break

                        if 'tts_speech' in result:
                            audio_tensor = result['tts_speech']
                            if hasattr(audio_tensor, 'numpy'):
                                audio_data = audio_tensor.numpy().flatten()
                            else:
                                audio_data = audio_tensor.flatten()

                            yield TTSStreamChunk(
                                audio_data=audio_data,
                                sample_rate=self.tts_config.sample_rate,
                                is_final=False,
                                chunk_id=str(chunk_id)
                            )
                            chunk_id += 1
                except Exception as e:
                    self.logger.error(f"Zero-shot流式合成失败: {e}")
                    import traceback
                    self.logger.error(f"详细错误: {traceback.format_exc()}")
                    return
            else:
                # 使用SFT模式
                speaker = self.tts_config.voice if self.tts_config.voice != "default" else self.available_speakers[0]
                for i, result in enumerate(self.model.inference_sft(text, speaker, stream=True)):
                    # 检查是否被打断
                    if self.is_interrupted:
                        self.logger.info("TTS合成被打断，停止生成")
                        break

                    if 'tts_speech' in result:
                        audio_tensor = result['tts_speech']
                        # 确保音频数据是numpy数组
                        if hasattr(audio_tensor, 'numpy'):
                            audio_data = audio_tensor.numpy().flatten()
                        else:
                            audio_data = audio_tensor.flatten()

                        yield TTSStreamChunk(
                            audio_data=audio_data,
                            sample_rate=self.tts_config.sample_rate,
                            is_final=False,
                            chunk_id=str(chunk_id)
                        )
                        chunk_id += 1

            # 发送结束块
            yield TTSStreamChunk(
                audio_data=np.array([]),
                sample_rate=self.tts_config.sample_rate,
                is_final=True,
                chunk_id=str(chunk_id)
            )

        except Exception as e:
            self.logger.error(f"流式语音合成失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            # 发送错误块
            yield TTSStreamChunk(
                audio_data=np.array([]),
                sample_rate=self.tts_config.sample_rate,
                is_final=True,
                chunk_id="error"
            )
    
    def set_voice(self, voice: str) -> None:
        """设置语音"""
        if voice in self.available_speakers:
            self.tts_config.voice = voice
            self.logger.info(f"设置语音为: {voice}")
        else:
            self.logger.warning(f"不支持的语音: {voice}, 可用语音: {self.available_speakers}")
    
    def set_speed(self, speed: float) -> None:
        """设置语速"""
        self.tts_config.speed = max(0.5, min(2.0, speed))  # 限制在0.5-2.0之间
        self.logger.info(f"设置语速为: {self.tts_config.speed}")

    def set_volume(self, volume: float) -> None:
        """设置音量"""
        self.tts_config.volume = max(0.0, min(2.0, volume))  # 限制在0.0-2.0之间
        self.logger.info(f"设置音量为: {self.tts_config.volume}")
    
    async def _on_text_to_synthesize(self, event_data: Dict[str, Any]):
        """处理文本合成事件"""
        text = event_data.get("text", "")
        streaming = event_data.get("streaming", self.tts_config.streaming)
        
        if not text.strip():
            return
        
        try:
            session_id = event_data.get("session_id", "")

            if streaming:
                # 流式合成
                all_audio_chunks = []
                total_duration = 0.0
                chunk_count = 0

                # 选择播放模式
                use_continuous_player = (self.enable_continuous_playback and
                                       self.continuous_player is not None)

                # 如果需要使用连续播放器但还没初始化，现在初始化
                if use_continuous_player and not getattr(self.continuous_player, 'is_initialized', False):
                    try:
                        if await self.continuous_player.initialize():
                            self.logger.info("连续音频播放器初始化成功")
                            # 设置回调函数
                            self.continuous_player.on_playback_start = lambda: self.logger.info("🔊 连续播放开始")
                            self.continuous_player.on_playback_stop = lambda: self.logger.info("🔊 连续播放停止")
                            self.continuous_player.on_chunk_played = lambda chunk: self.logger.debug(f"🔊 播放音频块: {len(chunk.data)} 样本")
                        else:
                            self.logger.error("连续音频播放器初始化失败")
                            use_continuous_player = False
                    except Exception as e:
                        self.logger.error(f"连续播放器初始化异常: {e}")
                        use_continuous_player = False

                if use_continuous_player:
                    self.logger.info("🔊 使用连续音频播放器进行流式播放")
                    print("🔊 使用连续音频播放器进行流式播放")

                async for chunk in self.synthesize_streaming(text):
                    chunk_count += 1

                    # 处理音频数据
                    audio_data = chunk.audio_data

                    # 重采样到16kHz（如果需要）
                    if chunk.sample_rate != 16000:
                        import torchaudio
                        audio_tensor = torch.from_numpy(audio_data).float()
                        resampler = torchaudio.transforms.Resample(chunk.sample_rate, 16000)
                        audio_data = resampler(audio_tensor).numpy()

                    if use_continuous_player:
                        # 立即添加到连续播放器
                        from ..infrastructure.sounddevice_audio_player import AudioChunk
                        audio_chunk = AudioChunk(
                            data=audio_data,
                            sample_rate=16000,
                            channels=1,
                            timestamp=time.time(),
                            is_final=chunk.is_final
                        )
                        self.continuous_player.add_audio_chunk(audio_chunk)
                        self.logger.info(f"🔊 添加音频块 {chunk_count} 到连续播放器")
                    else:
                        # 收集音频块用于后续播放
                        all_audio_chunks.append(audio_data)

                    await self.event_bus.emit("tts_audio_chunk", {
                        "audio_data": audio_data,
                        "sample_rate": 16000,
                        "is_final": chunk.is_final,
                        "chunk_id": chunk.chunk_id,
                        "session_id": session_id
                    })

                # 处理播放完成
                if use_continuous_player:
                    self.logger.info(f"🔊 TTS流式合成完成，已发送 {chunk_count} 个音频块到连续播放器")
                    print(f"🔊 TTS流式合成完成，已发送 {chunk_count} 个音频块到连续播放器")

                    # 强制启动播放（如果还没开始）
                    if hasattr(self.continuous_player, 'force_start_playback'):
                        self.continuous_player.force_start_playback()
                elif all_audio_chunks and self.audio_manager:
                    try:
                        # 合并音频数据
                        combined_audio = np.concatenate(all_audio_chunks)
                        total_duration = len(combined_audio) / self.tts_config.sample_rate

                        self.logger.info(f"🔊 合并音频数据: {len(combined_audio)} 样本, 时长: {total_duration:.2f}秒")

                        # 重采样到16kHz（如果需要）
                        if self.tts_config.sample_rate != 16000:
                            import torchaudio
                            audio_tensor = torch.from_numpy(combined_audio).float()
                            resampler = torchaudio.transforms.Resample(self.tts_config.sample_rate, 16000)
                            resampled_audio = resampler(audio_tensor).numpy()
                            self.logger.info(f"🔊 音频重采样: {self.tts_config.sample_rate}Hz -> 16000Hz")
                        else:
                            resampled_audio = combined_audio

                        self.logger.info(f"🔊 开始播放TTS音频，时长: {total_duration:.2f}秒")
                        print(f"🔊 开始播放TTS音频，时长: {total_duration:.2f}秒")  # 强制显示
                        await self.audio_manager.play_audio(resampled_audio)
                        self.logger.info(f"🔊 TTS音频播放完成")
                        print(f"🔊 TTS音频播放完成")  # 强制显示

                    except Exception as e:
                        self.logger.error(f"流式音频播放失败: {e}")
                        import traceback
                        self.logger.error(f"详细错误: {traceback.format_exc()}")
                elif not all_audio_chunks:
                    self.logger.warning("🔊 没有收集到音频数据")
                elif not self.audio_manager:
                    self.logger.warning("🔊 音频管理器未设置")

                # 流式合成完成后发送响应完成事件
                await self.event_bus.emit("response_complete", {
                    "session_id": session_id,
                    "timestamp": time.time(),
                    "tts_completed": True
                }, priority=EventPriority.HIGH)

            else:
                # 同步合成
                result = await self.synthesize(text)
                if result:
                    await self.event_bus.emit("tts_audio_ready", {
                        "audio_data": result.audio_data,
                        "sample_rate": result.sample_rate,
                        "duration": result.duration,
                        "processing_time": result.processing_time,
                        "session_id": session_id
                    })

                    # 同步合成完成后发送响应完成事件
                    await self.event_bus.emit("response_complete", {
                        "session_id": session_id,
                        "timestamp": time.time(),
                        "tts_completed": True
                    }, priority=EventPriority.HIGH)

        except Exception as e:
            self.logger.error(f"处理TTS事件失败: {e}")
            # 即使TTS失败，也要发送响应完成事件，避免系统卡住
            session_id = event_data.get("session_id", "")
            await self.event_bus.emit("response_complete", {
                "session_id": session_id,
                "timestamp": time.time(),
                "tts_completed": False,
                "error": str(e)
            }, priority=EventPriority.HIGH)
    
    async def _on_tts_interrupt(self, event_data: Dict[str, Any]):
        """处理TTS中断事件"""
        self.logger.info("收到TTS中断信号")
        # 停止当前合成
        self.is_synthesizing = False
        
        # 清空音频队列
        while not self.audio_queue.empty():
            try:
                self.audio_queue.get_nowait()
            except Empty:
                break
    
    def get_available_voices(self) -> List[str]:
        """获取可用语音列表"""
        return self.available_speakers if self.is_loaded else []
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_path": self.tts_config.model_path if self.tts_config and self.is_loaded else None,
            "is_loaded": self.is_loaded,
            "device": self.tts_config.device if self.tts_config else None,
            "available_speakers": self.available_speakers if hasattr(self, 'available_speakers') else [],
            "current_voice": self.tts_config.voice if self.tts_config else None,
            "sample_rate": self.tts_config.sample_rate if self.tts_config else None
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        avg_synthesis_time = (
            self.total_synthesis_time / self.synthesis_count 
            if self.synthesis_count > 0 else 0.0
        )
        
        avg_audio_duration = (
            self.total_audio_duration / self.synthesis_count 
            if self.synthesis_count > 0 else 0.0
        )
        
        return {
            "synthesis_count": self.synthesis_count,
            "avg_synthesis_time": avg_synthesis_time,
            "avg_audio_duration": avg_audio_duration,
            "total_synthesis_time": self.total_synthesis_time,
            "total_audio_duration": self.total_audio_duration,
            "last_synthesis_time": self.last_synthesis_time,
            "model_info": self.get_model_info()
        }
    
    async def test_synthesis(self, text: str = "你好，这是一个测试") -> Optional[TTSResult]:
        """测试合成功能"""
        if not self.is_loaded:
            raise RuntimeError("TTS模型未加载")
        
        return await self.synthesize(text)
    
    async def cleanup(self):
        """清理资源"""
        self.is_synthesizing = False
        
        if self.synthesis_thread and self.synthesis_thread.is_alive():
            self.synthesis_thread.join(timeout=1.0)
        
        # 清空队列
        while not self.audio_queue.empty():
            try:
                self.audio_queue.get_nowait()
            except Empty:
                break
        
        self.model = None
        self.is_loaded = False
        self.logger.info("TTS服务资源已清理")

    # 打断检测相关方法
    async def _on_tts_interrupt(self, event_data: Dict[str, Any]):
        """处理TTS打断事件"""
        try:
            session_id = event_data.get('session_id')
            mode = event_data.get('mode', 'immediate')

            if session_id != self.current_synthesis_id:
                return

            self.is_interrupted = True

            if mode == 'immediate':
                # 立即停止合成
                self.is_synthesizing = False
                if self.audio_manager:
                    await self.audio_manager.stop_playback()

                self.logger.info("TTS立即停止")

            elif mode == 'sentence_end':
                # 标记在句子结束后停止
                self.is_interrupted = True
                self.logger.info("TTS将在句子结束后停止")

            # 发布TTS停止事件
            await self.event_bus.emit("tts_stopped", {
                "session_id": session_id,
                "reason": "interrupted",
                "mode": mode,
                "timestamp": time.time()
            })

        except Exception as e:
            self.logger.error(f"处理TTS打断失败: {e}")

    async def _on_volume_adjust(self, event_data: Dict[str, Any]):
        """处理音量调整事件"""
        try:
            session_id = event_data.get('session_id')
            volume = event_data.get('volume', 1.0)

            if session_id != self.current_synthesis_id:
                return

            if self.audio_manager:
                await self.audio_manager.set_volume(volume)

        except Exception as e:
            self.logger.error(f"处理音量调整失败: {e}")

    async def _on_tts_resume(self, event_data: Dict[str, Any]):
        """处理TTS恢复事件"""
        try:
            session_id = event_data.get('session_id')
            text = event_data.get('text', '')
            position = event_data.get('position', 0)

            if not text:
                return

            # 从指定位置恢复TTS
            resume_text = text[position:] if position < len(text) else text

            self.logger.info(f"恢复TTS播放: {session_id}")

            # 重新开始合成
            await self.event_bus.emit("text_to_synthesize", {
                "text": resume_text,
                "session_id": session_id,
                "streaming": True
            })

        except Exception as e:
            self.logger.error(f"处理TTS恢复失败: {e}")

    def add_interruption_callback(self, callback):
        """添加打断回调"""
        self.interruption_callbacks.append(callback)

    def remove_interruption_callback(self, callback):
        """移除打断回调"""
        try:
            self.interruption_callbacks.remove(callback)
        except ValueError:
            pass

    def is_playing(self) -> bool:
        """检查是否正在播放"""
        return self.is_synthesizing and not self.is_interrupted
