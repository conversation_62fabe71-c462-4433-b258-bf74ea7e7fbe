#!/usr/bin/env python3
"""
FunASR语音识别服务
基于SenseVoiceSmall模型实现实时语音识别
"""

import asyncio
import numpy as np
import torch
import logging
import time
import threading
from pathlib import Path
from typing import Dict, Any, Optional, Callable, List, Tuple
from collections import deque
import tempfile
import soundfile as sf

from .base_service import BaseService, ServiceStatus
from .event_bus import EventBus, EventPriority
from ..core.silence_detector import SilenceDetector, SilenceConfig

try:
    from funasr import AutoModel
    from funasr.utils.postprocess_utils import rich_transcription_postprocess
    FUNASR_AVAILABLE = True
except ImportError:
    FUNASR_AVAILABLE = False
    print("警告: FunASR未安装，请运行: pip install funasr")


class AudioBuffer:
    """音频缓冲区管理"""
    
    def __init__(self, 
                 sample_rate: int = 16000,
                 max_duration: float = 30.0,
                 min_duration: float = 1.0):
        self.sample_rate = sample_rate
        self.max_duration = max_duration
        self.min_duration = min_duration
        
        # 计算样本数
        self.max_samples = int(max_duration * sample_rate)
        self.min_samples = int(min_duration * sample_rate)
        
        # 音频缓冲区
        self.buffer = deque(maxlen=self.max_samples)
        self.lock = threading.Lock()
        
        self.logger = logging.getLogger(__name__)
    
    def add_audio(self, audio_chunk: np.ndarray) -> None:
        """添加音频数据到缓冲区"""
        with self.lock:
            self.buffer.extend(audio_chunk.flatten())
    
    def get_audio_for_recognition(self, clear_after: bool = True) -> Optional[np.ndarray]:
        """获取用于识别的音频数据"""
        with self.lock:
            if len(self.buffer) < self.min_samples:
                return None
            
            # 获取所有缓冲的音频
            audio_data = np.array(self.buffer, dtype=np.float32)
            
            if clear_after:
                self.buffer.clear()
            
            return audio_data
    
    def clear(self) -> None:
        """清空缓冲区"""
        with self.lock:
            self.buffer.clear()
    
    def get_buffer_info(self) -> Dict[str, Any]:
        """获取缓冲区信息"""
        with self.lock:
            return {
                "buffer_size": len(self.buffer),
                "buffer_duration": len(self.buffer) / self.sample_rate,
                "max_samples": self.max_samples,
                "min_samples": self.min_samples,
                "is_ready": len(self.buffer) >= self.min_samples
            }

    def get_recent_audio(self, duration: float = 0.5) -> Optional[np.ndarray]:
        """获取最近指定时长的音频数据"""
        with self.lock:
            if len(self.buffer) == 0:
                return None

            # 计算需要的样本数
            samples_needed = int(duration * self.sample_rate)

            # 将deque转换为列表以支持切片操作
            buffer_list = list(self.buffer)

            # 获取最近的音频数据
            if len(buffer_list) >= samples_needed:
                recent_samples = buffer_list[-samples_needed:]
            else:
                recent_samples = buffer_list[:]

            return np.array(recent_samples, dtype=np.float32)


class FunASRProcessor:
    """FunASR处理器"""

    def __init__(self, model_path: str, device: str = "auto",
                 vad_model: str = "fsmn-vad", punc_model: str = "ct-punc"):
        self.model_path = model_path
        self.vad_model = vad_model
        self.punc_model = punc_model

        # FunASR设备选择逻辑
        if device == "auto":
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device
        self.model = None
        self.is_loaded = False

        self.logger = logging.getLogger(__name__)

    def _process_sensevoice_output(self, text: str) -> str:
        """处理SenseVoice的输出格式，提取纯文本并保留标点符号"""
        import re

        # SenseVoice的输出格式: < | zh | > < | NEUTRAL | > < | Speech | > < | withi tn | >实际文本内容
        # 我们需要移除这些特殊标记，但保留文本内容和标点符号

        # 移除所有的特殊标记 < | ... | >
        cleaned_text = re.sub(r'<\s*\|\s*[^|]*\s*\|\s*>', '', text)

        # 清理多余的空格
        cleaned_text = re.sub(r'\s+', '', cleaned_text)

        return cleaned_text.strip()

    def load_model(self) -> bool:
        """加载FunASR模型 - 使用完整的AutoModel pipeline"""
        if not FUNASR_AVAILABLE:
            self.logger.error("FunASR未安装")
            return False

        try:
            print(f"🔄 正在加载FunASR完整pipeline: {self.model_path}")
            print(f"📋 VAD模型: {self.vad_model}")
            print(f"📋 PUNC模型: {self.punc_model}")

            # 使用FunASR标准AutoModel pipeline
            # 包括ASR + VAD + PUNC模型

            # 设置环境变量禁用FunASR的进度条和日志
            import os
            os.environ['FUNASR_DISABLE_LOG'] = '1'
            os.environ['TQDM_DISABLE'] = '1'

            # PyTorch线程配置已在__init__中设置

            self.model = AutoModel(
                model=self.model_path,
                vad_model=self.vad_model,
                punc_model=self.punc_model,
                vad_kwargs={"max_single_segment_time": 30000},
                device=self.device,
                disable_update=True,
                disable_log=True,  # 禁用详细日志，避免dataset_classes信息
                disable_pbar=True  # 禁用进度条
            )

            print(f"✅ FunASR pipeline加载完成")
            print(f"🔍 模型组件: ASR={bool(self.model.model)}, VAD={bool(getattr(self.model, 'vad_model', None))}, PUNC={bool(getattr(self.model, 'punc_model', None))}")

            # 模型预热 - 消除首次推理延迟
            print("🔥 模型预热中...")
            self._warmup_model()
            print("✅ 模型预热完成")

            self.is_loaded = True
            self.logger.info("FunASR模型加载成功")

            return True
            
        except Exception as e:
            self.logger.error(f"FunASR模型加载失败: {e}")
            return False

    def _warmup_model(self):
        """模型预热 - 进行一次虚拟推理以消除首次延迟"""
        try:
            import tempfile
            import numpy as np
            import soundfile as sf

            # 创建1秒的虚拟音频（静音）
            dummy_audio = np.zeros(self.sample_rate, dtype=np.float32)

            # 保存为临时文件
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                sf.write(temp_file.name, dummy_audio, self.sample_rate)
                temp_path = temp_file.name

            try:
                # 进行虚拟推理
                import sys
                import io
                from contextlib import redirect_stdout, redirect_stderr

                with redirect_stdout(io.StringIO()), redirect_stderr(io.StringIO()):
                    _ = self.model.generate(
                        input=temp_path,
                        cache={},
                        language="auto",
                        use_itn=True,
                        batch_size_s=20,
                        merge_vad=True,
                        merge_length_s=5
                    )
            finally:
                # 清理临时文件
                import os
                try:
                    os.unlink(temp_path)
                except:
                    pass

        except Exception as e:
            self.logger.warning(f"模型预热失败: {e}")
            # 预热失败不影响正常功能
    
    def recognize(self, audio_data: np.ndarray, sample_rate: int = 16000) -> Dict[str, Any]:
        """识别音频"""
        if not self.is_loaded:
            return {"text": "", "confidence": 0.0, "error": "模型未加载"}
        
        try:
            start_time = time.time()
            
            # 确保音频格式正确
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
            
            # 确保音频是一维的
            if audio_data.ndim > 1:
                audio_data = audio_data.flatten()
            
            # 创建临时音频文件
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_path = temp_file.name
            
            try:
                # 保存音频到临时文件
                sf.write(temp_path, audio_data, sample_rate)
                
                # 使用FunASR标准pipeline进行识别
                # 重定向stdout和stderr以禁用rtf_avg输出
                import sys
                import io
                from contextlib import redirect_stdout, redirect_stderr

                with redirect_stdout(io.StringIO()), redirect_stderr(io.StringIO()):
                    result = self.model.generate(
                        input=temp_path,
                        cache={},
                        language="auto",  # 自动检测语言
                        use_itn=True,     # 使用逆文本标准化
                        batch_size_s=20,  # 减少批处理大小，提升响应速度
                        merge_vad=True,   # 合并VAD结果
                        merge_length_s=5  # 减少合并长度，提升速度
                    )
                
                # 解析结果
                recognition_time = time.time() - start_time
                
                if result and len(result) > 0:
                    # 使用FunASR标准后处理
                    raw_text = result[0].get("text", "") if isinstance(result[0], dict) else str(result[0])

                    # 自定义后处理，正确处理SenseVoice的输出格式
                    try:
                        print(f"🔍 原始识别结果: {raw_text}")

                        # 处理SenseVoice的特殊标记格式
                        processed_text = self._process_sensevoice_output(raw_text)

                        print(f"✅ 后处理结果: {processed_text}")
                    except Exception as e:
                        # 如果后处理失败，使用原始文本
                        print(f"❌ 后处理失败: {e}")
                        processed_text = raw_text

                    # 提取其他信息
                    confidence = 0.9  # FunASR通常不提供置信度，使用默认高置信度

                    return {
                        "text": processed_text.strip(),
                        "confidence": confidence,
                        "recognition_time": recognition_time,
                        "audio_duration": len(audio_data) / sample_rate,
                        "language": "auto",
                        "error": None
                    }
                else:
                    return {
                        "text": "",
                        "confidence": 0.0,
                        "recognition_time": recognition_time,
                        "audio_duration": len(audio_data) / sample_rate,
                        "language": "auto",
                        "error": "无识别结果"
                    }
                    
            finally:
                # 清理临时文件
                try:
                    Path(temp_path).unlink()
                except:
                    pass
                    
        except Exception as e:
            self.logger.error(f"语音识别失败: {e}")
            return {
                "text": "",
                "confidence": 0.0,
                "recognition_time": 0.0,
                "audio_duration": len(audio_data) / sample_rate if audio_data is not None else 0.0,
                "language": "auto",
                "error": str(e)
            }
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_path": self.model_path,
            "device": self.device,
            "is_loaded": self.is_loaded,
            "model_type": "SenseVoiceSmall",
            "supported_languages": ["中文", "英文", "日文", "韩文", "粤语"],
            "sample_rate": 16000,
            "max_duration": 30.0
        }


class ASRService(BaseService):
    """语音识别服务"""
    
    def __init__(self, config: Dict[str, Any], event_bus: EventBus):
        super().__init__("asr", config)
        self.event_bus = event_bus

        # 优化PyTorch线程配置，减少CPU竞争（必须在模型加载前设置）
        try:
            import torch
            torch.set_num_threads(2)  # 限制PyTorch线程数
            torch.set_num_interop_threads(1)  # 限制操作间并行度
        except Exception as e:
            self.logger.warning(f"PyTorch线程配置失败: {e}")

        # 配置参数
        self.enabled = config.get('enabled', True)
        self.model_name = config.get('model_name', 'models/funasr/iic/SenseVoiceSmall')  # 使用本地模型路径
        self.vad_model = config.get('vad_model', 'models/funasr/fsmn-vad')
        self.punc_model = config.get('punc_model', 'models/funasr/ct-punc')
        self.device = config.get('device', 'auto')
        self.sample_rate = config.get('sample_rate', 16000)
        self.max_audio_duration = config.get('max_audio_duration', 30.0)
        self.min_audio_duration = config.get('min_audio_duration', 1.0)
        self.auto_recognition = config.get('auto_recognition', False)
        self.recognition_interval = config.get('recognition_interval', 5.0)

        # 智能VAD配置
        self.use_smart_vad = config.get('use_smart_vad', True)  # 默认启用智能VAD
        self.waiting_timeout = config.get('waiting_timeout', 10.0)  # 等待语音超时
        self.speech_end_timeout = config.get('speech_end_timeout', 1.2)  # 语音结束确认超时

        # 核心组件 - 使用完整的FunASR pipeline
        self.processor = FunASRProcessor(
            model_path=self.model_name,
            device=self.device,
            vad_model=self.vad_model,
            punc_model=self.punc_model
        )
        self.audio_buffer = AudioBuffer(
            sample_rate=self.sample_rate,
            max_duration=self.max_audio_duration,
            min_duration=self.min_audio_duration
        )
        
        # 运行状态
        self.is_recognizing = False
        self.recognition_task = None

        # 语音收集状态
        self.is_collecting_speech = False
        self.speech_collection_start_time = None
        self.speech_collection_duration = 4.0  # 收集4秒语音
        self.speech_timeout = 10.0  # 10秒超时
        self.speech_collection_task = None

        # 静音检测 - 优化响应速度
        silence_config = SilenceConfig(
            energy_threshold=0.005,  # 降低能量阈值，更容易检测到语音
            zero_crossing_threshold=30,  # 降低零交叉率阈值
            silence_duration_threshold=0.8,  # 0.8秒静音后停止收集（大幅提升响应速度）
            speech_duration_threshold=0.2,   # 降低到0.2秒，更容易触发
            sample_rate=self.sample_rate
        )
        self.silence_detector = SilenceDetector(silence_config)
        self.silence_monitoring_task = None
        self.last_speech_activity = None

        # 性能统计
        self.recognition_count = 0
        self.total_recognition_time = 0.0
        self.total_audio_duration = 0.0
        self.last_recognition_time = None
        
        # 回调函数
        self.recognition_callbacks: List[Callable] = []

    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            if not self.enabled:
                self.logger.info("语音识别服务已禁用")
                return True

            # 加载ASR模型
            if not self.processor.load_model():
                self.logger.error("ASR模型加载失败")
                return False

            # 订阅音频数据事件
            await self.event_bus.subscribe(
                "audio_chunk",
                self._on_audio_chunk,
                priority=EventPriority.NORMAL
            )

            # 订阅唤醒词事件（触发识别）
            await self.event_bus.subscribe(
                "wake_word_detected",
                self._on_wake_word_detected,
                priority=EventPriority.HIGH
            )

            # 暂时禁用VAD长语音直接转写，专注于唤醒词触发模式
            # TODO: 后续可以实现智能音频分段功能

            self.logger.info(f"语音识别服务初始化完成 - 模型: {self.model_name}")
            return True

        except Exception as e:
            self.logger.error(f"语音识别服务初始化失败: {e}")
            return False

    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            if not self.enabled:
                return True

            # 启动自动识别任务（如果启用）
            if self.auto_recognition:
                self.is_recognizing = True
                self.recognition_task = asyncio.create_task(self._auto_recognition_loop())

            self.logger.info("语音识别服务已启动")
            return True

        except Exception as e:
            self.logger.error(f"语音识别服务启动失败: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            self.is_recognizing = False
            self.is_collecting_speech = False

            # 停止自动识别任务
            if self.recognition_task:
                self.recognition_task.cancel()
                try:
                    await self.recognition_task
                except asyncio.CancelledError:
                    pass
                self.recognition_task = None

            # 停止语音收集任务
            if self.speech_collection_task:
                self.speech_collection_task.cancel()
                try:
                    await self.speech_collection_task
                except asyncio.CancelledError:
                    pass
                self.speech_collection_task = None

            # 清空缓冲区
            self.audio_buffer.clear()

            self.logger.info("语音识别服务已停止")
            return True

        except Exception as e:
            self.logger.error(f"语音识别服务停止失败: {e}")
            return False

    async def _health_check_impl(self) -> bool:
        """健康检查实现"""
        try:
            if not self.enabled:
                return True

            # 检查模型是否加载
            if not self.processor.is_loaded:
                self.logger.warning("ASR模型未加载")
                return False

            # 检查自动识别任务是否运行（如果启用）
            if self.auto_recognition and self.is_recognizing:
                if not self.recognition_task or self.recognition_task.done():
                    self.logger.warning("自动识别任务未运行")
                    return False

            return True

        except Exception as e:
            self.logger.error(f"语音识别服务健康检查失败: {e}")
            return False

    async def _on_audio_chunk(self, event_data: Dict[str, Any]) -> None:
        """处理音频数据事件"""
        try:
            if not self.enabled:
                return

            audio_chunk = event_data.get('audio_data')
            if audio_chunk is None:
                return

            # 如果正在收集语音，进行静音检测
            if self.is_collecting_speech:
                # 添加音频到缓冲区
                self.audio_buffer.add_audio(audio_chunk)

                # 进行静音检测
                vad_result = self.silence_detector.analyze_audio_chunk(audio_chunk)

                # 添加调试信息
                if hasattr(self, '_vad_debug_count'):
                    self._vad_debug_count += 1
                else:
                    self._vad_debug_count = 1

                # 语音活动调试信息已关闭

                # 更新最后语音活动时间
                if vad_result.is_speech:
                    self.last_speech_activity = time.time()

                # 发布语音活动事件
                await self.event_bus.emit(
                    "speech_activity",
                    {
                        "is_speech": vad_result.is_speech,
                        "energy": vad_result.energy,
                        "confidence": vad_result.confidence,
                        "silence_duration": self.silence_detector.get_silence_duration(),
                        "speech_duration": self.silence_detector.get_speech_duration(),
                        "timestamp": vad_result.timestamp
                    }
                )

        except Exception as e:
            self.logger.error(f"处理音频数据失败: {e}")

    async def _on_wake_word_detected(self, event_data: Dict[str, Any]) -> None:
        """处理唤醒词检测事件"""
        try:
            if not self.enabled:
                return

            confidence = event_data.get('confidence', 0.0)

            print(f"🎯 检测到唤醒词! 置信度: {confidence:.4f}")

            # 启动智能语音收集
            await self.start_smart_speech_collection()

        except Exception as e:
            self.logger.error(f"处理唤醒词事件失败: {e}")

    async def start_smart_speech_collection(self):
        """启动智能语音收集（基于VAD）"""
        try:
            print("🎤 启动智能VAD语音收集...")

            # 导入唤醒VAD处理器
            from ..core.wake_word_vad_handler import WakeWordVADHandler, WakeVADState

            # 创建VAD处理器
            vad_handler = WakeWordVADHandler(
                waiting_timeout=self.waiting_timeout,      # 使用配置的等待超时
                speech_end_timeout=self.speech_end_timeout, # 使用配置的语音结束确认超时
                sample_rate=self.sample_rate
            )

            # 设置回调
            vad_handler.on_speech_complete = self._on_vad_speech_complete
            vad_handler.on_timeout = self._on_vad_timeout

            # 清空音频缓冲区（移除包含唤醒词的音频）
            self.audio_buffer.clear()
            print("🧹 音频缓冲区已清空")

            # 启动VAD处理
            print("🔧 正在启动VAD处理器...")
            if await vad_handler.start_after_wake_word():
                self.current_vad_handler = vad_handler
                self.is_collecting_speech = True

                # 启动音频转发任务
                self.vad_audio_task = asyncio.create_task(self._forward_audio_to_vad())
                print("✅ 智能VAD语音收集启动成功")
            else:
                print("❌ VAD处理器启动失败，回退到传统模式")
                await self._start_legacy_speech_collection()

        except Exception as e:
            self.logger.error(f"启动智能语音收集失败: {e}")
            print(f"❌ 启动智能语音收集失败: {e}")
            import traceback
            traceback.print_exc()
            # 回退到传统模式
            print("🔄 回退到传统模式...")
            await self._start_legacy_speech_collection()

    async def _forward_audio_to_vad(self):
        """将音频数据转发给VAD处理器"""
        try:
            print("🔄 开始音频转发给VAD处理器...")
            audio_chunks_sent = 0

            while self.is_collecting_speech and hasattr(self, 'current_vad_handler'):
                # 获取最新的音频数据
                buffer_info = self.audio_buffer.get_buffer_info()
                if buffer_info['buffer_size'] > 0:
                    # 获取最近的音频块
                    recent_audio = self.audio_buffer.get_recent_audio(duration=0.5)
                    if recent_audio is not None and len(recent_audio) > 0:
                        await self.current_vad_handler.add_audio_chunk(recent_audio)
                        audio_chunks_sent += 1

                        if audio_chunks_sent % 10 == 1:  # 每10个块显示一次
                            print(f"📤 已发送 {audio_chunks_sent} 个音频块给VAD处理器")

                await asyncio.sleep(0.1)

            print(f"🔄 音频转发结束，共发送 {audio_chunks_sent} 个音频块")

        except asyncio.CancelledError:
            print("🔄 音频转发被取消")
        except Exception as e:
            self.logger.error(f"音频转发错误: {e}")
            print(f"❌ 音频转发错误: {e}")
            import traceback
            traceback.print_exc()

    async def _on_vad_speech_complete(self):
        """VAD检测到语音完成"""
        try:
            print(f"✅ 语音收集完成，开始转写...")

            # 停止收集
            self.is_collecting_speech = False

            # 停止音频转发任务
            if hasattr(self, 'vad_audio_task') and self.vad_audio_task:
                self.vad_audio_task.cancel()
                try:
                    await self.vad_audio_task
                except asyncio.CancelledError:
                    pass

            # 清理VAD处理器
            if hasattr(self, 'current_vad_handler'):
                await self.current_vad_handler.stop()
                delattr(self, 'current_vad_handler')

            # 发送语音收集完成事件
            await self.event_bus.emit(
                "speech_collection_complete",
                {
                    "timestamp": time.time(),
                    "using_smart_vad": True
                },
                priority=EventPriority.HIGH
            )

            # 开始识别
            result = await self.recognize_buffered_audio()
            if not result:
                print(f"❌ 识别失败或无结果")

        except Exception as e:
            self.logger.error(f"处理VAD语音完成失败: {e}")

    async def _on_vad_timeout(self):
        """VAD超时处理"""
        try:
            print(f"🔇 检测到静默或无效输入")

            # 停止收集
            self.is_collecting_speech = False

            # 停止音频转发任务
            if hasattr(self, 'vad_audio_task') and self.vad_audio_task:
                self.vad_audio_task.cancel()
                try:
                    await self.vad_audio_task
                except asyncio.CancelledError:
                    pass

            # 清理VAD处理器
            if hasattr(self, 'current_vad_handler'):
                await self.current_vad_handler.stop()
                delattr(self, 'current_vad_handler')

            # 发布空识别结果
            await self.event_bus.emit(
                "speech_recognized",
                {
                    "text": "",
                    "confidence": 0.0,
                    "error": "等待语音超时",
                    "timestamp": time.time(),
                    "timeout": True
                }
            )

        except Exception as e:
            self.logger.error(f"处理VAD超时失败: {e}")

    async def _speech_collection_handler(self):
        """语音收集处理器 - 基于静音检测的智能收集"""
        try:
            # 启动静音监控
            self.silence_monitoring_task = asyncio.create_task(self._silence_monitoring_loop())

            # 等待静音监控完成或超时
            try:
                await asyncio.wait_for(self.silence_monitoring_task, timeout=30.0)  # 最大30秒超时
            except asyncio.TimeoutError:
                print(f"⏰ ASR服务：语音收集超时（30秒）")

            # 无论如何都要检查是否有音频需要识别
            # 停止收集
            self.is_collecting_speech = False

            # 发送语音收集完成事件，通知状态机转换状态
            print(f"✅ ASR服务：语音收集完成，开始转写...")
            await self.event_bus.emit(
                "speech_collection_complete",
                {
                    "timestamp": time.time(),
                    "collection_duration": time.time() - self.speech_collection_start_time if self.speech_collection_start_time else 0
                },
                priority=EventPriority.HIGH
            )

            # 检查是否有足够的音频
            buffer_info = self.audio_buffer.get_buffer_info()
            if buffer_info['buffer_size'] > 0:
                # 开始识别
                result = await self.recognize_buffered_audio()
                if result:
                    # 只显示识别结果，不显示调试信息
                    pass
                else:
                    print(f"❌ ASR服务：识别失败或无结果")
            else:
                print(f"❌ ASR服务：未检测到语音输入")
                # 发布空识别结果
                await self.event_bus.emit(
                    "speech_recognized",
                    {
                        "text": "",
                        "confidence": 0.0,
                        "error": "未检测到语音输入",
                        "timestamp": time.time()
                    }
                )

        except asyncio.CancelledError:
            # 任务被取消，停止收集
            self.is_collecting_speech = False
        except Exception as e:
            self.logger.error(f"语音收集处理失败: {e}")
            self.is_collecting_speech = False

    async def _silence_monitoring_loop(self):
        """静音监控循环"""
        try:
            print(f"🔍 ASR服务：开始静音监控...")

            # 等待初始语音检测
            initial_speech_detected = False
            start_time = time.time()

            while self.is_collecting_speech:
                # 检查是否检测到初始语音
                if not initial_speech_detected:
                    speech_detected = self.silence_detector.is_speech_detected()
                    elapsed_time = time.time() - start_time

                    # 等待状态调试信息已关闭

                    if speech_detected:
                        initial_speech_detected = True
                        print(f"🎤 ASR服务：检测到语音开始")
                        self.last_speech_activity = time.time()
                    elif elapsed_time > 5.0:  # 5秒内没有语音，退出（减少等待时间）
                        print(f"⏰ ASR服务：5秒内未检测到语音，停止收集")
                        break
                else:
                    # 已检测到语音，监控静音
                    silence_duration = self.silence_detector.get_silence_duration()

                    if self.silence_detector.is_long_silence():
                        print(f"🔇 ASR服务：检测到{silence_duration:.1f}秒静音，停止收集")
                        break

                    # 如果有语音活动，更新时间
                    if self.silence_detector.current_state == "speech":
                        self.last_speech_activity = time.time()

                # 短暂等待
                await asyncio.sleep(0.1)

            # 停止收集
            self.is_collecting_speech = False

        except asyncio.CancelledError:
            print(f"🛑 ASR服务：静音监控被取消")
            self.is_collecting_speech = False
        except Exception as e:
            self.logger.error(f"静音监控失败: {e}")
            self.is_collecting_speech = False

    async def start_speech_collection(self):
        """由状态机控制的语音收集开始（智能VAD模式）"""
        try:
            if not self.enabled:
                return

            # 优先使用智能VAD处理器
            use_smart_vad = getattr(self, 'use_smart_vad', True)

            if use_smart_vad:
                print(f"🎤 启动智能VAD语音收集...")
                await self.start_smart_speech_collection()
            else:
                # 回退到传统模式
                print(f"🎤 启动传统静音监控...")
                await self._start_legacy_speech_collection()

        except Exception as e:
            self.logger.error(f"启动语音收集失败: {e}")
            # 如果智能VAD失败，回退到传统模式
            if hasattr(self, 'use_smart_vad') and self.use_smart_vad:
                print("⚠️  智能VAD失败，回退到传统模式")
                await self._start_legacy_speech_collection()

    async def _start_legacy_speech_collection(self):
        """启动传统语音收集（备用方法）"""
        try:
            print(f"🎤 请说出您的指令...")

            # 清空音频缓冲区（移除包含唤醒词的音频）
            self.audio_buffer.clear()

            # 重置静音检测器
            self.silence_detector.reset()
            self.last_speech_activity = None

            # 开始收集新的语音
            self.is_collecting_speech = True
            self.speech_collection_start_time = time.time()

            # 取消之前的收集任务
            if self.speech_collection_task:
                self.speech_collection_task.cancel()

            # 启动语音收集任务
            self.speech_collection_task = asyncio.create_task(self._speech_collection_handler())

        except Exception as e:
            self.logger.error(f"启动传统语音收集失败: {e}")

    async def stop_speech_collection(self):
        """停止语音收集"""
        try:
            self.is_collecting_speech = False

            # 取消静音监控任务
            if self.silence_monitoring_task:
                self.silence_monitoring_task.cancel()
                try:
                    await self.silence_monitoring_task
                except asyncio.CancelledError:
                    pass
                self.silence_monitoring_task = None

            # 取消语音收集任务
            if self.speech_collection_task:
                self.speech_collection_task.cancel()
                try:
                    await self.speech_collection_task
                except asyncio.CancelledError:
                    pass
                self.speech_collection_task = None

        except Exception as e:
            self.logger.error(f"停止语音收集失败: {e}")

    async def _auto_recognition_loop(self) -> None:
        """自动识别循环"""
        try:
            while self.is_recognizing:
                # 检查缓冲区是否有足够的音频
                buffer_info = self.audio_buffer.get_buffer_info()

                if buffer_info['is_ready']:
                    await self.recognize_buffered_audio()

                # 等待下一次检查
                await asyncio.sleep(self.recognition_interval)

        except asyncio.CancelledError:
            self.logger.info("自动识别循环被取消")
        except Exception as e:
            self.logger.error(f"自动识别循环异常: {e}")

    async def recognize_buffered_audio(self) -> Optional[Dict[str, Any]]:
        """识别缓冲区中的音频"""
        try:
            # 获取音频数据
            audio_data = self.audio_buffer.get_audio_for_recognition(clear_after=True)

            if audio_data is None:
                print(f"❌ ASR服务：缓冲区音频不足，跳过识别")
                return None

            # 音频数据调试信息已关闭

            # 执行识别
            result = await self._recognize_audio(audio_data)

            return result

        except Exception as e:
            self.logger.error(f"识别缓冲音频失败: {e}")
            return None

    async def recognize_audio(self, audio_data: np.ndarray) -> Dict[str, Any]:
        """识别指定的音频数据"""
        try:
            return await self._recognize_audio(audio_data)
        except Exception as e:
            self.logger.error(f"识别音频失败: {e}")
            return {
                "text": "",
                "confidence": 0.0,
                "error": str(e)
            }

    async def _recognize_audio(self, audio_data: np.ndarray) -> Dict[str, Any]:
        """内部识别方法"""
        try:
            # 在线程池中执行识别（避免阻塞事件循环）
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self.processor.recognize,
                audio_data,
                self.sample_rate
            )

            # 更新统计
            self.recognition_count += 1
            self.total_recognition_time += result.get('recognition_time', 0.0)
            self.total_audio_duration += result.get('audio_duration', 0.0)
            self.last_recognition_time = time.time()

            # 记录日志
            if result.get('error'):
                self.logger.warning(f"识别失败: {result['error']}")
            else:
                text = result.get('text', '')
                confidence = result.get('confidence', 0.0)
                recognition_time = result.get('recognition_time', 0.0)

                print(f"✅ ASR服务：转写完成 - 文本: '{text}', 置信度: {confidence:.3f}")
                self.logger.info(
                    f"识别完成 - 文本: '{text}', "
                    f"置信度: {confidence:.3f}, "
                    f"耗时: {recognition_time*1000:.1f}ms"
                )

            # 发布识别结果事件
            await self.event_bus.emit(
                "speech_recognized",
                {
                    "text": result.get('text', ''),
                    "confidence": result.get('confidence', 0.0),
                    "recognition_time": result.get('recognition_time', 0.0),
                    "audio_duration": result.get('audio_duration', 0.0),
                    "language": result.get('language', 'auto'),
                    "timestamp": time.time(),
                    "error": result.get('error')
                },
                priority=EventPriority.HIGH
            )

            # 调用回调函数
            for callback in self.recognition_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(result)
                    else:
                        callback(result)
                except Exception as e:
                    self.logger.error(f"识别回调函数执行失败: {e}")

            return result

        except Exception as e:
            self.logger.error(f"内部识别失败: {e}")
            return {
                "text": "",
                "confidence": 0.0,
                "recognition_time": 0.0,
                "audio_duration": 0.0,
                "language": "auto",
                "error": str(e)
            }

    def add_recognition_callback(self, callback: Callable) -> None:
        """添加识别回调函数"""
        self.recognition_callbacks.append(callback)

    def remove_recognition_callback(self, callback: Callable) -> None:
        """移除识别回调函数"""
        if callback in self.recognition_callbacks:
            self.recognition_callbacks.remove(callback)

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        avg_recognition_time = (
            self.total_recognition_time / self.recognition_count
            if self.recognition_count > 0 else 0.0
        )

        avg_audio_duration = (
            self.total_audio_duration / self.recognition_count
            if self.recognition_count > 0 else 0.0
        )

        return {
            "enabled": self.enabled,
            "is_recognizing": self.is_recognizing,
            "auto_recognition": self.auto_recognition,
            "recognition_count": self.recognition_count,
            "avg_recognition_time": avg_recognition_time,
            "avg_audio_duration": avg_audio_duration,
            "total_recognition_time": self.total_recognition_time,
            "total_audio_duration": self.total_audio_duration,
            "last_recognition_time": self.last_recognition_time,
            "model_info": self.processor.get_model_info(),
            "buffer_info": self.audio_buffer.get_buffer_info()
        }

    async def test_recognition(self, audio_data: np.ndarray) -> Dict[str, Any]:
        """测试识别功能"""
        if not self.processor.is_loaded:
            raise RuntimeError("ASR模型未加载")

        return await self.recognize_audio(audio_data)

    def clear_audio_buffer(self) -> None:
        """清空音频缓冲区"""
        self.audio_buffer.clear()

    # VAD长语音直接转写功能已禁用，专注于唤醒词触发模式
        self.logger.info("音频缓冲区已清空")

    def get_buffer_status(self) -> Dict[str, Any]:
        """获取缓冲区状态"""
        return self.audio_buffer.get_buffer_info()
