#!/usr/bin/env python3
"""
基础对话管理服务
实现对话上下文管理、会话状态跟踪和简单的打断检测
"""

import asyncio
import time
import logging
import re
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, field
from enum import Enum
import uuid

from .base_service import BaseService, ServiceStatus
from .event_bus import EventBus, EventPriority
from .llm import DifyLLMService
from ..interfaces.llm_interface import LLMRequest, LLMMessage


class ConversationState(Enum):
    """对话状态"""
    IDLE = "idle"                    # 空闲状态
    LISTENING = "listening"          # 监听用户输入
    PROCESSING = "processing"        # 处理用户输入
    RESPONDING = "responding"        # 系统响应中
    WAITING_FOLLOWUP = "waiting"     # 等待后续输入


class MessageType(Enum):
    """消息类型"""
    USER = "user"                    # 用户消息
    ASSISTANT = "assistant"          # 助手消息
    SYSTEM = "system"                # 系统消息
    ERROR = "error"                  # 错误消息


@dataclass
class ConversationMessage:
    """对话消息"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    type: MessageType = MessageType.USER
    content: str = ""
    timestamp: float = field(default_factory=time.time)
    confidence: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ConversationContext:
    """对话上下文"""
    session_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    user_id: Optional[str] = None
    start_time: float = field(default_factory=time.time)
    last_activity_time: float = field(default_factory=time.time)
    state: ConversationState = ConversationState.IDLE
    messages: List[ConversationMessage] = field(default_factory=list)
    context_data: Dict[str, Any] = field(default_factory=dict)
    turn_count: int = 0

    # 增强的上下文记忆
    topic_history: List[str] = field(default_factory=list)  # 话题历史
    entity_memory: Dict[str, Any] = field(default_factory=dict)  # 实体记忆
    user_preferences: Dict[str, Any] = field(default_factory=dict)  # 用户偏好
    conversation_summary: str = ""  # 对话摘要
    intent_history: List[str] = field(default_factory=list)  # 意图历史
    emotional_state: str = "neutral"  # 情感状态
    context_keywords: List[str] = field(default_factory=list)  # 上下文关键词
    
    def add_message(self, message: ConversationMessage):
        """添加消息到对话历史"""
        self.messages.append(message)
        self.last_activity_time = time.time()
        if message.type == MessageType.USER:
            self.turn_count += 1
            # 提取和更新上下文信息
            self._update_context_from_message(message)

    def get_recent_messages(self, count: int = 5) -> List[ConversationMessage]:
        """获取最近的消息"""
        return self.messages[-count:] if self.messages else []

    def get_conversation_duration(self) -> float:
        """获取对话持续时间"""
        return time.time() - self.start_time

    def get_idle_duration(self) -> float:
        """获取空闲时间"""
        return time.time() - self.last_activity_time

    def _update_context_from_message(self, message: ConversationMessage):
        """从消息中更新上下文信息"""
        if message.type == MessageType.USER:
            # 提取关键词
            keywords = self._extract_keywords(message.content)
            self.context_keywords.extend(keywords)
            # 保持关键词列表不超过20个
            self.context_keywords = list(set(self.context_keywords))[-20:]

            # 检测意图
            intent = self._detect_intent(message.content)
            if intent:
                self.intent_history.append(intent)
                self.intent_history = self.intent_history[-10:]  # 保持最近10个意图

    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词（简单实现）"""
        import re
        # 移除标点符号，分词
        words = re.findall(r'\b\w+\b', text.lower())
        # 过滤停用词（简化版）
        stop_words = {'的', '了', '是', '在', '我', '你', '他', '她', '它', '这', '那', '和', '与', '或'}
        keywords = [word for word in words if word not in stop_words and len(word) > 1]
        return keywords[:5]  # 返回前5个关键词

    def _detect_intent(self, text: str) -> Optional[str]:
        """检测用户意图（简单实现）"""
        text_lower = text.lower()
        if any(word in text_lower for word in ['天气', '温度', '下雨', '晴天']):
            return 'weather_query'
        elif any(word in text_lower for word in ['时间', '几点', '现在']):
            return 'time_query'
        elif any(word in text_lower for word in ['你是谁', '介绍', '自己']):
            return 'self_introduction'
        elif any(word in text_lower for word in ['帮助', '怎么', '如何']):
            return 'help_request'
        elif any(word in text_lower for word in ['谢谢', '感谢', '再见', '拜拜']):
            return 'farewell'
        return 'general_chat'

    def get_context_summary(self) -> str:
        """获取对话上下文摘要"""
        summary_parts = []

        if self.topic_history:
            summary_parts.append(f"讨论话题: {', '.join(self.topic_history[-3:])}")

        if self.context_keywords:
            summary_parts.append(f"关键词: {', '.join(self.context_keywords[-5:])}")

        if self.intent_history:
            recent_intents = list(set(self.intent_history[-3:]))
            summary_parts.append(f"最近意图: {', '.join(recent_intents)}")

        if self.user_preferences:
            prefs = [f"{k}: {v}" for k, v in list(self.user_preferences.items())[:2]]
            if prefs:
                summary_parts.append(f"用户偏好: {', '.join(prefs)}")

        return " | ".join(summary_parts) if summary_parts else "新对话"


class ConversationService(BaseService):
    """基础对话管理服务"""
    
    def __init__(self, config: Dict[str, Any], event_bus: EventBus, llm_service: DifyLLMService):
        super().__init__("conversation_service", config)
        self.event_bus = event_bus
        self.llm_service = llm_service
        
        # 配置参数
        self.max_context_length = config.get('max_context_length', 10)  # 最大上下文长度
        self.session_timeout = config.get('session_timeout', 300.0)  # 会话超时时间（5分钟）
        self.max_turn_count = config.get('max_turn_count', 50)  # 最大对话轮数
        self.enable_interruption = config.get('enable_interruption', True)  # 是否启用打断检测
        self.interruption_threshold = config.get('interruption_threshold', 0.5)  # 打断检测阈值

        # 增强的配置参数
        self.enable_context_compression = config.get('enable_context_compression', True)  # 启用上下文压缩
        self.context_compression_threshold = config.get('context_compression_threshold', 15)  # 压缩阈值
        self.enable_intent_detection = config.get('enable_intent_detection', True)  # 启用意图检测
        self.enable_emotional_analysis = config.get('enable_emotional_analysis', False)  # 启用情感分析
        self.enable_personalization = config.get('enable_personalization', True)  # 启用个性化
        self.conversation_quality_threshold = config.get('conversation_quality_threshold', 0.7)  # 对话质量阈值
        
        # 对话管理
        self.current_context: Optional[ConversationContext] = None
        self.conversation_history: Dict[str, ConversationContext] = {}
        
        # 状态管理
        self.is_processing = False
        self.is_responding = False
        self.current_response_task: Optional[asyncio.Task] = None
        
        # 增强的打断检测
        self.interruption_callbacks: List[Callable] = []
        self.last_speech_activity_time = 0.0
        self.interruption_count = 0  # 打断次数统计
        self.interruption_sensitivity = config.get('interruption_sensitivity', 'medium')  # 打断敏感度
        self.adaptive_interruption = config.get('adaptive_interruption', True)  # 自适应打断
        self.interruption_history: List[Dict[str, Any]] = []  # 打断历史
        
        # 统计信息
        self.total_conversations = 0
        self.total_messages = 0
        self.average_conversation_length = 0.0

        # 会话持久化
        self.enable_persistence = config.get('enable_persistence', False)
        self.persistence_path = config.get('persistence_path', 'data/conversations')
        self.auto_save_interval = config.get('auto_save_interval', 60.0)  # 自动保存间隔（秒）
        self.last_save_time = time.time()
        
        # 定时器
        self.cleanup_task: Optional[asyncio.Task] = None
        
    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            # 订阅相关事件
            await self.event_bus.subscribe("speech_recognized", self._on_speech_recognized)
            await self.event_bus.subscribe("wake_word_detected", self._on_wake_word_detected)
            await self.event_bus.subscribe("state_changed", self._on_state_changed)
            await self.event_bus.subscribe("speech_activity_detected", self._on_speech_activity)
            await self.event_bus.subscribe("tts_started", self._on_tts_started)
            await self.event_bus.subscribe("tts_finished", self._on_tts_finished)
            
            self.logger.info("对话管理服务初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"对话管理服务初始化失败: {e}")
            return False
    
    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            # 启动清理任务
            self.cleanup_task = asyncio.create_task(self._cleanup_expired_sessions())
            
            self.logger.info("对话管理服务已启动")
            return True
            
        except Exception as e:
            self.logger.error(f"对话管理服务启动失败: {e}")
            return False
    
    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            # 停止清理任务
            if self.cleanup_task:
                self.cleanup_task.cancel()
                try:
                    await self.cleanup_task
                except asyncio.CancelledError:
                    pass
                self.cleanup_task = None
            
            # 停止当前响应任务
            if self.current_response_task:
                self.current_response_task.cancel()
                try:
                    await self.current_response_task
                except asyncio.CancelledError:
                    pass
                self.current_response_task = None
            
            # 保存当前对话状态
            if self.current_context:
                await self._save_conversation_context()
            
            self.logger.info("对话管理服务已停止")
            return True
            
        except Exception as e:
            self.logger.error(f"对话管理服务停止失败: {e}")
            return False
    
    async def _health_check_impl(self) -> bool:
        """健康检查实现"""
        try:
            # 检查是否有长时间运行的任务
            if self.current_response_task and not self.current_response_task.done():
                # 检查任务运行时间
                if hasattr(self.current_response_task, '_start_time'):
                    runtime = time.time() - self.current_response_task._start_time
                    if runtime > 30.0:  # 超过30秒认为异常
                        self.logger.warning(f"响应任务运行时间过长: {runtime:.1f}s")
                        return False
            
            # 检查会话状态
            if self.current_context:
                idle_duration = self.current_context.get_idle_duration()
                if idle_duration > self.session_timeout * 2:  # 超过2倍超时时间
                    self.logger.warning(f"会话空闲时间过长: {idle_duration:.1f}s")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"对话管理服务健康检查失败: {e}")
            return False
    
    # 事件处理方法
    async def _on_wake_word_detected(self, event_data: Dict[str, Any]):
        """处理唤醒词检测事件"""
        try:
            # 开始新的对话会话
            await self.start_new_conversation()
            
        except Exception as e:
            self.logger.error(f"处理唤醒词事件失败: {e}")
    
    async def _on_speech_recognized(self, event_data: Dict[str, Any]):
        """处理语音识别事件"""
        try:
            text = event_data.get('text', '').strip()
            confidence = event_data.get('confidence', 0.0)
            error = event_data.get('error')
            
            if error:
                # 处理识别错误
                await self._handle_recognition_error(error)
                return
            
            if not text:
                # 空文本，可能是静默或噪音
                await self._handle_empty_input()
                return
            
            # 处理有效的用户输入
            await self._handle_user_input(text, confidence, event_data)
            
        except Exception as e:
            self.logger.error(f"处理语音识别事件失败: {e}")
    
    async def _on_state_changed(self, event_data: Dict[str, Any]):
        """处理状态变化事件"""
        try:
            current_state = event_data.get('current_state')
            previous_state = event_data.get('previous_state')
            
            # 根据状态变化更新对话状态
            if current_state == 'speech_collecting':
                if self.current_context:
                    self.current_context.state = ConversationState.LISTENING
            elif current_state == 'speech_processing':
                if self.current_context:
                    self.current_context.state = ConversationState.PROCESSING
                    self.is_processing = True
            elif current_state == 'active_conv':
                if self.current_context:
                    self.current_context.state = ConversationState.WAITING_FOLLOWUP
                    self.is_processing = False
            elif current_state == 'wake_listening':
                # 回到监听唤醒词状态，结束当前对话
                await self._end_current_conversation()
                
        except Exception as e:
            self.logger.error(f"处理状态变化事件失败: {e}")
    
    async def _on_speech_activity(self, event_data: Dict[str, Any]):
        """处理语音活动检测事件"""
        try:
            self.last_speech_activity_time = time.time()
            
            # 检查是否需要打断当前响应
            if self.enable_interruption and self.is_responding:
                await self._handle_interruption(event_data)
                
        except Exception as e:
            self.logger.error(f"处理语音活动事件失败: {e}")
    
    async def _on_tts_started(self, event_data: Dict[str, Any]):
        """处理TTS开始事件"""
        try:
            self.is_responding = True
            if self.current_context:
                self.current_context.state = ConversationState.RESPONDING
                
        except Exception as e:
            self.logger.error(f"处理TTS开始事件失败: {e}")
    
    async def _on_tts_finished(self, event_data: Dict[str, Any]):
        """处理TTS完成事件"""
        try:
            self.is_responding = False
            if self.current_context:
                self.current_context.state = ConversationState.WAITING_FOLLOWUP
                
        except Exception as e:
            self.logger.error(f"处理TTS完成事件失败: {e}")

    # 对话管理核心方法
    async def start_new_conversation(self, user_id: Optional[str] = None) -> str:
        """开始新的对话会话"""
        try:
            # 结束当前对话（如果存在）
            if self.current_context:
                await self._save_conversation_context()

            # 创建新的对话上下文
            self.current_context = ConversationContext(user_id=user_id)

            # 添加系统欢迎消息
            welcome_message = ConversationMessage(
                type=MessageType.SYSTEM,
                content="对话开始",
                metadata={"event": "conversation_start"}
            )
            self.current_context.add_message(welcome_message)

            # 更新统计信息
            self.total_conversations += 1

            # 发布对话开始事件
            await self.event_bus.emit(
                "conversation_started",
                {
                    "session_id": self.current_context.session_id,
                    "user_id": user_id,
                    "timestamp": time.time()
                },
                priority=EventPriority.HIGH
            )

            print(f"💬 开始新对话 (会话ID: {self.current_context.session_id[:8]}...)")
            self.logger.info(f"开始新对话会话: {self.current_context.session_id}")

            return self.current_context.session_id

        except Exception as e:
            self.logger.error(f"开始新对话失败: {e}")
            raise

    async def _handle_user_input(self, text: str, confidence: float, metadata: Dict[str, Any]):
        """处理用户输入"""
        try:
            if not self.current_context:
                # 如果没有当前对话，开始新对话
                await self.start_new_conversation()

            # 清理和验证输入文本
            cleaned_text = self._clean_input_text(text)
            if not cleaned_text:
                await self._handle_empty_input()
                return

            # 创建用户消息
            user_message = ConversationMessage(
                type=MessageType.USER,
                content=cleaned_text,
                confidence=confidence,
                metadata=metadata
            )

            # 添加到对话历史
            self.current_context.add_message(user_message)
            self.total_messages += 1

            # 检查对话长度限制
            if len(self.current_context.messages) > self.max_context_length:
                await self._trim_conversation_history()

            # 检查对话轮数限制
            if self.current_context.turn_count > self.max_turn_count:
                await self._handle_max_turns_reached()
                return

            print(f"👤 用户: {cleaned_text} (置信度: {confidence:.2f})")

            # 发布用户输入事件
            await self.event_bus.emit(
                "user_input_received",
                {
                    "session_id": self.current_context.session_id,
                    "text": cleaned_text,
                    "confidence": confidence,
                    "turn_count": self.current_context.turn_count,
                    "timestamp": time.time()
                },
                priority=EventPriority.HIGH
            )

            # 调用LLM服务生成响应
            await self._generate_llm_response(cleaned_text)

        except Exception as e:
            self.logger.error(f"处理用户输入失败: {e}")

    async def _handle_recognition_error(self, error: str):
        """处理识别错误"""
        try:
            if not self.current_context:
                return

            # 创建错误消息
            error_message = ConversationMessage(
                type=MessageType.ERROR,
                content=f"识别错误: {error}",
                metadata={"error_type": "speech_recognition"}
            )

            self.current_context.add_message(error_message)

            print(f"❌ 语音识别错误: {error}")

            # 发布错误事件
            await self.event_bus.emit(
                "conversation_error",
                {
                    "session_id": self.current_context.session_id,
                    "error": error,
                    "error_type": "speech_recognition",
                    "timestamp": time.time()
                }
            )

        except Exception as e:
            self.logger.error(f"处理识别错误失败: {e}")

    async def _handle_empty_input(self):
        """处理空输入"""
        try:
            if not self.current_context:
                return

            print("🔇 检测到静默或无效输入")

            # 发布空输入事件
            await self.event_bus.emit(
                "empty_input_detected",
                {
                    "session_id": self.current_context.session_id,
                    "timestamp": time.time()
                }
            )

        except Exception as e:
            self.logger.error(f"处理空输入失败: {e}")

    async def _handle_interruption(self, event_data: Dict[str, Any]):
        """增强的打断检测处理"""
        try:
            if not self.current_context or not self.is_responding:
                return

            # 获取打断信息
            activity_level = event_data.get('activity_level', 0.0)
            speech_duration = event_data.get('speech_duration', 0.0)
            confidence = event_data.get('confidence', 0.0)

            # 自适应打断阈值
            adaptive_threshold = self._calculate_adaptive_threshold()

            # 检查打断条件
            if not self._should_interrupt(activity_level, speech_duration, confidence, adaptive_threshold):
                return

            print(f"⚡ 检测到用户打断 (活动度: {activity_level:.2f}, 阈值: {adaptive_threshold:.2f})")

            # 记录打断历史
            interruption_record = {
                "timestamp": time.time(),
                "activity_level": activity_level,
                "speech_duration": speech_duration,
                "confidence": confidence,
                "threshold_used": adaptive_threshold,
                "context": self.current_context.state.value
            }
            self.interruption_history.append(interruption_record)
            self.interruption_count += 1

            # 保持打断历史不超过50条
            if len(self.interruption_history) > 50:
                self.interruption_history = self.interruption_history[-50:]

            # 停止当前响应
            if self.current_response_task:
                self.current_response_task.cancel()

            # 创建增强的打断消息
            interruption_message = ConversationMessage(
                type=MessageType.SYSTEM,
                content=f"用户打断 (第{self.interruption_count}次)",
                metadata={
                    "event": "interruption",
                    "activity_level": activity_level,
                    "speech_duration": speech_duration,
                    "confidence": confidence,
                    "interruption_count": self.interruption_count
                }
            )

            self.current_context.add_message(interruption_message)

            # 发布增强的打断事件
            await self.event_bus.emit(
                "user_interruption",
                {
                    "session_id": self.current_context.session_id,
                    "activity_level": activity_level,
                    "speech_duration": speech_duration,
                    "confidence": confidence,
                    "interruption_count": self.interruption_count,
                    "timestamp": time.time()
                },
                priority=EventPriority.CRITICAL
            )

            # 通知打断回调
            for callback in self.interruption_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(self.current_context, event_data)
                    else:
                        callback(self.current_context, event_data)
                except Exception as e:
                    self.logger.error(f"打断回调执行失败: {e}")

        except Exception as e:
            self.logger.error(f"处理打断检测失败: {e}")

    def _calculate_adaptive_threshold(self) -> float:
        """计算自适应打断阈值"""
        if not self.adaptive_interruption or len(self.interruption_history) < 3:
            return self.interruption_threshold

        # 分析最近的打断模式
        recent_interruptions = self.interruption_history[-10:]

        # 如果最近打断频繁，提高阈值
        recent_count = len([i for i in recent_interruptions
                           if time.time() - i["timestamp"] < 60])  # 最近1分钟

        if recent_count > 3:
            # 频繁打断，提高阈值
            return min(self.interruption_threshold * 1.3, 0.9)
        elif recent_count == 0:
            # 很少打断，降低阈值
            return max(self.interruption_threshold * 0.8, 0.2)

        return self.interruption_threshold

    def _should_interrupt(self, activity_level: float, speech_duration: float,
                         confidence: float, threshold: float) -> bool:
        """判断是否应该打断"""
        # 基础活动度检查
        if activity_level < threshold:
            return False

        # 语音持续时间检查（避免误触发）
        if speech_duration < 0.3:  # 少于300ms的语音可能是噪音
            return False

        # 置信度检查
        if confidence < 0.3:  # 低置信度可能是噪音
            return False

        # 敏感度设置
        if self.interruption_sensitivity == 'low':
            return activity_level > threshold * 1.2
        elif self.interruption_sensitivity == 'high':
            return activity_level > threshold * 0.8
        else:  # medium
            return activity_level > threshold

    async def save_conversation(self, session_id: Optional[str] = None):
        """保存对话到持久化存储"""
        try:
            if not self.enable_persistence:
                return

            target_context = None
            if session_id:
                target_context = self.conversation_history.get(session_id)
            else:
                target_context = self.current_context

            if not target_context:
                return

            # 创建保存目录
            import os
            os.makedirs(self.persistence_path, exist_ok=True)

            # 准备保存数据
            save_data = {
                "session_id": target_context.session_id,
                "user_id": target_context.user_id,
                "start_time": target_context.start_time,
                "last_activity_time": target_context.last_activity_time,
                "state": target_context.state.value,
                "turn_count": target_context.turn_count,
                "messages": [
                    {
                        "type": msg.type.value,
                        "content": msg.content,
                        "timestamp": msg.timestamp,
                        "metadata": msg.metadata
                    }
                    for msg in target_context.messages
                ],
                "context_data": target_context.context_data,
                "topic_history": target_context.topic_history,
                "entity_memory": target_context.entity_memory,
                "user_preferences": target_context.user_preferences,
                "conversation_summary": target_context.conversation_summary,
                "intent_history": target_context.intent_history,
                "emotional_state": target_context.emotional_state,
                "context_keywords": target_context.context_keywords
            }

            # 保存到文件
            import json
            file_path = os.path.join(self.persistence_path, f"{target_context.session_id}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)

            self.last_save_time = time.time()
            self.logger.info(f"对话已保存: {target_context.session_id}")

        except Exception as e:
            self.logger.error(f"保存对话失败: {e}")

    async def load_conversation(self, session_id: str) -> Optional[ConversationContext]:
        """从持久化存储加载对话"""
        try:
            if not self.enable_persistence:
                return None

            import os
            import json

            file_path = os.path.join(self.persistence_path, f"{session_id}.json")
            if not os.path.exists(file_path):
                return None

            # 加载数据
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 重建ConversationContext
            context = ConversationContext(
                session_id=data["session_id"],
                user_id=data.get("user_id"),
                start_time=data["start_time"],
                last_activity_time=data["last_activity_time"],
                state=ConversationState(data["state"]),
                turn_count=data["turn_count"],
                context_data=data.get("context_data", {}),
                topic_history=data.get("topic_history", []),
                entity_memory=data.get("entity_memory", {}),
                user_preferences=data.get("user_preferences", {}),
                conversation_summary=data.get("conversation_summary", ""),
                intent_history=data.get("intent_history", []),
                emotional_state=data.get("emotional_state", "neutral"),
                context_keywords=data.get("context_keywords", [])
            )

            # 重建消息
            for msg_data in data["messages"]:
                message = ConversationMessage(
                    type=MessageType(msg_data["type"]),
                    content=msg_data["content"],
                    timestamp=msg_data["timestamp"],
                    metadata=msg_data.get("metadata", {})
                )
                context.messages.append(message)

            self.logger.info(f"对话已加载: {session_id}")
            return context

        except Exception as e:
            self.logger.error(f"加载对话失败: {e}")
            return None

    async def auto_save_check(self):
        """自动保存检查"""
        try:
            if (self.enable_persistence and
                self.current_context and
                time.time() - self.last_save_time > self.auto_save_interval):
                await self.save_conversation()
        except Exception as e:
            self.logger.error(f"自动保存检查失败: {e}")

    async def _end_current_conversation(self):
        """结束当前对话"""
        try:
            if not self.current_context:
                return

            # 添加结束消息
            end_message = ConversationMessage(
                type=MessageType.SYSTEM,
                content="对话结束",
                metadata={"event": "conversation_end"}
            )
            self.current_context.add_message(end_message)

            # 计算对话统计信息
            duration = self.current_context.get_conversation_duration()
            message_count = len(self.current_context.messages)

            print(f"🏁 对话结束 (时长: {duration:.1f}s, 消息数: {message_count})")

            # 发布对话结束事件
            await self.event_bus.emit(
                "conversation_ended",
                {
                    "session_id": self.current_context.session_id,
                    "duration": duration,
                    "message_count": message_count,
                    "turn_count": self.current_context.turn_count,
                    "timestamp": time.time()
                },
                priority=EventPriority.HIGH
            )

            # 保存对话上下文
            await self._save_conversation_context()

            # 更新平均对话长度
            self._update_conversation_statistics(duration)

            # 清除当前上下文
            self.current_context = None
            self.is_processing = False
            self.is_responding = False

        except Exception as e:
            self.logger.error(f"结束对话失败: {e}")

    def _clean_input_text(self, text: str) -> str:
        """清理输入文本"""
        try:
            # 移除FunASR的特殊标记
            cleaned = text.strip()

            # 移除常见的ASR噪音标记（保留标点符号）
            noise_patterns = [
                '<|startoftranscript|>', '<|endoftranscript|>',
                '<|nospeech|>', '<|silence|>',
                # 移除了标点符号的清理，让PUNC模型的结果保留
                'uh', 'um', 'er', 'ah',  # 语气词
            ]

            for pattern in noise_patterns:
                cleaned = cleaned.replace(pattern, '')

            # 移除多余的空格和标点
            cleaned = ' '.join(cleaned.split())

            # 检查最小长度
            if len(cleaned) < 2:
                return ""

            return cleaned

        except Exception as e:
            self.logger.error(f"清理输入文本失败: {e}")
            return text

    def _filter_llm_output(self, text: str) -> str:
        """过滤LLM输出内容，移除不应该被TTS合成的内容"""
        if not text:
            return ""

        try:
            # 移除<think>标签及其内容
            filtered = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)

            # 移除其他可能的标签
            filtered = re.sub(r'<[^>]+>', '', filtered)

            # 清理多余的空白字符
            filtered = re.sub(r'\n\s*\n', '\n', filtered)  # 移除多余的空行
            filtered = re.sub(r'\s+', ' ', filtered)       # 合并多余的空格

            return filtered.strip()

        except Exception as e:
            self.logger.error(f"过滤LLM输出失败: {e}")
            return text

    async def _tts_timeout_check(self, session_id: str):
        """TTS超时检查，防止系统卡住"""
        try:
            # 等待10秒，如果TTS服务没有响应，自动发送完成事件
            await asyncio.sleep(10.0)

            # 检查会话是否仍然活跃
            if (self.current_context and
                self.current_context.session_id == session_id and
                self.is_responding):

                self.logger.warning(f"TTS服务响应超时，自动发送完成事件 (会话: {session_id[:8]})")
                await self.event_bus.emit(
                    "response_complete",
                    {
                        "session_id": session_id,
                        "timestamp": time.time(),
                        "tts_timeout": True
                    },
                    priority=EventPriority.HIGH
                )

        except Exception as e:
            self.logger.error(f"TTS超时检查失败: {e}")

    async def _trim_conversation_history(self):
        """智能修剪对话历史"""
        try:
            if not self.current_context:
                return

            # 如果消息数量超过压缩阈值，进行智能压缩
            if len(self.current_context.messages) > self.context_compression_threshold:
                if self.enable_context_compression:
                    await self._compress_conversation_context()
                else:
                    # 简单修剪：保留系统消息和最近的用户/助手消息
                    system_messages = [msg for msg in self.current_context.messages if msg.type == MessageType.SYSTEM]
                    recent_messages = self.current_context.messages[-(self.max_context_length - len(system_messages)):]
                    self.current_context.messages = system_messages + recent_messages
                    self.logger.info(f"对话历史已修剪到 {len(self.current_context.messages)} 条消息")

        except Exception as e:
            self.logger.error(f"修剪对话历史失败: {e}")

    async def _compress_conversation_context(self):
        """智能压缩对话上下文"""
        try:
            if not self.current_context or len(self.current_context.messages) <= self.max_context_length:
                return

            # 分析消息重要性
            important_messages = []
            recent_messages = self.current_context.messages[-5:]  # 保留最近5条

            # 保留重要消息（包含关键信息的消息）
            for msg in self.current_context.messages[:-5]:  # 排除最近5条
                if self._is_important_message(msg):
                    important_messages.append(msg)

            # 生成对话摘要
            if len(self.current_context.messages) > 10:
                summary = await self._generate_conversation_summary()
                if summary:
                    self.current_context.conversation_summary = summary

            # 重构消息列表：摘要 + 重要消息 + 最近消息
            compressed_messages = []

            # 添加摘要消息
            if self.current_context.conversation_summary:
                summary_msg = ConversationMessage(
                    type=MessageType.SYSTEM,
                    content=f"[对话摘要] {self.current_context.conversation_summary}",
                    metadata={"compressed": True}
                )
                compressed_messages.append(summary_msg)

            # 添加重要消息（最多5条）
            compressed_messages.extend(important_messages[-5:])

            # 添加最近消息
            compressed_messages.extend(recent_messages)

            # 更新消息列表
            original_count = len(self.current_context.messages)
            self.current_context.messages = compressed_messages

            self.logger.info(f"对话上下文已压缩: {original_count} -> {len(compressed_messages)} 条消息")

        except Exception as e:
            self.logger.error(f"压缩对话上下文失败: {e}")

    def _is_important_message(self, message: ConversationMessage) -> bool:
        """判断消息是否重要"""
        # 系统消息通常重要
        if message.type == MessageType.SYSTEM:
            return True

        # 包含关键词的消息
        content_lower = message.content.lower()
        important_keywords = ['重要', '记住', '提醒', '设置', '配置', '偏好', '喜欢', '不喜欢']
        if any(keyword in content_lower for keyword in important_keywords):
            return True

        # 长消息可能包含重要信息
        if len(message.content) > 50:
            return True

        # 包含实体信息的消息
        if any(char.isdigit() for char in message.content):  # 包含数字
            return True

        return False

    async def _generate_conversation_summary(self) -> str:
        """生成对话摘要"""
        try:
            if not self.current_context or len(self.current_context.messages) < 5:
                return ""

            # 简单的摘要生成（可以后续集成更复杂的摘要模型）
            topics = list(set(self.current_context.topic_history))
            intents = list(set(self.current_context.intent_history))
            keywords = self.current_context.context_keywords[-10:]

            summary_parts = []
            if topics:
                summary_parts.append(f"讨论了{', '.join(topics[:3])}")
            if intents:
                summary_parts.append(f"涉及{', '.join(intents[:3])}等需求")
            if keywords:
                summary_parts.append(f"关键词包括{', '.join(keywords[:5])}")

            return "；".join(summary_parts) if summary_parts else "进行了一般性对话"

        except Exception as e:
            self.logger.error(f"生成对话摘要失败: {e}")
            return ""

    async def _learn_user_preferences(self, user_input: str, assistant_response: str):
        """学习用户偏好"""
        try:
            if not self.current_context:
                return

            user_lower = user_input.lower()

            # 检测用户偏好表达
            if any(word in user_lower for word in ['喜欢', '爱', '偏爱', '更喜欢']):
                # 提取喜欢的内容
                preference_keywords = self._extract_preference_keywords(user_input, positive=True)
                for keyword in preference_keywords:
                    self.current_context.user_preferences[f"喜欢_{keyword}"] = True

            elif any(word in user_lower for word in ['不喜欢', '讨厌', '不要', '别']):
                # 提取不喜欢的内容
                preference_keywords = self._extract_preference_keywords(user_input, positive=False)
                for keyword in preference_keywords:
                    self.current_context.user_preferences[f"不喜欢_{keyword}"] = True

            # 检测交互偏好
            if '简短' in user_lower or '简单' in user_lower:
                self.current_context.user_preferences["回复风格"] = "简短"
            elif '详细' in user_lower or '具体' in user_lower:
                self.current_context.user_preferences["回复风格"] = "详细"

            # 检测话题偏好
            if any(word in user_lower for word in ['天气', '温度']):
                self.current_context.user_preferences["关注天气"] = True
            elif any(word in user_lower for word in ['新闻', '资讯']):
                self.current_context.user_preferences["关注新闻"] = True

        except Exception as e:
            self.logger.error(f"学习用户偏好失败: {e}")

    def _extract_preference_keywords(self, text: str, positive: bool = True) -> List[str]:
        """提取偏好关键词"""
        # 简单的关键词提取
        import re
        words = re.findall(r'\b\w+\b', text.lower())

        # 过滤停用词和偏好表达词
        stop_words = {'的', '了', '是', '在', '我', '你', '他', '她', '它', '这', '那', '和', '与', '或',
                     '喜欢', '爱', '偏爱', '更喜欢', '不喜欢', '讨厌', '不要', '别'}

        keywords = [word for word in words if word not in stop_words and len(word) > 1]
        return keywords[:3]  # 返回前3个关键词

    def _evaluate_response_quality(self, user_input: str, assistant_response: str) -> float:
        """评估响应质量"""
        try:
            score = 0.5  # 基础分数

            # 长度适中性评估
            response_length = len(assistant_response)
            if 20 <= response_length <= 200:
                score += 0.1
            elif response_length > 500:
                score -= 0.1

            # 相关性评估（简单的关键词匹配）
            user_keywords = set(self._extract_keywords(user_input))
            response_keywords = set(self._extract_keywords(assistant_response))

            if user_keywords and response_keywords:
                relevance = len(user_keywords & response_keywords) / len(user_keywords)
                score += relevance * 0.2

            # 情感适当性评估
            if any(word in user_input.lower() for word in ['谢谢', '感谢']):
                if any(word in assistant_response.lower() for word in ['不客气', '不用谢', '很高兴']):
                    score += 0.1

            # 避免重复性评估
            if len(set(assistant_response.split())) / len(assistant_response.split()) > 0.8:
                score += 0.1
            else:
                score -= 0.1

            return min(max(score, 0.0), 1.0)  # 限制在0-1范围内

        except Exception as e:
            self.logger.error(f"评估响应质量失败: {e}")
            return 0.5

    async def _handle_max_turns_reached(self):
        """处理达到最大对话轮数"""
        try:
            print(f"⚠️ 达到最大对话轮数限制 ({self.max_turn_count})")

            # 添加提示消息
            limit_message = ConversationMessage(
                type=MessageType.SYSTEM,
                content=f"达到最大对话轮数 ({self.max_turn_count})",
                metadata={"event": "max_turns_reached"}
            )

            if self.current_context:
                self.current_context.add_message(limit_message)

            # 发布事件
            await self.event_bus.emit(
                "max_turns_reached",
                {
                    "session_id": self.current_context.session_id if self.current_context else None,
                    "turn_count": self.max_turn_count,
                    "timestamp": time.time()
                }
            )

            # 结束当前对话
            await self._end_current_conversation()

        except Exception as e:
            self.logger.error(f"处理最大轮数限制失败: {e}")

    async def _save_conversation_context(self):
        """保存对话上下文"""
        try:
            if not self.current_context:
                return

            # 保存到历史记录
            self.conversation_history[self.current_context.session_id] = self.current_context

            # 限制历史记录数量
            if len(self.conversation_history) > 100:
                # 移除最旧的记录
                oldest_session = min(self.conversation_history.keys(),
                                   key=lambda k: self.conversation_history[k].start_time)
                del self.conversation_history[oldest_session]

            self.logger.debug(f"保存对话上下文: {self.current_context.session_id}")

        except Exception as e:
            self.logger.error(f"保存对话上下文失败: {e}")

    def _update_conversation_statistics(self, duration: float):
        """更新对话统计信息"""
        try:
            # 计算平均对话长度
            if self.total_conversations > 0:
                self.average_conversation_length = (
                    (self.average_conversation_length * (self.total_conversations - 1) + duration)
                    / self.total_conversations
                )

        except Exception as e:
            self.logger.error(f"更新统计信息失败: {e}")

    async def _cleanup_expired_sessions(self):
        """清理过期会话"""
        try:
            while True:
                await asyncio.sleep(60)  # 每分钟检查一次

                current_time = time.time()
                expired_sessions = []

                for session_id, context in self.conversation_history.items():
                    if current_time - context.last_activity_time > self.session_timeout:
                        expired_sessions.append(session_id)

                # 移除过期会话
                for session_id in expired_sessions:
                    del self.conversation_history[session_id]
                    self.logger.debug(f"清理过期会话: {session_id}")

                if expired_sessions:
                    self.logger.info(f"清理了 {len(expired_sessions)} 个过期会话")

        except asyncio.CancelledError:
            pass
        except Exception as e:
            self.logger.error(f"清理过期会话失败: {e}")

    # 公共接口方法
    def get_current_context(self) -> Optional[ConversationContext]:
        """获取当前对话上下文"""
        return self.current_context

    def get_conversation_history(self, session_id: str) -> Optional[ConversationContext]:
        """获取指定会话的对话历史"""
        return self.conversation_history.get(session_id)

    def get_recent_messages(self, count: int = 5) -> List[ConversationMessage]:
        """获取最近的消息"""
        if not self.current_context:
            return []
        return self.current_context.get_recent_messages(count)

    def add_assistant_message(self, content: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """添加助手消息"""
        try:
            if not self.current_context:
                raise ValueError("没有活跃的对话上下文")

            message = ConversationMessage(
                type=MessageType.ASSISTANT,
                content=content,
                metadata=metadata or {}
            )

            self.current_context.add_message(message)
            self.total_messages += 1

            print(f"🤖 助手: {content}")

            return message.id

        except Exception as e:
            self.logger.error(f"添加助手消息失败: {e}")
            raise

    def add_interruption_callback(self, callback: Callable):
        """添加打断检测回调"""
        self.interruption_callbacks.append(callback)

    def remove_interruption_callback(self, callback: Callable):
        """移除打断检测回调"""
        try:
            self.interruption_callbacks.remove(callback)
        except ValueError:
            pass

    def get_conversation_statistics(self) -> Dict[str, Any]:
        """获取增强的对话统计信息"""
        current_duration = 0.0
        current_context_summary = ""
        current_quality_scores = []

        if self.current_context:
            current_duration = self.current_context.get_conversation_duration()
            current_context_summary = self.current_context.get_context_summary()

            # 计算当前对话的质量分数
            for msg in self.current_context.messages:
                if msg.type == MessageType.ASSISTANT and "quality_score" in msg.metadata:
                    current_quality_scores.append(msg.metadata["quality_score"])

        # 计算平均质量分数
        avg_quality = sum(current_quality_scores) / len(current_quality_scores) if current_quality_scores else 0.0

        # 分析打断模式
        recent_interruptions = len([i for i in self.interruption_history
                                  if time.time() - i["timestamp"] < 300])  # 最近5分钟

        return {
            # 基础统计
            "total_conversations": self.total_conversations,
            "total_messages": self.total_messages,
            "average_conversation_length": self.average_conversation_length,
            "active_sessions": len(self.conversation_history),

            # 当前会话信息
            "current_session_id": self.current_context.session_id if self.current_context else None,
            "current_conversation_duration": current_duration,
            "current_turn_count": self.current_context.turn_count if self.current_context else 0,
            "current_context_summary": current_context_summary,
            "current_state": self.current_context.state.value if self.current_context else "idle",

            # 状态信息
            "is_processing": self.is_processing,
            "is_responding": self.is_responding,

            # 质量统计
            "current_avg_quality": avg_quality,
            "quality_scores_count": len(current_quality_scores),

            # 打断统计
            "total_interruptions": self.interruption_count,
            "recent_interruptions": recent_interruptions,
            "interruption_sensitivity": self.interruption_sensitivity,
            "adaptive_interruption": self.adaptive_interruption,

            # 功能状态
            "enable_context_compression": self.enable_context_compression,
            "enable_intent_detection": self.enable_intent_detection,
            "enable_personalization": self.enable_personalization,
            "enable_persistence": self.enable_persistence,

            # 用户偏好统计
            "user_preferences_count": len(self.current_context.user_preferences) if self.current_context else 0,
            "context_keywords_count": len(self.current_context.context_keywords) if self.current_context else 0,
            "intent_history_count": len(self.current_context.intent_history) if self.current_context else 0
        }

    async def force_end_conversation(self, reason: str = "manual"):
        """强制结束当前对话"""
        try:
            if not self.current_context:
                return

            print(f"🛑 强制结束对话: {reason}")

            # 添加强制结束消息
            end_message = ConversationMessage(
                type=MessageType.SYSTEM,
                content=f"对话被强制结束: {reason}",
                metadata={"event": "force_end", "reason": reason}
            )

            self.current_context.add_message(end_message)

            # 结束对话
            await self._end_current_conversation()

        except Exception as e:
            self.logger.error(f"强制结束对话失败: {e}")

    async def set_conversation_context_data(self, key: str, value: Any):
        """设置对话上下文数据"""
        try:
            if not self.current_context:
                raise ValueError("没有活跃的对话上下文")

            self.current_context.context_data[key] = value

        except Exception as e:
            self.logger.error(f"设置上下文数据失败: {e}")
            raise

    def get_conversation_context_data(self, key: str, default: Any = None) -> Any:
        """获取对话上下文数据"""
        if not self.current_context:
            return default
        return self.current_context.context_data.get(key, default)

    async def _generate_llm_response(self, user_text: str):
        """生成LLM响应"""
        try:
            if not self.current_context:
                self.logger.error("没有当前对话上下文")
                return

            print(f"🤖 正在生成LLM响应...")

            # 构建增强的LLM请求
            messages = []

            # 添加上下文摘要（如果存在）
            context_summary = self.current_context.get_context_summary()
            if context_summary and context_summary != "新对话":
                context_msg = LLMMessage(
                    role="system",
                    content=f"对话上下文: {context_summary}",
                    timestamp=time.time()
                )
                messages.append(context_msg)

            # 添加个性化信息
            if self.enable_personalization and self.current_context.user_preferences:
                prefs = []
                for key, value in list(self.current_context.user_preferences.items())[:3]:
                    prefs.append(f"{key}: {value}")
                if prefs:
                    pref_msg = LLMMessage(
                        role="system",
                        content=f"用户偏好: {'; '.join(prefs)}",
                        timestamp=time.time()
                    )
                    messages.append(pref_msg)

            # 添加历史消息（最近几条）
            recent_messages = self.current_context.get_recent_messages(5)
            for msg in recent_messages[:-1]:  # 排除最后一条（当前用户输入）
                if msg.type == MessageType.USER:
                    messages.append(LLMMessage(role="user", content=msg.content, timestamp=msg.timestamp))
                elif msg.type == MessageType.ASSISTANT:
                    messages.append(LLMMessage(role="assistant", content=msg.content, timestamp=msg.timestamp))
                elif msg.type == MessageType.SYSTEM and not msg.metadata.get("compressed"):
                    # 只添加非压缩的系统消息
                    messages.append(LLMMessage(role="system", content=msg.content, timestamp=msg.timestamp))

            # 添加当前用户输入
            messages.append(LLMMessage(role="user", content=user_text, timestamp=time.time()))

            # 创建LLM请求
            llm_request = LLMRequest(
                messages=messages,
                max_tokens=500,
                temperature=0.7
            )

            # 调用LLM服务
            llm_response = await self.llm_service.chat(llm_request)

            if llm_response and llm_response.content:
                # 创建助手消息
                assistant_message = ConversationMessage(
                    type=MessageType.ASSISTANT,
                    content=llm_response.content,
                    metadata={
                        "processing_time": llm_response.processing_time,
                        "model": "dify"
                    }
                )

                # 添加到对话历史
                self.current_context.add_message(assistant_message)

                # 学习用户偏好
                if self.enable_personalization:
                    await self._learn_user_preferences(user_text, llm_response.content)

                # 评估对话质量
                quality_score = self._evaluate_response_quality(user_text, llm_response.content)
                assistant_message.metadata["quality_score"] = quality_score

                print(f"🤖 助手: {llm_response.content}")

                # 过滤LLM输出内容，移除不应该被TTS合成的部分
                filtered_content = self._filter_llm_output(llm_response.content)

                # 发布LLM响应事件
                await self.event_bus.emit(
                    "llm_response_generated",
                    {
                        "session_id": self.current_context.session_id,
                        "text": llm_response.content,
                        "filtered_text": filtered_content,
                        "processing_time": llm_response.processing_time,
                        "timestamp": time.time()
                    },
                    priority=EventPriority.HIGH
                )

                # 发布TTS合成请求（使用过滤后的内容）
                if filtered_content.strip():  # 只有在有内容时才发送TTS请求
                    print(f"🔊 准备TTS合成: {filtered_content[:50]}...")
                    await self.event_bus.emit(
                        "text_to_synthesize",
                        {
                            "text": filtered_content,
                            "streaming": True,
                            "session_id": self.current_context.session_id
                        },
                        priority=EventPriority.HIGH
                    )

                    # 启动TTS超时检查任务
                    asyncio.create_task(self._tts_timeout_check(self.current_context.session_id))

                else:
                    self.logger.warning("过滤后的LLM内容为空，跳过TTS合成")
                    # 直接发送响应完成事件
                    await self.event_bus.emit(
                        "response_complete",
                        {
                            "session_id": self.current_context.session_id,
                            "timestamp": time.time()
                        },
                        priority=EventPriority.HIGH
                    )

            else:
                print(f"❌ LLM响应失败或为空")

        except Exception as e:
            self.logger.error(f"生成LLM响应失败: {e}")
            print(f"❌ LLM响应生成失败: {e}")

    async def get_conversation_insights(self) -> Dict[str, Any]:
        """获取对话洞察分析"""
        try:
            if not self.current_context:
                return {"error": "没有活跃的对话"}

            insights = {
                "session_analysis": {
                    "duration": self.current_context.get_conversation_duration(),
                    "turn_count": self.current_context.turn_count,
                    "message_count": len(self.current_context.messages),
                    "avg_response_time": self._calculate_avg_response_time(),
                },
                "content_analysis": {
                    "topics": self.current_context.topic_history[-5:],
                    "intents": list(set(self.current_context.intent_history[-10:])),
                    "keywords": self.current_context.context_keywords[-10:],
                    "emotional_state": self.current_context.emotional_state,
                },
                "interaction_patterns": {
                    "interruption_rate": self.interruption_count / max(self.current_context.turn_count, 1),
                    "user_preferences": dict(list(self.current_context.user_preferences.items())[:5]),
                    "conversation_flow": self._analyze_conversation_flow(),
                },
                "quality_metrics": {
                    "avg_quality_score": self._calculate_avg_quality_score(),
                    "response_relevance": self._calculate_response_relevance(),
                    "user_satisfaction_indicators": self._detect_satisfaction_indicators(),
                }
            }

            return insights

        except Exception as e:
            self.logger.error(f"获取对话洞察失败: {e}")
            return {"error": str(e)}

    def _calculate_avg_response_time(self) -> float:
        """计算平均响应时间"""
        if not self.current_context or len(self.current_context.messages) < 2:
            return 0.0

        response_times = []
        for i in range(1, len(self.current_context.messages)):
            prev_msg = self.current_context.messages[i-1]
            curr_msg = self.current_context.messages[i]

            if (prev_msg.type == MessageType.USER and
                curr_msg.type == MessageType.ASSISTANT):
                response_time = curr_msg.timestamp - prev_msg.timestamp
                response_times.append(response_time)

        return sum(response_times) / len(response_times) if response_times else 0.0

    def _analyze_conversation_flow(self) -> Dict[str, Any]:
        """分析对话流程"""
        if not self.current_context:
            return {}

        flow_analysis = {
            "user_initiated_turns": 0,
            "assistant_initiated_turns": 0,
            "system_interventions": 0,
            "topic_switches": len(set(self.current_context.topic_history)),
            "intent_switches": len(set(self.current_context.intent_history)),
        }

        for msg in self.current_context.messages:
            if msg.type == MessageType.USER:
                flow_analysis["user_initiated_turns"] += 1
            elif msg.type == MessageType.ASSISTANT:
                flow_analysis["assistant_initiated_turns"] += 1
            elif msg.type == MessageType.SYSTEM:
                flow_analysis["system_interventions"] += 1

        return flow_analysis

    def _calculate_avg_quality_score(self) -> float:
        """计算平均质量分数"""
        if not self.current_context:
            return 0.0

        quality_scores = []
        for msg in self.current_context.messages:
            if (msg.type == MessageType.ASSISTANT and
                "quality_score" in msg.metadata):
                quality_scores.append(msg.metadata["quality_score"])

        return sum(quality_scores) / len(quality_scores) if quality_scores else 0.0

    def _calculate_response_relevance(self) -> float:
        """计算响应相关性"""
        # 简化的相关性计算
        if not self.current_context or len(self.current_context.messages) < 2:
            return 0.0

        relevance_scores = []
        for i in range(1, len(self.current_context.messages)):
            prev_msg = self.current_context.messages[i-1]
            curr_msg = self.current_context.messages[i]

            if (prev_msg.type == MessageType.USER and
                curr_msg.type == MessageType.ASSISTANT):

                # 简单的关键词重叠计算
                user_keywords = set(self._extract_keywords(prev_msg.content))
                assistant_keywords = set(self._extract_keywords(curr_msg.content))

                if user_keywords:
                    overlap = len(user_keywords & assistant_keywords) / len(user_keywords)
                    relevance_scores.append(overlap)

        return sum(relevance_scores) / len(relevance_scores) if relevance_scores else 0.0

    def _detect_satisfaction_indicators(self) -> Dict[str, int]:
        """检测用户满意度指标"""
        if not self.current_context:
            return {}

        indicators = {
            "positive_feedback": 0,
            "negative_feedback": 0,
            "questions_answered": 0,
            "follow_up_questions": 0,
            "thank_you_expressions": 0,
        }

        for msg in self.current_context.messages:
            if msg.type == MessageType.USER:
                content_lower = msg.content.lower()

                # 积极反馈
                if any(word in content_lower for word in ['好的', '谢谢', '很好', '不错', '满意']):
                    indicators["positive_feedback"] += 1

                # 消极反馈
                if any(word in content_lower for word in ['不对', '错误', '不好', '不满意', '重新']):
                    indicators["negative_feedback"] += 1

                # 感谢表达
                if any(word in content_lower for word in ['谢谢', '感谢', '多谢']):
                    indicators["thank_you_expressions"] += 1

                # 后续问题
                if '?' in msg.content or '？' in msg.content:
                    indicators["follow_up_questions"] += 1

        return indicators
