#!/usr/bin/env python3
"""
增强版ASR服务
集成智能VAD管理器，解决唤醒后的语音收集问题
"""

import asyncio
import time
import logging
import numpy as np
from typing import Dict, Any, Optional

from ..services.asr_service import ASRService
from ..core.smart_vad_manager import SmartVADManager, VoiceStateConfig, VoiceState, VoiceActivity
from ..services.event_bus import EventBus, EventPriority


class EnhancedASRService(ASRService):
    """增强版ASR服务，集成智能VAD管理器"""
    
    def __init__(self, config: Dict[str, Any], event_bus: EventBus):
        super().__init__(config, event_bus)
        
        # 智能VAD管理器配置
        vad_config = VoiceStateConfig(
            waiting_timeout=config.get('waiting_timeout', 10.0),
            speech_end_timeout=config.get('speech_end_timeout', 1.2),
            vad_chunk_size=config.get('vad_chunk_size', 200),
            min_speech_duration=config.get('min_speech_duration', 0.3),
            max_silence_in_speech=config.get('max_silence_in_speech', 0.5),
            sample_rate=self.sample_rate,
            enable_debug=config.get('enable_vad_debug', True)
        )
        
        # 创建智能VAD管理器
        self.smart_vad = SmartVADManager(vad_config)
        
        # 状态标志
        self.using_smart_vad = config.get('use_smart_vad', True)
        self.smart_vad_active = False
        
        self.logger.info(f"增强版ASR服务初始化 - 智能VAD: {'启用' if self.using_smart_vad else '禁用'}")
    
    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            # 调用父类初始化
            if not await super()._initialize_impl():
                return False
            
            # 初始化智能VAD管理器
            if self.using_smart_vad:
                if not await self.smart_vad.initialize():
                    self.logger.error("智能VAD管理器初始化失败")
                    return False
                
                # 设置智能VAD回调
                self.smart_vad.add_state_change_callback(self._on_vad_state_change)
                self.smart_vad.add_speech_activity_callback(self._on_vad_speech_activity)
            
            self.logger.info("增强版ASR服务初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"增强版ASR服务初始化失败: {e}")
            return False
    
    async def _on_wake_word_detected(self, event_data: Dict[str, Any]) -> None:
        """处理唤醒词检测事件（重写）"""
        try:
            if not self.enabled:
                return
            
            # 如果启用智能VAD，使用新的处理流程
            if self.using_smart_vad:
                await self._handle_wake_word_with_smart_vad(event_data)
            else:
                # 使用原有流程
                await super()._on_wake_word_detected(event_data)
                
        except Exception as e:
            self.logger.error(f"处理唤醒词事件失败: {e}")
    
    async def _handle_wake_word_with_smart_vad(self, event_data: Dict[str, Any]):
        """使用智能VAD处理唤醒词"""
        try:
            confidence = event_data.get('confidence', 0.0)
            
            print(f"🎯 检测到唤醒词! 置信度: {confidence:.4f}")
            print(f"🎤 请说出您的指令...")
            
            # 清空音频缓冲区（移除包含唤醒词的音频）
            self.audio_buffer.clear()
            
            # 启动智能VAD管理器
            if await self.smart_vad.start_voice_detection():
                self.smart_vad_active = True
                
                # 发布唤醒事件
                await self.event_bus.emit(
                    "wake_word_detected",
                    {
                        "confidence": confidence,
                        "timestamp": time.time(),
                        "using_smart_vad": True
                    },
                    priority=EventPriority.HIGH
                )
            else:
                self.logger.error("启动智能VAD失败，回退到传统模式")
                await super()._on_wake_word_detected(event_data)
                
        except Exception as e:
            self.logger.error(f"智能VAD处理唤醒词失败: {e}")
    
    async def _on_audio_chunk(self, event_data: Dict[str, Any]) -> None:
        """处理音频块事件（重写）"""
        try:
            if not self.enabled:
                return
            
            audio_chunk = event_data.get('audio_chunk')
            if audio_chunk is None:
                return
            
            # 如果智能VAD激活，将音频数据发送给VAD管理器
            if self.smart_vad_active:
                await self.smart_vad.add_audio_chunk(audio_chunk)
            
            # 调用父类处理（用于传统模式或备用处理）
            if not self.using_smart_vad or not self.smart_vad_active:
                await super()._on_audio_chunk(event_data)
                
        except Exception as e:
            self.logger.error(f"处理音频块失败: {e}")
    
    async def _on_vad_state_change(self, new_state: VoiceState, old_state: VoiceState = None):
        """处理VAD状态变化"""
        try:
            current_time = time.time()
            
            if new_state == VoiceState.WAITING_FOR_SPEECH:
                # 开始等待用户说话
                print(f"🔄 [{time.strftime('%H:%M:%S')}] 状态变化: wake_listening → speech_collecting")
                
            elif new_state == VoiceState.SPEECH_DETECTED:
                # 检测到语音开始
                print(f"🎤 ASR服务：检测到语音开始")
                
                # 开始收集音频到缓冲区
                self.is_collecting_speech = True
                self.speech_collection_start_time = current_time
                
            elif new_state == VoiceState.SPEECH_ACTIVE:
                # 用户正在说话
                pass  # 继续收集音频
                
            elif new_state == VoiceState.SPEECH_ENDED:
                # 语音结束，等待静音确认
                pass  # 继续等待确认
                
            elif new_state == VoiceState.TIMEOUT:
                # 超时，返回待唤醒状态
                await self._handle_vad_timeout()
                
        except Exception as e:
            self.logger.error(f"处理VAD状态变化失败: {e}")
    
    async def _on_vad_speech_activity(self, activity: VoiceActivity):
        """处理VAD语音活动"""
        try:
            # 如果正在收集语音且检测到语音活动，添加到缓冲区
            if self.is_collecting_speech and activity.is_speech:
                # 这里可以添加更精细的音频处理逻辑
                pass
                
            # 如果语音结束且静音时间足够，开始转写
            if (activity.state == VoiceState.SPEECH_ENDED and 
                not activity.is_speech):
                
                # 检查静音时间是否足够
                silence_duration = time.time() - self.smart_vad.last_silence_time
                if silence_duration >= self.smart_vad.config.speech_end_timeout:
                    await self._finalize_speech_collection()
                    
        except Exception as e:
            self.logger.error(f"处理VAD语音活动失败: {e}")
    
    async def _finalize_speech_collection(self):
        """完成语音收集并开始转写"""
        try:
            print(f"🔇 ASR服务：检测到{self.smart_vad.config.speech_end_timeout}秒静音，停止收集")
            
            # 停止收集
            self.is_collecting_speech = False
            self.smart_vad_active = False
            
            # 停止智能VAD
            await self.smart_vad.stop_voice_detection()
            
            # 发送语音收集完成事件
            print(f"✅ ASR服务：语音收集完成，开始转写...")
            await self.event_bus.emit(
                "speech_collection_complete",
                {
                    "timestamp": time.time(),
                    "collection_duration": time.time() - self.speech_collection_start_time if self.speech_collection_start_time else 0,
                    "using_smart_vad": True
                },
                priority=EventPriority.HIGH
            )
            
            # 检查是否有足够的音频进行识别
            buffer_info = self.audio_buffer.get_buffer_info()
            if buffer_info['buffer_size'] > 0:
                # 开始识别
                result = await self.recognize_buffered_audio()
                if not result:
                    print(f"❌ ASR服务：识别失败或无结果")
            else:
                print(f"❌ ASR服务：未检测到语音输入")
                # 发布空识别结果
                await self.event_bus.emit(
                    "speech_recognized",
                    {
                        "text": "",
                        "confidence": 0.0,
                        "error": "未检测到语音输入",
                        "timestamp": time.time(),
                        "using_smart_vad": True
                    }
                )
            
        except Exception as e:
            self.logger.error(f"完成语音收集失败: {e}")
    
    async def _handle_vad_timeout(self):
        """处理VAD超时"""
        try:
            print(f"🔇 检测到静默或无效输入")
            
            # 停止收集
            self.is_collecting_speech = False
            self.smart_vad_active = False
            
            # 发布超时事件
            await self.event_bus.emit(
                "speech_timeout",
                {
                    "timeout_type": "waiting_for_speech",
                    "timeout_duration": self.smart_vad.config.waiting_timeout,
                    "timestamp": time.time()
                },
                priority=EventPriority.HIGH
            )
            
            # 发布空识别结果
            await self.event_bus.emit(
                "speech_recognized",
                {
                    "text": "",
                    "confidence": 0.0,
                    "error": "等待语音超时",
                    "timestamp": time.time(),
                    "timeout": True
                }
            )
            
        except Exception as e:
            self.logger.error(f"处理VAD超时失败: {e}")
    
    async def start_speech_collection(self):
        """启动语音收集（重写以支持智能VAD）"""
        try:
            if not self.enabled:
                return
            
            if self.using_smart_vad:
                # 使用智能VAD模式
                print(f"🎤 请说出您的指令...")
                
                # 清空音频缓冲区
                self.audio_buffer.clear()
                
                # 启动智能VAD
                if await self.smart_vad.start_voice_detection():
                    self.smart_vad_active = True
                else:
                    # 回退到传统模式
                    await super().start_speech_collection()
            else:
                # 使用传统模式
                await super().start_speech_collection()
                
        except Exception as e:
            self.logger.error(f"启动语音收集失败: {e}")
    
    def get_enhanced_statistics(self) -> Dict[str, Any]:
        """获取增强统计信息"""
        try:
            base_stats = super().get_statistics() if hasattr(super(), 'get_statistics') else {}
            
            if self.using_smart_vad:
                vad_stats = self.smart_vad.get_statistics()
                return {
                    **base_stats,
                    "smart_vad": vad_stats,
                    "using_smart_vad": self.using_smart_vad,
                    "smart_vad_active": self.smart_vad_active
                }
            else:
                return {
                    **base_stats,
                    "using_smart_vad": False
                }
                
        except Exception as e:
            self.logger.error(f"获取增强统计信息失败: {e}")
            return {}
    
    async def cleanup(self):
        """清理资源"""
        try:
            # 停止智能VAD
            if self.smart_vad_active:
                await self.smart_vad.stop_voice_detection()
            
            # 调用父类清理
            await super().cleanup()
            
        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")
