#!/usr/bin/env python3
"""
打断检测服务
基于FunASR VAD的实时打断检测机制，支持TTS播放中的用户打断检测和处理
"""

import asyncio
import time
import logging
import numpy as np
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, field
from enum import Enum
import threading
from collections import deque

from .base_service import BaseService, ServiceStatus
from .event_bus import EventBus, EventPriority
from ..core.silence_detector import SilenceDetector, SilenceConfig, VoiceActivityResult
from ..interfaces.audio_interface import AudioChunk


class InterruptionType(Enum):
    """打断类型"""
    SPEECH_BASED = "speech_based"      # 基于语音的打断
    ENERGY_BASED = "energy_based"      # 基于能量的打断
    KEYWORD_BASED = "keyword_based"    # 基于关键词的打断
    GESTURE_BASED = "gesture_based"    # 基于手势的打断（预留）


class InterruptionSeverity(Enum):
    """打断严重程度"""
    LOW = "low"          # 低级打断（可能是噪音）
    MEDIUM = "medium"    # 中级打断（明确的语音活动）
    HIGH = "high"        # 高级打断（强烈的打断意图）
    CRITICAL = "critical" # 紧急打断（立即停止）


@dataclass
class InterruptionEvent:
    """打断事件"""
    event_id: str = field(default_factory=lambda: str(time.time()))
    type: InterruptionType = InterruptionType.SPEECH_BASED
    severity: InterruptionSeverity = InterruptionSeverity.MEDIUM
    confidence: float = 0.0
    timestamp: float = field(default_factory=time.time)
    audio_features: Dict[str, Any] = field(default_factory=dict)
    context: Dict[str, Any] = field(default_factory=dict)
    should_interrupt: bool = False
    reason: str = ""


@dataclass
class InterruptionConfig:
    """打断检测配置"""
    # 基础配置
    enabled: bool = True
    sensitivity: str = "medium"  # low, medium, high
    
    # 阈值配置
    energy_threshold: float = 0.02
    speech_confidence_threshold: float = 0.6
    duration_threshold: float = 0.3  # 最小语音持续时间
    
    # 自适应配置
    adaptive_threshold: bool = True
    learning_rate: float = 0.1
    history_window: int = 50
    
    # TTS相关配置
    tts_protection_time: float = 0.5  # TTS开始后的保护时间
    tts_fade_detection: bool = True   # 检测TTS音量衰减
    
    # 多模态配置
    enable_keyword_detection: bool = False
    interrupt_keywords: List[str] = field(default_factory=lambda: ["停止", "等等", "打断"])
    
    # 性能配置
    processing_interval: float = 0.05  # 处理间隔（50ms）
    max_queue_size: int = 100


class InterruptionDetectionService(BaseService):
    """打断检测服务"""
    
    def __init__(self, config: Dict[str, Any], event_bus: EventBus):
        super().__init__("interruption_detection", config)
        self.event_bus = event_bus
        
        # 解析配置
        interruption_config = config.get('interruption_detection', {})
        self.interruption_config = InterruptionConfig(
            enabled=interruption_config.get('enabled', True),
            sensitivity=interruption_config.get('sensitivity', 'medium'),
            energy_threshold=interruption_config.get('energy_threshold', 0.02),
            speech_confidence_threshold=interruption_config.get('speech_confidence_threshold', 0.6),
            duration_threshold=interruption_config.get('duration_threshold', 0.3),
            adaptive_threshold=interruption_config.get('adaptive_threshold', True),
            learning_rate=interruption_config.get('learning_rate', 0.1),
            history_window=interruption_config.get('history_window', 50),
            tts_protection_time=interruption_config.get('tts_protection_time', 0.5),
            tts_fade_detection=interruption_config.get('tts_fade_detection', True),
            enable_keyword_detection=interruption_config.get('enable_keyword_detection', False),
            interrupt_keywords=interruption_config.get('interrupt_keywords', ["停止", "等等", "打断"]),
            processing_interval=interruption_config.get('processing_interval', 0.05),
            max_queue_size=interruption_config.get('max_queue_size', 100)
        )
        
        # 状态管理
        self.is_monitoring = False
        self.is_tts_playing = False
        self.tts_start_time = 0.0
        self.tts_volume_history = deque(maxlen=20)
        
        # 音频处理
        self.audio_queue = asyncio.Queue(maxsize=self.interruption_config.max_queue_size)
        self.silence_detector = SilenceDetector(SilenceConfig(
            energy_threshold=self.interruption_config.energy_threshold,
            silence_duration_threshold=2.0,
            speech_duration_threshold=self.interruption_config.duration_threshold
        ))
        
        # 打断历史和统计
        self.interruption_history = deque(maxlen=self.interruption_config.history_window)
        self.false_positive_count = 0
        self.true_positive_count = 0
        self.adaptive_threshold = self.interruption_config.energy_threshold
        
        # 回调管理
        self.interruption_callbacks: List[Callable] = []
        
        # 处理任务
        self.processing_task: Optional[asyncio.Task] = None
        self.monitoring_task: Optional[asyncio.Task] = None
        
        # 性能统计
        self.total_detections = 0
        self.successful_interruptions = 0
        self.false_positives = 0
        
    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            if not self.interruption_config.enabled:
                self.logger.info("打断检测服务已禁用")
                return True
            
            # 订阅相关事件
            await self.event_bus.subscribe("audio_chunk", self._on_audio_chunk)
            await self.event_bus.subscribe("tts_started", self._on_tts_started)
            await self.event_bus.subscribe("tts_finished", self._on_tts_finished)
            await self.event_bus.subscribe("tts_audio_chunk", self._on_tts_audio_chunk)
            await self.event_bus.subscribe("speech_recognized", self._on_speech_recognized)
            
            self.logger.info("打断检测服务初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"打断检测服务初始化失败: {e}")
            return False
    
    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            if not self.interruption_config.enabled:
                return True
            
            # 启动音频处理任务
            self.processing_task = asyncio.create_task(self._audio_processing_loop())
            
            # 启动监控任务
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            
            self.is_monitoring = True
            self.logger.info("打断检测服务已启动")
            return True
            
        except Exception as e:
            self.logger.error(f"打断检测服务启动失败: {e}")
            return False
    
    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            self.is_monitoring = False
            
            # 停止处理任务
            if self.processing_task:
                self.processing_task.cancel()
                try:
                    await self.processing_task
                except asyncio.CancelledError:
                    pass
            
            # 停止监控任务
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass
            
            self.logger.info("打断检测服务已停止")
            return True
            
        except Exception as e:
            self.logger.error(f"打断检测服务停止失败: {e}")
            return False
    
    async def _health_check_impl(self) -> bool:
        """健康检查实现"""
        try:
            if not self.interruption_config.enabled:
                return True
            
            # 检查任务状态
            if self.processing_task and self.processing_task.done():
                return False
            
            if self.monitoring_task and self.monitoring_task.done():
                return False
            
            # 检查队列状态
            if self.audio_queue.qsize() > self.interruption_config.max_queue_size * 0.9:
                self.logger.warning("音频队列接近满载")
            
            return True
            
        except Exception as e:
            self.logger.error(f"打断检测服务健康检查失败: {e}")
            return False

    # 事件处理方法
    async def _on_audio_chunk(self, event_data: Dict[str, Any]):
        """处理音频块事件"""
        try:
            if not self.is_monitoring:
                return

            audio_chunk = event_data.get('audio_chunk')
            if audio_chunk and isinstance(audio_chunk, AudioChunk):
                # 将音频块加入处理队列
                try:
                    self.audio_queue.put_nowait(audio_chunk)
                except asyncio.QueueFull:
                    # 队列满时丢弃最旧的数据
                    try:
                        self.audio_queue.get_nowait()
                        self.audio_queue.put_nowait(audio_chunk)
                    except asyncio.QueueEmpty:
                        pass

        except Exception as e:
            self.logger.error(f"处理音频块事件失败: {e}")

    async def _on_tts_started(self, event_data: Dict[str, Any]):
        """处理TTS开始事件"""
        try:
            self.is_tts_playing = True
            self.tts_start_time = time.time()
            self.tts_volume_history.clear()

            self.logger.debug("TTS播放开始，启动打断检测")

        except Exception as e:
            self.logger.error(f"处理TTS开始事件失败: {e}")

    async def _on_tts_finished(self, event_data: Dict[str, Any]):
        """处理TTS完成事件"""
        try:
            self.is_tts_playing = False
            self.tts_start_time = 0.0
            self.tts_volume_history.clear()

            self.logger.debug("TTS播放结束，停止打断检测")

        except Exception as e:
            self.logger.error(f"处理TTS完成事件失败: {e}")

    async def _on_tts_audio_chunk(self, event_data: Dict[str, Any]):
        """处理TTS音频块事件"""
        try:
            if not self.is_tts_playing or not self.interruption_config.tts_fade_detection:
                return

            # 分析TTS音频音量，用于检测音量衰减
            audio_data = event_data.get('audio_data')
            if audio_data is not None:
                volume = np.sqrt(np.mean(audio_data ** 2)) if len(audio_data) > 0 else 0.0
                self.tts_volume_history.append(volume)

        except Exception as e:
            self.logger.error(f"处理TTS音频块事件失败: {e}")

    async def _on_speech_recognized(self, event_data: Dict[str, Any]):
        """处理语音识别事件"""
        try:
            if not self.interruption_config.enable_keyword_detection:
                return

            text = event_data.get('text', '').strip().lower()
            if text and any(keyword in text for keyword in self.interruption_config.interrupt_keywords):
                # 检测到打断关键词
                interruption_event = InterruptionEvent(
                    type=InterruptionType.KEYWORD_BASED,
                    severity=InterruptionSeverity.HIGH,
                    confidence=0.9,
                    context={"recognized_text": text, "keywords_found": True},
                    should_interrupt=True,
                    reason=f"检测到打断关键词: {text}"
                )

                await self._handle_interruption_event(interruption_event)

        except Exception as e:
            self.logger.error(f"处理语音识别事件失败: {e}")

    # 核心处理循环
    async def _audio_processing_loop(self):
        """音频处理循环"""
        try:
            while self.is_monitoring:
                try:
                    # 等待音频数据
                    audio_chunk = await asyncio.wait_for(
                        self.audio_queue.get(),
                        timeout=self.interruption_config.processing_interval
                    )

                    # 只在TTS播放时进行打断检测
                    if self.is_tts_playing:
                        await self._process_audio_for_interruption(audio_chunk)

                except asyncio.TimeoutError:
                    # 超时是正常的，继续循环
                    continue
                except Exception as e:
                    self.logger.error(f"音频处理循环错误: {e}")
                    await asyncio.sleep(0.1)

        except asyncio.CancelledError:
            self.logger.debug("音频处理循环被取消")
        except Exception as e:
            self.logger.error(f"音频处理循环异常: {e}")

    async def _monitoring_loop(self):
        """监控循环"""
        try:
            while self.is_monitoring:
                try:
                    # 更新自适应阈值
                    if self.interruption_config.adaptive_threshold:
                        self._update_adaptive_threshold()

                    # 清理过期的历史记录
                    self._cleanup_history()

                    # 等待下一次检查
                    await asyncio.sleep(1.0)

                except Exception as e:
                    self.logger.error(f"监控循环错误: {e}")
                    await asyncio.sleep(1.0)

        except asyncio.CancelledError:
            self.logger.debug("监控循环被取消")
        except Exception as e:
            self.logger.error(f"监控循环异常: {e}")

    async def _process_audio_for_interruption(self, audio_chunk: AudioChunk):
        """处理音频进行打断检测"""
        try:
            # 检查TTS保护时间
            if time.time() - self.tts_start_time < self.interruption_config.tts_protection_time:
                return

            # 使用静音检测器分析音频
            vad_result = self.silence_detector.analyze_audio_chunk(audio_chunk.data)

            # 创建打断事件
            interruption_event = InterruptionEvent(
                type=InterruptionType.SPEECH_BASED,
                confidence=vad_result.confidence,
                audio_features={
                    "energy": vad_result.energy,
                    "zero_crossing_rate": vad_result.zero_crossing_rate,
                    "is_speech": vad_result.is_speech
                },
                context={
                    "tts_playing_duration": time.time() - self.tts_start_time,
                    "adaptive_threshold": self.adaptive_threshold
                }
            )

            # 多维度打断检测
            should_interrupt = await self._evaluate_interruption(interruption_event, vad_result)
            interruption_event.should_interrupt = should_interrupt

            if should_interrupt:
                await self._handle_interruption_event(interruption_event)

        except Exception as e:
            self.logger.error(f"音频打断检测失败: {e}")

    async def _evaluate_interruption(self, event: InterruptionEvent, vad_result: VoiceActivityResult) -> bool:
        """评估是否应该打断"""
        try:
            # 基础语音检测
            if not vad_result.is_speech:
                return False

            # 能量阈值检查
            if vad_result.energy < self.adaptive_threshold:
                return False

            # 置信度检查
            if vad_result.confidence < self.interruption_config.speech_confidence_threshold:
                return False

            # TTS音量衰减检测
            if self.interruption_config.tts_fade_detection and self._is_tts_fading():
                # TTS音量正在衰减，降低打断阈值
                threshold_multiplier = 0.7
            else:
                threshold_multiplier = 1.0

            # 根据敏感度调整
            sensitivity_multipliers = {
                "low": 1.3,
                "medium": 1.0,
                "high": 0.7
            }
            threshold_multiplier *= sensitivity_multipliers.get(self.interruption_config.sensitivity, 1.0)

            # 最终判断
            final_threshold = self.adaptive_threshold * threshold_multiplier
            should_interrupt = vad_result.energy > final_threshold and vad_result.confidence > 0.5

            # 设置严重程度
            if should_interrupt:
                if vad_result.energy > final_threshold * 2.0:
                    event.severity = InterruptionSeverity.HIGH
                elif vad_result.energy > final_threshold * 1.5:
                    event.severity = InterruptionSeverity.MEDIUM
                else:
                    event.severity = InterruptionSeverity.LOW

                event.reason = f"语音能量 {vad_result.energy:.3f} 超过阈值 {final_threshold:.3f}"

            return should_interrupt

        except Exception as e:
            self.logger.error(f"评估打断失败: {e}")
            return False

    def _is_tts_fading(self) -> bool:
        """检测TTS音量是否正在衰减"""
        try:
            if len(self.tts_volume_history) < 5:
                return False

            # 计算最近几个音量的趋势
            recent_volumes = list(self.tts_volume_history)[-5:]

            # 简单的衰减检测：最新音量明显低于平均音量
            avg_volume = sum(recent_volumes) / len(recent_volumes)
            latest_volume = recent_volumes[-1]

            return latest_volume < avg_volume * 0.7

        except Exception as e:
            self.logger.error(f"TTS衰减检测失败: {e}")
            return False

    def _update_adaptive_threshold(self):
        """更新自适应阈值"""
        try:
            if len(self.interruption_history) < 5:
                return

            # 分析最近的打断历史
            recent_events = list(self.interruption_history)[-10:]

            # 计算成功率
            successful_interruptions = sum(1 for event in recent_events
                                         if event.context.get('confirmed', False))
            false_positives = len(recent_events) - successful_interruptions

            # 根据成功率调整阈值
            if false_positives > successful_interruptions:
                # 误报较多，提高阈值
                self.adaptive_threshold *= (1 + self.interruption_config.learning_rate)
            elif successful_interruptions > false_positives * 2:
                # 成功率高，可以降低阈值
                self.adaptive_threshold *= (1 - self.interruption_config.learning_rate * 0.5)

            # 限制阈值范围
            min_threshold = self.interruption_config.energy_threshold * 0.5
            max_threshold = self.interruption_config.energy_threshold * 3.0
            self.adaptive_threshold = max(min_threshold, min(max_threshold, self.adaptive_threshold))

        except Exception as e:
            self.logger.error(f"更新自适应阈值失败: {e}")

    def _cleanup_history(self):
        """清理过期的历史记录"""
        try:
            current_time = time.time()
            # 清理超过1小时的记录
            while (self.interruption_history and
                   current_time - self.interruption_history[0].timestamp > 3600):
                self.interruption_history.popleft()

        except Exception as e:
            self.logger.error(f"清理历史记录失败: {e}")

    async def _handle_interruption_event(self, event: InterruptionEvent):
        """处理打断事件"""
        try:
            # 记录事件
            self.interruption_history.append(event)
            self.total_detections += 1

            if event.should_interrupt:
                self.successful_interruptions += 1

                # 发布打断事件
                await self.event_bus.emit(
                    "interruption_detected",
                    {
                        "event_id": event.event_id,
                        "type": event.type.value,
                        "severity": event.severity.value,
                        "confidence": event.confidence,
                        "reason": event.reason,
                        "audio_features": event.audio_features,
                        "context": event.context,
                        "timestamp": event.timestamp
                    },
                    priority=EventPriority.CRITICAL
                )

                # 调用回调函数
                for callback in self.interruption_callbacks:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(event)
                        else:
                            callback(event)
                    except Exception as e:
                        self.logger.error(f"打断回调执行失败: {e}")

                self.logger.info(f"检测到打断: {event.reason} (置信度: {event.confidence:.2f})")

        except Exception as e:
            self.logger.error(f"处理打断事件失败: {e}")

    # 公共API方法
    def add_interruption_callback(self, callback: Callable):
        """添加打断检测回调"""
        self.interruption_callbacks.append(callback)

    def remove_interruption_callback(self, callback: Callable):
        """移除打断检测回调"""
        try:
            self.interruption_callbacks.remove(callback)
        except ValueError:
            pass

    def get_interruption_statistics(self) -> Dict[str, Any]:
        """获取打断检测统计信息"""
        try:
            recent_events = [event for event in self.interruption_history
                           if time.time() - event.timestamp < 300]  # 最近5分钟

            return {
                "total_detections": self.total_detections,
                "successful_interruptions": self.successful_interruptions,
                "false_positives": self.false_positives,
                "recent_detections": len(recent_events),
                "current_threshold": self.adaptive_threshold,
                "is_monitoring": self.is_monitoring,
                "is_tts_playing": self.is_tts_playing,
                "tts_playing_duration": time.time() - self.tts_start_time if self.is_tts_playing else 0,
                "queue_size": self.audio_queue.qsize(),
                "config": {
                    "enabled": self.interruption_config.enabled,
                    "sensitivity": self.interruption_config.sensitivity,
                    "adaptive_threshold": self.interruption_config.adaptive_threshold,
                    "keyword_detection": self.interruption_config.enable_keyword_detection
                }
            }

        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}

    async def set_sensitivity(self, sensitivity: str):
        """动态设置敏感度"""
        try:
            if sensitivity in ["low", "medium", "high"]:
                self.interruption_config.sensitivity = sensitivity
                self.logger.info(f"打断检测敏感度已设置为: {sensitivity}")
            else:
                raise ValueError(f"无效的敏感度设置: {sensitivity}")

        except Exception as e:
            self.logger.error(f"设置敏感度失败: {e}")
            raise

    async def confirm_interruption(self, event_id: str, confirmed: bool):
        """确认打断检测结果（用于学习）"""
        try:
            # 查找对应的事件
            for event in self.interruption_history:
                if event.event_id == event_id:
                    event.context['confirmed'] = confirmed
                    if confirmed:
                        self.true_positive_count += 1
                    else:
                        self.false_positive_count += 1
                    break

        except Exception as e:
            self.logger.error(f"确认打断结果失败: {e}")
