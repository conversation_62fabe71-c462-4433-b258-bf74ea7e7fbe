#!/usr/bin/env python3
"""
整合版唤醒词检测服务
包含模型加载、特征提取、实时检测等所有功能
基于验证的TC-ResNet8架构实现
"""

import asyncio
import numpy as np
import torch
import torch.nn as nn
import onnxruntime as ort
import librosa
import time
import logging
import sys
from pathlib import Path
from typing import Dict, Any, Optional, Callable, List, Tuple
from collections import deque
import threading

from .base_service import BaseService, ServiceStatus
from .event_bus import EventBus, EventPriority

# 导入TC-ResNet8模型定义
sys.path.append(str(Path(__file__).parent.parent.parent / "models" / "wake_word"))
from model import TCResNet8


class AudioFeatureExtractor:
    """音频特征提取器 - 匹配TC-ResNet8的输入要求"""
    
    def __init__(self, sample_rate: int = 16000, n_mfcc: int = 40, 
                 win_len_ms: float = 25.0, hop_len_ms: float = 10.0,
                 target_time_steps: int = 100):
        self.sample_rate = sample_rate
        self.n_mfcc = n_mfcc
        self.win_len_ms = win_len_ms
        self.hop_len_ms = hop_len_ms
        self.target_time_steps = target_time_steps
        
        # 计算窗长和帧移（样本数）
        self.win_length = int(self.sample_rate * self.win_len_ms / 1000)
        self.hop_length = int(self.sample_rate * self.hop_len_ms / 1000)
        self.n_fft = 2 ** int(np.ceil(np.log2(self.win_length)))
        
        # 计算目标音频长度
        self.target_audio_length = (self.target_time_steps - 1) * self.hop_length + self.win_length
        
        self.logger = logging.getLogger(__name__)
    
    def extract_features(self, audio: np.ndarray) -> np.ndarray:
        """
        提取MFCC特征，输出格式为 (time_steps, n_mfcc)
        然后转换为模型期望的 (1, 1, time_steps, n_mfcc) 格式
        """
        try:
            # 确保音频是一维的
            if audio.ndim > 1:
                audio = audio.flatten()
            
            # 调整音频长度到目标长度
            if len(audio) > self.target_audio_length:
                # 截取中间部分
                start_idx = (len(audio) - self.target_audio_length) // 2
                audio = audio[start_idx:start_idx + self.target_audio_length]
            elif len(audio) < self.target_audio_length:
                # 零填充
                pad_width = self.target_audio_length - len(audio)
                audio = np.pad(audio, (0, pad_width), mode='constant')
            
            # 提取MFCC特征
            mfcc = librosa.feature.mfcc(
                y=audio,
                sr=self.sample_rate,
                n_mfcc=self.n_mfcc,
                n_fft=self.n_fft,
                hop_length=self.hop_length,
                win_length=self.win_length
            )
            
            # mfcc shape: (n_mfcc, time_steps)
            # 转置为 (time_steps, n_mfcc)
            mfcc = mfcc.T
            
            # 确保时间步数正确
            if mfcc.shape[0] != self.target_time_steps:
                if mfcc.shape[0] > self.target_time_steps:
                    # 截取
                    mfcc = mfcc[:self.target_time_steps, :]
                else:
                    # 填充
                    pad_width = ((0, self.target_time_steps - mfcc.shape[0]), (0, 0))
                    mfcc = np.pad(mfcc, pad_width, mode='constant')
            
            return mfcc.astype(np.float32)
            
        except Exception as e:
            self.logger.error(f"特征提取失败: {e}")
            return np.zeros((self.target_time_steps, self.n_mfcc), dtype=np.float32)


class WakeWordDetector:
    """唤醒词检测器 - 支持ONNX和PyTorch模型"""
    
    def __init__(self, model_path: str, confidence_threshold: float = 0.65, 
                 model_type: str = "auto"):
        self.model_path = Path(model_path)
        self.confidence_threshold = confidence_threshold
        self.model_type = model_type
        
        # 组件
        self.feature_extractor = AudioFeatureExtractor()
        self.onnx_session = None
        self.pytorch_model = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 状态
        self.is_loaded = False
        self.actual_model_type = None
        
        self.logger = logging.getLogger(__name__)
    
    def _detect_model_type(self) -> str:
        """自动检测模型类型"""
        if self.model_type != "auto":
            return self.model_type
        
        suffix = self.model_path.suffix.lower()
        if suffix == ".onnx":
            return "onnx"
        elif suffix in [".pt", ".pth"]:
            return "pytorch"
        else:
            raise ValueError(f"不支持的模型文件格式: {suffix}")
    
    def load_model(self) -> bool:
        """加载模型"""
        try:
            self.actual_model_type = self._detect_model_type()
            
            if self.actual_model_type == "onnx":
                return self._load_onnx_model()
            elif self.actual_model_type == "pytorch":
                return self._load_pytorch_model()
            else:
                raise ValueError(f"不支持的模型类型: {self.actual_model_type}")
                
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            return False
    
    def _load_onnx_model(self) -> bool:
        """加载ONNX模型"""
        try:
            # 极致ONNX Runtime配置优化（基于官方最佳实践）
            session_options = ort.SessionOptions()

            # 极致线程配置：单线程避免过度并行化
            session_options.intra_op_num_threads = 1  # 单线程，避免线程竞争
            session_options.inter_op_num_threads = 1

            # 执行模式：顺序执行，对简单模型更优
            session_options.execution_mode = ort.ExecutionMode.ORT_SEQUENTIAL

            # 图优化：启用最高级别优化
            session_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL

            # 禁用线程旋转（关键优化）：大幅降低CPU使用率
            session_options.add_session_config_entry("session.intra_op.allow_spinning", "0")
            session_options.add_session_config_entry("session.inter_op.allow_spinning", "0")

            # 内存优化：禁用内存池减少开销
            session_options.enable_mem_pattern = False
            session_options.enable_cpu_mem_arena = False

            # 创建ONNX Runtime会话
            providers = ['CPUExecutionProvider']
            if ort.get_device() == 'GPU':
                providers.insert(0, 'CUDAExecutionProvider')

            self.onnx_session = ort.InferenceSession(
                str(self.model_path),
                sess_options=session_options,
                providers=providers
            )
            
            self.is_loaded = True
            self.logger.info(f"ONNX模型加载成功: {self.model_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"ONNX模型加载失败: {e}")
            return False
    
    def _load_pytorch_model(self) -> bool:
        """加载PyTorch模型"""
        try:
            # 尝试加载TorchScript模型
            try:
                self.pytorch_model = torch.jit.load(self.model_path, map_location=self.device)
                self.logger.info("加载TorchScript模型")
            except:
                # 尝试加载state_dict
                checkpoint = torch.load(self.model_path, map_location=self.device)
                
                # 创建TC-ResNet8模型实例
                self.pytorch_model = TCResNet8(input_channels=1, num_classes=1, k=1)
                
                if isinstance(checkpoint, dict):
                    if 'model_state_dict' in checkpoint:
                        self.pytorch_model.load_state_dict(checkpoint['model_state_dict'])
                    elif 'state_dict' in checkpoint:
                        self.pytorch_model.load_state_dict(checkpoint['state_dict'])
                    else:
                        self.pytorch_model.load_state_dict(checkpoint)
                else:
                    # 直接是state_dict
                    self.pytorch_model.load_state_dict(checkpoint)
                
                self.logger.info("加载PyTorch state_dict模型")
            
            self.pytorch_model.to(self.device)
            self.pytorch_model.eval()
            
            self.is_loaded = True
            self.logger.info(f"PyTorch模型加载成功: {self.model_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"PyTorch模型加载失败: {e}")
            return False
    
    def detect(self, audio: np.ndarray) -> Tuple[bool, float]:
        """检测唤醒词"""
        if not self.is_loaded:
            self.logger.warning("模型未加载")
            return False, 0.0
        
        try:
            # 提取特征 - 输出 (time_steps, n_mfcc)
            features = self.feature_extractor.extract_features(audio)

            # 调试：打印特征信息
            if hasattr(self, '_debug_count'):
                self._debug_count += 1
            else:
                self._debug_count = 1

            # 调试信息已关闭，只在检测到时输出

            # 根据模型类型进行推理
            if self.actual_model_type == "onnx":
                confidence = self._predict_onnx(features)
            elif self.actual_model_type == "pytorch":
                confidence = self._predict_pytorch(features)
            else:
                return False, 0.0

            # 调试信息已关闭

            # 判断是否检测到唤醒词
            detected = confidence >= self.confidence_threshold

            return detected, confidence
            
        except Exception as e:
            self.logger.error(f"检测失败: {e}")
            return False, 0.0
    
    def _predict_onnx(self, features: np.ndarray) -> float:
        """使用ONNX模型进行预测"""
        try:
            # features shape: (time_steps, n_mfcc) = (100, 40)
            # 需要转换为: (batch_size, channels, time_steps, n_mfcc) = (1, 1, 100, 40)

            # 转置为 (n_mfcc, time_steps) = (40, 100)
            features = features.T

            # 添加batch和channel维度: (1, 1, 40, 100)
            features = features[np.newaxis, np.newaxis, :, :]

            # 转置为期望格式 (1, 1, 100, 40)
            features = features.transpose(0, 1, 3, 2)

            # 调试信息已关闭

            # 获取输入名称
            input_name = self.onnx_session.get_inputs()[0].name

            # 运行推理（添加详细计时）
            onnx_start_time = time.time()
            outputs = self.onnx_session.run(None, {input_name: features})
            onnx_inference_time = time.time() - onnx_start_time

            # 记录ONNX推理时间（如果有性能统计）
            if hasattr(self, 'performance_stats'):
                self.performance_stats['onnx_inference_time'] += onnx_inference_time

            # 调试：打印原始输出
            raw_output = float(outputs[0][0][0])
            # 获取置信度 - 应用sigmoid激活
            confidence = 1.0 / (1.0 + np.exp(-raw_output))  # sigmoid

            return confidence
            
        except Exception as e:
            self.logger.error(f"ONNX推理失败: {e}")
            return 0.0
    
    def _predict_pytorch(self, features: np.ndarray) -> float:
        """使用PyTorch模型进行预测"""
        try:
            # features shape: (time_steps, n_mfcc) = (100, 40)
            # 需要转换为: (batch_size, channels, time_steps, n_mfcc) = (1, 1, 100, 40)
            
            # 转置为 (n_mfcc, time_steps) = (40, 100)
            features = features.T
            
            # 添加batch和channel维度: (1, 1, 40, 100)
            features = features[np.newaxis, np.newaxis, :, :]
            
            # 转置为期望格式 (1, 1, 100, 40)
            features = features.transpose(0, 1, 3, 2)
            
            # 转换为tensor
            features_tensor = torch.from_numpy(features).float().to(self.device)
            
            # 推理
            with torch.no_grad():
                output = self.pytorch_model(features_tensor)
                raw_output = float(output.cpu().numpy()[0][0])
                
                # 应用sigmoid激活
                confidence = 1.0 / (1.0 + np.exp(-raw_output))
            
            return confidence
            
        except Exception as e:
            self.logger.error(f"PyTorch推理失败: {e}")
            return 0.0
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_path": str(self.model_path),
            "model_type": self.actual_model_type,
            "confidence_threshold": self.confidence_threshold,
            "is_loaded": self.is_loaded,
            "device": str(self.device) if self.actual_model_type == "pytorch" else "CPU",
            "feature_extractor": {
                "sample_rate": self.feature_extractor.sample_rate,
                "n_mfcc": self.feature_extractor.n_mfcc,
                "target_time_steps": self.feature_extractor.target_time_steps,
                "target_audio_length": self.feature_extractor.target_audio_length
            }
        }


class AudioBuffer:
    """音频缓冲区管理"""

    def __init__(self,
                 sample_rate: int = 16000,
                 window_size: float = 1.1,  # 调整为1.1秒，确保能容纳模型需要的1.015秒
                 hop_size: float = 0.3,     # 减少hop_size以提高检测频率
                 max_buffer_size: float = 10.0):
        self.sample_rate = sample_rate
        self.window_size = window_size
        self.hop_size = hop_size
        self.max_buffer_size = max_buffer_size

        # 计算样本数
        self.window_samples = int(window_size * sample_rate)
        self.hop_samples = int(hop_size * sample_rate)
        self.max_buffer_samples = int(max_buffer_size * sample_rate)

        # 音频缓冲区
        self.buffer = deque(maxlen=self.max_buffer_samples)
        self.lock = threading.Lock()

        self.logger = logging.getLogger(__name__)

    def add_audio(self, audio_chunk: np.ndarray) -> None:
        """添加音频数据到缓冲区"""
        with self.lock:
            self.buffer.extend(audio_chunk.flatten())

    def get_windows(self) -> List[np.ndarray]:
        """获取滑动窗口音频片段"""
        windows = []

        with self.lock:
            buffer_array = np.array(self.buffer)

            if len(buffer_array) < self.window_samples:
                return windows

            # 生成滑动窗口
            start = 0
            while start + self.window_samples <= len(buffer_array):
                window = buffer_array[start:start + self.window_samples]
                windows.append(window.astype(np.float32))
                start += self.hop_samples

            # 保留足够的数据以便下次能产生窗口
            if windows:
                # 保留最后一个窗口大小的数据，确保下次仍能产生窗口
                keep_samples = max(self.window_samples, len(buffer_array) - self.hop_samples)
                if keep_samples < len(buffer_array):
                    self.buffer = deque(buffer_array[-keep_samples:], maxlen=self.max_buffer_samples)
                # 如果没有足够数据可以清理，就保持原样

        return windows

    def clear(self) -> None:
        """清空缓冲区"""
        with self.lock:
            self.buffer.clear()

    def get_buffer_info(self) -> Dict[str, Any]:
        """获取缓冲区信息"""
        with self.lock:
            return {
                "buffer_size": len(self.buffer),
                "buffer_duration": len(self.buffer) / self.sample_rate,
                "window_samples": self.window_samples,
                "hop_samples": self.hop_samples,
                "max_buffer_samples": self.max_buffer_samples
            }


class WakeWordService(BaseService):
    """唤醒词检测服务 - 整合版"""

    def __init__(self, config: Dict[str, Any], event_bus: EventBus):
        super().__init__("wake_word", config)
        self.event_bus = event_bus

        # 配置参数
        self.enabled = config.get('enabled', True)
        self.model_path = config.get('model_path', 'models/wake_word/hey_aibi.onnx')
        self.keyword = config.get('keyword', 'hey aibi')
        self.confidence_threshold = config.get('confidence_threshold', 0.85)
        self.sample_rate = config.get('sample_rate', 16000)
        self.chunk_size = config.get('chunk_size', 1024)
        self.window_size = config.get('window_size', 1.1)  # 调整为1.1秒
        self.hop_size = config.get('hop_size', 0.3)      # 调整为0.3秒
        self.cooldown_period = config.get('cooldown_period', 2.0)

        # 核心组件
        self.detector = WakeWordDetector(
            model_path=self.model_path,
            confidence_threshold=self.confidence_threshold
        )
        self.audio_buffer = AudioBuffer(
            sample_rate=self.sample_rate,
            window_size=self.window_size,
            hop_size=self.hop_size
        )

        # 运行状态
        self.is_detecting = False
        self.detection_task = None

        # 移除VAD预筛选相关状态，回到原始架构

        # 性能统计
        self.detection_count = 0
        self.wake_word_count = 0
        self.avg_detection_time = 0.0
        self.last_detection_time = None
        self.last_wake_time = 0

        # 详细性能监控
        self.performance_stats = {
            'total_detections': 0,
            'total_detection_time': 0.0,
            'onnx_inference_time': 0.0,
            'audio_processing_time': 0.0,
            'loop_iterations': 0,
            'windows_processed': 0,
            'skipped_detections': 0,
            'start_time': time.time(),
            'last_stats_time': time.time()
        }

        # 极致推理频率控制（平衡性能和功能）
        self.min_inference_interval = 0.15  # 最小推理间隔150ms（平衡优化）
        self.last_inference_time = 0.0
        self.energy_threshold = 0.0001  # 降低音频能量阈值，确保检测功能

        # 回调函数
        self.detection_callbacks: List[Callable] = []

    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            if not self.enabled:
                self.logger.info("唤醒词检测已禁用")
                return True

            # 加载检测模型
            if not self.detector.load_model():
                self.logger.error("唤醒词模型加载失败")
                return False

            # 订阅音频数据事件
            await self.event_bus.subscribe(
                "audio_chunk",
                self._on_audio_chunk,
                priority=EventPriority.HIGH
            )

            # VAD预筛选已移除，回到原始架构

            self.logger.info(f"唤醒词检测服务初始化完成 - 关键词: {self.keyword}")
            return True

        except Exception as e:
            self.logger.error(f"唤醒词检测服务初始化失败: {e}")
            return False

    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            if not self.enabled:
                return True

            # 启动检测任务
            self.is_detecting = True
            self.detection_task = asyncio.create_task(self._detection_loop())

            self.logger.info("唤醒词检测服务已启动")
            return True

        except Exception as e:
            self.logger.error(f"唤醒词检测服务启动失败: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            self.is_detecting = False

            # 停止检测任务
            if self.detection_task:
                self.detection_task.cancel()
                try:
                    await self.detection_task
                except asyncio.CancelledError:
                    pass
                self.detection_task = None

            # 清空缓冲区
            self.audio_buffer.clear()

            self.logger.info("唤醒词检测服务已停止")
            return True

        except Exception as e:
            self.logger.error(f"唤醒词检测服务停止失败: {e}")
            return False

    async def _health_check_impl(self) -> bool:
        """健康检查实现"""
        try:
            if not self.enabled:
                return True

            # 检查模型是否加载
            if not self.detector.is_loaded:
                self.logger.warning("唤醒词模型未加载")
                return False

            # 检查检测任务是否运行
            if self.is_detecting and (not self.detection_task or self.detection_task.done()):
                self.logger.warning("唤醒词检测任务未运行")
                return False

            return True

        except Exception as e:
            self.logger.error(f"唤醒词检测服务健康检查失败: {e}")
            return False

    async def _on_audio_chunk(self, event_data: Dict[str, Any]) -> None:
        """处理音频数据事件"""
        try:
            # 初始化计数器
            if not hasattr(self, '_audio_chunk_count'):
                self._audio_chunk_count = 0
                print("🎤 唤醒词服务开始接收音频数据")

            self._audio_chunk_count += 1

            if not self.is_detecting:
                return

            audio_chunk = event_data.get('audio_data')
            if audio_chunk is not None:
                # 音频数据调试信息已关闭，只在检测到时输出

                self.audio_buffer.add_audio(audio_chunk)

                # 每200次音频块显示一次状态（约10秒）
                if self._audio_chunk_count % 200 == 0:
                    buffer_info = self.audio_buffer.get_buffer_info()
                    print(f"📊 音频状态: 已接收{self._audio_chunk_count}块, 缓冲区{buffer_info['buffer_size']}样本")

        except Exception as e:
            print(f"❌ 音频处理错误: {e}")

    async def _detection_loop(self) -> None:
        """检测循环"""
        try:
            print("🔍 唤醒词检测循环已启动")
            loop_count = 0
            last_log_time = time.time()

            while self.is_detecting:
                loop_start_time = time.time()

                # 减少获取窗口的频率
                loop_count += 1
                current_time = time.time()

                # 更新循环统计
                self.performance_stats['loop_iterations'] += 1

                # 每10秒记录一次详细性能状态
                if current_time - last_log_time >= 10.0:
                    self._print_performance_stats()
                    last_log_time = current_time

                # 恢复原始检测逻辑，移除VAD预筛选

                # 获取音频窗口
                windows = self.audio_buffer.get_windows()

                # 如果没有足够的音频数据，跳过这次循环
                if not windows or len(windows) == 0:
                    await asyncio.sleep(0.1)
                    continue

                # 窗口调试信息已关闭

                # 大幅减少检测频率，解决CPU过载问题
                max_windows_per_cycle = 1  # 每次循环只处理1个窗口
                processed_windows = 0

                # 极致窗口处理优化：大幅减少处理频率
                if len(windows) > 20:
                    windows = windows[-1:]  # 只保留最新的1个窗口
                elif len(windows) > 5:
                    # 只处理每5个窗口中的1个
                    windows = windows[::5]
                elif len(windows) > 0:
                    # 少量窗口时，只处理最新的
                    windows = windows[-1:]

                for window in windows:
                    if not self.is_detecting:
                        break

                    # 限制每次循环处理的窗口数量
                    if processed_windows >= max_windows_per_cycle:
                        break

                    # 智能推理控制：时间间隔 + 音频能量预筛选
                    current_time = time.time()

                    # 检查时间间隔
                    if current_time - self.last_inference_time < self.min_inference_interval:
                        # 跳过推理，使用假结果
                        detected, confidence = False, 0.0
                        detection_time = 0.0
                        self.performance_stats['skipped_detections'] += 1
                    else:
                        # 音频能量预筛选
                        audio_energy = np.mean(np.abs(window))
                        if audio_energy < self.energy_threshold:
                            # 音频能量太低，跳过推理
                            detected, confidence = False, 0.0
                            detection_time = 0.0
                            self.performance_stats['skipped_detections'] += 1
                        else:
                            # 执行真实检测
                            detection_start_time = time.time()
                            detected, confidence = self.detector.detect(window)
                            detection_time = time.time() - detection_start_time
                            self.last_inference_time = current_time

                    # 更新详细性能统计
                    self.detection_count += 1
                    self.performance_stats['windows_processed'] += 1
                    self.last_detection_time = time.time()
                    processed_windows += 1

                    # 只有真实推理才计入total_detections和detection_time
                    if detection_time > 0:
                        self.performance_stats['total_detections'] += 1
                        self.performance_stats['total_detection_time'] += detection_time

                    # 只在检测到唤醒词时输出
                    if detected:
                        print(f"🎯 检测到唤醒词! 置信度: {confidence:.4f} (阈值: {self.detector.confidence_threshold})")

                    # 处理检测结果
                    if detected:
                        await self._handle_wake_word_detected(confidence, detection_time)
                        break  # 检测到后立即退出循环

                # 极致休眠时间优化，大幅减少CPU消耗
                await asyncio.sleep(0.1)  # 增加休眠时间，显著降低CPU使用率

        except asyncio.CancelledError:
            self.logger.info("检测循环被取消")
        except Exception as e:
            self.logger.error(f"检测循环异常: {e}")

    async def _handle_wake_word_detected(self, confidence: float, detection_time: float) -> None:
        """处理唤醒词检测到的情况"""
        try:
            current_time = time.time()

            # 防抖动检查
            if current_time - self.last_wake_time < self.cooldown_period:
                return

            self.last_wake_time = current_time
            self.wake_word_count += 1

            # 记录日志
            self.logger.info(
                f"检测到唤醒词 '{self.keyword}' - "
                f"置信度: {confidence:.4f}, "
                f"检测时间: {detection_time*1000:.1f}ms"
            )

            # 发布唤醒词事件
            await self.event_bus.emit(
                "wake_word_detected",
                {
                    "keyword": self.keyword,
                    "confidence": confidence,
                    "detection_time": detection_time,
                    "timestamp": current_time
                },
                priority=EventPriority.CRITICAL
            )

            # 调用回调函数
            for callback in self.detection_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(self.keyword, confidence)
                    else:
                        callback(self.keyword, confidence)
                except Exception as e:
                    self.logger.error(f"回调函数执行失败: {e}")

        except Exception as e:
            self.logger.error(f"处理唤醒词检测失败: {e}")

    def add_detection_callback(self, callback: Callable) -> None:
        """添加检测回调函数"""
        self.detection_callbacks.append(callback)

    def remove_detection_callback(self, callback: Callable) -> None:
        """移除检测回调函数"""
        if callback in self.detection_callbacks:
            self.detection_callbacks.remove(callback)

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "enabled": self.enabled,
            "is_detecting": self.is_detecting,
            "detection_count": self.detection_count,
            "wake_word_count": self.wake_word_count,
            "avg_detection_time": self.avg_detection_time,
            "last_detection_time": self.last_detection_time,
            "last_wake_time": self.last_wake_time,
            "model_info": self.detector.get_model_info() if self.detector.is_loaded else None,
            "buffer_info": self.audio_buffer.get_buffer_info()
        }

    async def test_detection(self, audio_data: np.ndarray) -> Tuple[bool, float]:
        """测试检测功能"""
        if not self.detector.is_loaded:
            raise RuntimeError("模型未加载")

        return self.detector.detect(audio_data)

    def _print_performance_stats(self):
        """打印详细性能统计"""
        try:
            current_time = time.time()
            elapsed_time = current_time - self.performance_stats['start_time']

            if elapsed_time > 0:
                # 计算各种速率
                loops_per_sec = self.performance_stats['loop_iterations'] / elapsed_time
                detections_per_sec = self.performance_stats['total_detections'] / elapsed_time
                detection_ratio = (self.performance_stats['total_detections'] /
                                 max(self.performance_stats['loop_iterations'], 1)) * 100

                # 计算平均耗时
                avg_detection_time = (self.performance_stats['total_detection_time'] /
                                    max(self.performance_stats['total_detections'], 1)) * 1000
                avg_audio_time = (self.performance_stats['audio_processing_time'] /
                                max(self.performance_stats['loop_iterations'], 1)) * 1000

                # 计算跳过的推理统计
                skipped_ratio = (self.performance_stats['skipped_detections'] /
                               max(self.performance_stats['loop_iterations'], 1)) * 100
                actual_inferences = self.performance_stats['total_detections']
                actual_inference_rate = actual_inferences / elapsed_time

                print(f"📊 性能统计 (运行{elapsed_time:.1f}s):")
                print(f"   🔄 循环: {self.performance_stats['loop_iterations']}次 ({loops_per_sec:.1f}/s)")
                print(f"   🎯 实际推理: {actual_inferences}次 ({actual_inference_rate:.1f}/s)")
                print(f"   ⏭️  跳过推理: {self.performance_stats['skipped_detections']}次 ({skipped_ratio:.1f}%)")
                print(f"   ⏱️  平均推理耗时: {avg_detection_time:.2f}ms")
                print(f"   🎵 平均音频处理: {avg_audio_time:.2f}ms")
                print(f"   🏆 唤醒成功: {self.wake_word_count}次")

        except Exception as e:
            print(f"❌ 性能统计错误: {e}")

    # VAD事件处理方法已移除，回到原始架构
