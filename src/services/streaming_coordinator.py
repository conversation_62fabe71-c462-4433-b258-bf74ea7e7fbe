#!/usr/bin/env python3
"""
流式协调器
协调LLM响应和TTS合成的流式处理，优化端到端延迟
"""

import asyncio
import time
import logging
import re
from typing import Dict, Any, Optional, List, AsyncGenerator, Callable
from dataclasses import dataclass, field
from enum import Enum
from collections import deque
import threading
from concurrent.futures import ThreadPoolExecutor

from .base_service import BaseService, ServiceStatus
from .event_bus import EventBus, EventPriority
from ..interfaces.llm_interface import LLMMessage, LLMResponse


class StreamingState(Enum):
    """流式处理状态"""
    IDLE = "idle"
    LLM_STREAMING = "llm_streaming"
    TTS_PROCESSING = "tts_processing"
    PARALLEL_PROCESSING = "parallel_processing"
    COMPLETING = "completing"
    ERROR = "error"


class ChunkType(Enum):
    """数据块类型"""
    TEXT = "text"
    SENTENCE = "sentence"
    PARAGRAPH = "paragraph"
    FINAL = "final"


@dataclass
class StreamChunk:
    """流式数据块"""
    content: str
    chunk_type: ChunkType
    sequence_id: int
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)
    is_final: bool = False


@dataclass
class StreamingSession:
    """流式处理会话"""
    session_id: str
    state: StreamingState = StreamingState.IDLE
    start_time: float = field(default_factory=time.time)
    llm_chunks: deque = field(default_factory=deque)
    tts_queue: asyncio.Queue = field(default_factory=lambda: asyncio.Queue())
    processed_chunks: List[StreamChunk] = field(default_factory=list)
    total_llm_tokens: int = 0
    total_tts_duration: float = 0.0
    first_chunk_time: Optional[float] = None
    completion_time: Optional[float] = None
    error_count: int = 0


@dataclass
class StreamingConfig:
    """流式协调器配置"""
    # 缓冲区配置
    llm_buffer_size: int = 50  # LLM缓冲区大小
    tts_buffer_size: int = 10  # TTS缓冲区大小
    chunk_size_threshold: int = 30  # 块大小阈值（字符数）- 增加以减少块数量

    # 延迟优化配置
    first_chunk_delay: float = 0.05  # 首块延迟（秒）- 减少延迟
    parallel_threshold: int = 15  # 并行处理阈值 - 增加以减少频繁切换
    sentence_boundary_delay: float = 0.02  # 句子边界延迟 - 减少延迟

    # 连续播放优化配置
    enable_continuous_playback: bool = True  # 启用连续播放
    audio_buffer_duration: float = 2.0  # 音频缓冲时长（秒）
    preload_next_chunk: bool = True  # 预加载下一个块
    smooth_transitions: bool = True  # 平滑过渡
    
    # 文本处理配置
    enable_smart_chunking: bool = True  # 启用智能分块
    min_chunk_length: int = 15  # 最小块长度 - 增加以减少块数量
    max_chunk_length: int = 150  # 最大块长度 - 增加以减少块数量
    sentence_endings: List[str] = field(default_factory=lambda: ['。', '！', '？', '.', '!', '?'])

    # 连贯性优化配置
    merge_short_chunks: bool = True  # 合并短块
    buffer_threshold_seconds: float = 1.5  # 缓冲阈值（秒）
    enable_chunk_preloading: bool = True  # 启用块预加载
    
    # 性能配置
    max_concurrent_tts: int = 3  # 最大并发TTS任务
    processing_timeout: float = 30.0  # 处理超时时间
    cleanup_interval: float = 60.0  # 清理间隔
    
    # 错误处理配置
    max_retry_count: int = 3  # 最大重试次数
    error_recovery_delay: float = 1.0  # 错误恢复延迟


class StreamingCoordinator(BaseService):
    """流式协调器"""
    
    def __init__(self, config: Dict[str, Any], event_bus: EventBus):
        super().__init__("streaming_coordinator", config)
        self.event_bus = event_bus
        
        # 解析配置
        streaming_config = config.get('streaming_coordinator', {})
        self.streaming_config = StreamingConfig(
            llm_buffer_size=streaming_config.get('llm_buffer_size', 50),
            tts_buffer_size=streaming_config.get('tts_buffer_size', 10),
            chunk_size_threshold=streaming_config.get('chunk_size_threshold', 20),
            first_chunk_delay=streaming_config.get('first_chunk_delay', 0.1),
            parallel_threshold=streaming_config.get('parallel_threshold', 10),
            sentence_boundary_delay=streaming_config.get('sentence_boundary_delay', 0.05),
            enable_smart_chunking=streaming_config.get('enable_smart_chunking', True),
            min_chunk_length=streaming_config.get('min_chunk_length', 5),
            max_chunk_length=streaming_config.get('max_chunk_length', 100),
            max_concurrent_tts=streaming_config.get('max_concurrent_tts', 3),
            processing_timeout=streaming_config.get('processing_timeout', 30.0),
            cleanup_interval=streaming_config.get('cleanup_interval', 60.0),
            max_retry_count=streaming_config.get('max_retry_count', 3),
            error_recovery_delay=streaming_config.get('error_recovery_delay', 1.0)
        )
        
        # 状态管理
        self.active_sessions: Dict[str, StreamingSession] = {}
        self.is_processing = False
        
        # 处理器和任务管理
        self.text_processor = TextProcessor(self.streaming_config)
        self.tts_semaphore = asyncio.Semaphore(self.streaming_config.max_concurrent_tts)
        self.executor = ThreadPoolExecutor(max_workers=4)

        # 连续音频播放器（使用SoundDevice）
        self.continuous_player = None
        if self.streaming_config.enable_continuous_playback:
            from ..infrastructure.sounddevice_audio_player import SoundDeviceAudioPlayer
            from ..interfaces.audio_interface import AudioConfig
            audio_config = AudioConfig(
                sample_rate=16000,
                channels=1,
                chunk_size=1024,
                output_device_id=None
            )
            self.continuous_player = SoundDeviceAudioPlayer(audio_config)
        
        # 任务管理
        self.processing_tasks: Dict[str, asyncio.Task] = {}
        self.cleanup_task: Optional[asyncio.Task] = None
        
        # 性能统计
        self.total_sessions = 0
        self.successful_sessions = 0
        self.average_first_chunk_latency = 0.0
        self.average_total_latency = 0.0
        
        # 回调管理
        self.chunk_callbacks: List[Callable] = []
        self.completion_callbacks: List[Callable] = []
        
    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            # 订阅相关事件
            await self.event_bus.subscribe("llm_response_chunk", self._on_llm_chunk)
            await self.event_bus.subscribe("llm_response_complete", self._on_llm_complete)
            await self.event_bus.subscribe("tts_chunk_complete", self._on_tts_chunk_complete)
            await self.event_bus.subscribe("streaming_session_start", self._on_session_start)
            await self.event_bus.subscribe("streaming_session_stop", self._on_session_stop)
            
            self.logger.info("流式协调器初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"流式协调器初始化失败: {e}")
            return False
    
    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            # 启动清理任务
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())
            
            self.is_processing = True
            self.logger.info("流式协调器已启动")
            return True
            
        except Exception as e:
            self.logger.error(f"流式协调器启动失败: {e}")
            return False
    
    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            self.is_processing = False
            
            # 停止所有处理任务
            for task in self.processing_tasks.values():
                task.cancel()
            
            # 等待任务完成
            if self.processing_tasks:
                await asyncio.gather(*self.processing_tasks.values(), return_exceptions=True)
            
            # 停止清理任务
            if self.cleanup_task:
                self.cleanup_task.cancel()
                try:
                    await self.cleanup_task
                except asyncio.CancelledError:
                    pass
            
            # 关闭线程池
            self.executor.shutdown(wait=True)
            
            self.logger.info("流式协调器已停止")
            return True
            
        except Exception as e:
            self.logger.error(f"流式协调器停止失败: {e}")
            return False
    
    async def _health_check_impl(self) -> bool:
        """健康检查实现"""
        try:
            # 检查活跃会话数量
            if len(self.active_sessions) > 100:  # 防止内存泄漏
                self.logger.warning(f"活跃会话数量过多: {len(self.active_sessions)}")
                return False
            
            # 检查处理任务状态
            failed_tasks = [task_id for task_id, task in self.processing_tasks.items() 
                           if task.done() and task.exception()]
            
            if failed_tasks:
                self.logger.warning(f"发现失败的处理任务: {failed_tasks}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"流式协调器健康检查失败: {e}")
            return False

    # 事件处理方法
    async def _on_llm_chunk(self, event_data: Dict[str, Any]):
        """处理LLM响应块事件"""
        try:
            session_id = event_data.get('session_id')
            content = event_data.get('content', '')
            is_final = event_data.get('is_final', False)

            if not session_id or not content:
                return

            session = self.active_sessions.get(session_id)
            if not session:
                # 创建新会话
                session = await self._create_session(session_id)

            # 记录首块时间
            if session.first_chunk_time is None:
                session.first_chunk_time = time.time()

            # 处理LLM块
            await self._process_llm_chunk(session, content, is_final)

        except Exception as e:
            self.logger.error(f"处理LLM块事件失败: {e}")

    async def _on_llm_complete(self, event_data: Dict[str, Any]):
        """处理LLM响应完成事件"""
        try:
            session_id = event_data.get('session_id')
            if not session_id:
                return

            session = self.active_sessions.get(session_id)
            if session:
                session.state = StreamingState.COMPLETING
                await self._finalize_session(session)

        except Exception as e:
            self.logger.error(f"处理LLM完成事件失败: {e}")

    async def _on_tts_chunk_complete(self, event_data: Dict[str, Any]):
        """处理TTS块完成事件"""
        try:
            session_id = event_data.get('session_id')
            chunk_id = event_data.get('chunk_id')
            duration = event_data.get('duration', 0.0)

            if not session_id:
                return

            session = self.active_sessions.get(session_id)
            if session:
                session.total_tts_duration += duration

        except Exception as e:
            self.logger.error(f"处理TTS块完成事件失败: {e}")

    async def _on_session_start(self, event_data: Dict[str, Any]):
        """处理会话开始事件"""
        try:
            session_id = event_data.get('session_id')
            if session_id:
                await self._create_session(session_id)

        except Exception as e:
            self.logger.error(f"处理会话开始事件失败: {e}")

    async def _on_session_stop(self, event_data: Dict[str, Any]):
        """处理会话停止事件"""
        try:
            session_id = event_data.get('session_id')
            if session_id and session_id in self.active_sessions:
                await self._cleanup_session(session_id)

        except Exception as e:
            self.logger.error(f"处理会话停止事件失败: {e}")

    # 核心协调方法
    async def _create_session(self, session_id: str) -> StreamingSession:
        """创建流式处理会话"""
        try:
            session = StreamingSession(session_id=session_id)
            self.active_sessions[session_id] = session
            self.total_sessions += 1

            # 启动会话处理任务
            self.processing_tasks[session_id] = asyncio.create_task(
                self._session_processing_loop(session)
            )

            self.logger.info(f"创建流式会话: {session_id}")
            return session

        except Exception as e:
            self.logger.error(f"创建会话失败: {e}")
            raise

    async def _process_llm_chunk(self, session: StreamingSession, content: str, is_final: bool):
        """处理LLM响应块"""
        try:
            session.state = StreamingState.LLM_STREAMING
            session.total_llm_tokens += len(content.split())

            # 智能分块处理
            if self.streaming_config.enable_smart_chunking:
                chunks = self.text_processor.smart_chunk(content, is_final)
            else:
                chunks = [StreamChunk(
                    content=content,
                    chunk_type=ChunkType.FINAL if is_final else ChunkType.TEXT,
                    sequence_id=len(session.processed_chunks),
                    is_final=is_final
                )]

            # 添加到处理队列
            for chunk in chunks:
                session.llm_chunks.append(chunk)
                await session.tts_queue.put(chunk)

            self.logger.debug(f"处理LLM块: {len(chunks)}个子块, 会话: {session.session_id}")

        except Exception as e:
            self.logger.error(f"处理LLM块失败: {e}")
            session.error_count += 1

    async def _session_processing_loop(self, session: StreamingSession):
        """会话处理循环"""
        try:
            while self.is_processing and session.state != StreamingState.COMPLETING:
                try:
                    # 等待TTS任务
                    chunk = await asyncio.wait_for(
                        session.tts_queue.get(),
                        timeout=self.streaming_config.processing_timeout
                    )

                    # 处理TTS合成
                    await self._process_tts_chunk(session, chunk)

                except asyncio.TimeoutError:
                    # 超时检查
                    if time.time() - session.start_time > self.streaming_config.processing_timeout:
                        self.logger.warning(f"会话处理超时: {session.session_id}")
                        break
                    continue

                except Exception as e:
                    self.logger.error(f"会话处理循环错误: {e}")
                    session.error_count += 1
                    if session.error_count > self.streaming_config.max_retry_count:
                        break
                    await asyncio.sleep(self.streaming_config.error_recovery_delay)

        except asyncio.CancelledError:
            self.logger.debug(f"会话处理循环被取消: {session.session_id}")
        except Exception as e:
            self.logger.error(f"会话处理循环异常: {e}")
        finally:
            # 清理会话
            await self._cleanup_session(session.session_id)

    async def _process_tts_chunk(self, session: StreamingSession, chunk: StreamChunk):
        """处理TTS合成块"""
        try:
            async with self.tts_semaphore:  # 限制并发TTS任务
                session.state = StreamingState.TTS_PROCESSING

                # 发送TTS合成事件
                await self.event_bus.emit(
                    "text_to_synthesize",
                    {
                        "text": chunk.content,
                        "session_id": session.session_id,
                        "chunk_id": chunk.sequence_id,
                        "streaming": True,
                        "priority": "high" if chunk.is_final else "normal"
                    },
                    priority=EventPriority.HIGH if chunk.is_final else EventPriority.NORMAL
                )

                # 记录处理的块
                session.processed_chunks.append(chunk)

                # 调用回调
                for callback in self.chunk_callbacks:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(session, chunk)
                        else:
                            callback(session, chunk)
                    except Exception as e:
                        self.logger.error(f"块回调执行失败: {e}")

                self.logger.debug(f"处理TTS块: {chunk.sequence_id}, 内容: {chunk.content[:50]}...")

        except Exception as e:
            self.logger.error(f"处理TTS块失败: {e}")
            session.error_count += 1

    async def _finalize_session(self, session: StreamingSession):
        """完成会话处理"""
        try:
            session.completion_time = time.time()
            session.state = StreamingState.COMPLETING

            # 等待所有TTS任务完成
            while not session.tts_queue.empty():
                try:
                    chunk = await asyncio.wait_for(session.tts_queue.get(), timeout=1.0)
                    await self._process_tts_chunk(session, chunk)
                except asyncio.TimeoutError:
                    break

            # 计算统计信息
            total_latency = session.completion_time - session.start_time
            first_chunk_latency = (session.first_chunk_time - session.start_time
                                 if session.first_chunk_time else 0.0)

            # 更新全局统计
            self.successful_sessions += 1
            self.average_first_chunk_latency = (
                (self.average_first_chunk_latency * (self.successful_sessions - 1) + first_chunk_latency)
                / self.successful_sessions
            )
            self.average_total_latency = (
                (self.average_total_latency * (self.successful_sessions - 1) + total_latency)
                / self.successful_sessions
            )

            # 发送完成事件
            await self.event_bus.emit(
                "streaming_session_complete",
                {
                    "session_id": session.session_id,
                    "total_latency": total_latency,
                    "first_chunk_latency": first_chunk_latency,
                    "total_chunks": len(session.processed_chunks),
                    "total_tokens": session.total_llm_tokens,
                    "total_tts_duration": session.total_tts_duration,
                    "error_count": session.error_count
                }
            )

            # 调用完成回调
            for callback in self.completion_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(session)
                    else:
                        callback(session)
                except Exception as e:
                    self.logger.error(f"完成回调执行失败: {e}")

            self.logger.info(f"会话完成: {session.session_id}, 延迟: {total_latency:.2f}s")

        except Exception as e:
            self.logger.error(f"完成会话失败: {e}")

    async def _cleanup_session(self, session_id: str):
        """清理会话"""
        try:
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]

            if session_id in self.processing_tasks:
                task = self.processing_tasks[session_id]
                if not task.done():
                    task.cancel()
                del self.processing_tasks[session_id]

            self.logger.debug(f"清理会话: {session_id}")

        except Exception as e:
            self.logger.error(f"清理会话失败: {e}")

    async def _cleanup_loop(self):
        """清理循环"""
        try:
            while self.is_processing:
                try:
                    current_time = time.time()
                    expired_sessions = []

                    # 查找过期会话
                    for session_id, session in self.active_sessions.items():
                        if (current_time - session.start_time > self.streaming_config.processing_timeout * 2):
                            expired_sessions.append(session_id)

                    # 清理过期会话
                    for session_id in expired_sessions:
                        self.logger.warning(f"清理过期会话: {session_id}")
                        await self._cleanup_session(session_id)

                    # 等待下次清理
                    await asyncio.sleep(self.streaming_config.cleanup_interval)

                except Exception as e:
                    self.logger.error(f"清理循环错误: {e}")
                    await asyncio.sleep(10.0)

        except asyncio.CancelledError:
            self.logger.debug("清理循环被取消")
        except Exception as e:
            self.logger.error(f"清理循环异常: {e}")

    # 公共API方法
    async def start_streaming_session(self, session_id: str) -> bool:
        """启动流式处理会话"""
        try:
            if session_id in self.active_sessions:
                self.logger.warning(f"会话已存在: {session_id}")
                return False

            await self._create_session(session_id)

            # 发送会话开始事件
            await self.event_bus.emit(
                "streaming_session_started",
                {
                    "session_id": session_id,
                    "timestamp": time.time()
                }
            )

            return True

        except Exception as e:
            self.logger.error(f"启动流式会话失败: {e}")
            return False

    async def stop_streaming_session(self, session_id: str) -> bool:
        """停止流式处理会话"""
        try:
            if session_id not in self.active_sessions:
                self.logger.warning(f"会话不存在: {session_id}")
                return False

            session = self.active_sessions[session_id]
            await self._finalize_session(session)
            await self._cleanup_session(session_id)

            return True

        except Exception as e:
            self.logger.error(f"停止流式会话失败: {e}")
            return False

    def add_chunk_callback(self, callback: Callable):
        """添加块处理回调"""
        self.chunk_callbacks.append(callback)

    def remove_chunk_callback(self, callback: Callable):
        """移除块处理回调"""
        try:
            self.chunk_callbacks.remove(callback)
        except ValueError:
            pass

    def add_completion_callback(self, callback: Callable):
        """添加完成回调"""
        self.completion_callbacks.append(callback)

    def remove_completion_callback(self, callback: Callable):
        """移除完成回调"""
        try:
            self.completion_callbacks.remove(callback)
        except ValueError:
            pass

    def get_streaming_statistics(self) -> Dict[str, Any]:
        """获取流式处理统计信息"""
        try:
            active_sessions_info = {}
            for session_id, session in self.active_sessions.items():
                active_sessions_info[session_id] = {
                    "state": session.state.value,
                    "duration": time.time() - session.start_time,
                    "chunks_processed": len(session.processed_chunks),
                    "tokens": session.total_llm_tokens,
                    "tts_duration": session.total_tts_duration,
                    "errors": session.error_count
                }

            return {
                "total_sessions": self.total_sessions,
                "successful_sessions": self.successful_sessions,
                "active_sessions": len(self.active_sessions),
                "active_sessions_info": active_sessions_info,
                "average_first_chunk_latency": self.average_first_chunk_latency,
                "average_total_latency": self.average_total_latency,
                "processing_tasks": len(self.processing_tasks),
                "is_processing": self.is_processing,
                "config": {
                    "llm_buffer_size": self.streaming_config.llm_buffer_size,
                    "tts_buffer_size": self.streaming_config.tts_buffer_size,
                    "chunk_size_threshold": self.streaming_config.chunk_size_threshold,
                    "first_chunk_delay": self.streaming_config.first_chunk_delay,
                    "parallel_threshold": self.streaming_config.parallel_threshold,
                    "max_concurrent_tts": self.streaming_config.max_concurrent_tts,
                    "enable_smart_chunking": self.streaming_config.enable_smart_chunking
                }
            }

        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}

    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话信息"""
        try:
            session = self.active_sessions.get(session_id)
            if not session:
                return None

            return {
                "session_id": session.session_id,
                "state": session.state.value,
                "start_time": session.start_time,
                "duration": time.time() - session.start_time,
                "first_chunk_time": session.first_chunk_time,
                "completion_time": session.completion_time,
                "llm_chunks_count": len(session.llm_chunks),
                "processed_chunks_count": len(session.processed_chunks),
                "total_llm_tokens": session.total_llm_tokens,
                "total_tts_duration": session.total_tts_duration,
                "error_count": session.error_count,
                "tts_queue_size": session.tts_queue.qsize()
            }

        except Exception as e:
            self.logger.error(f"获取会话信息失败: {e}")
            return None


class TextProcessor:
    """文本处理器 - 智能分块和优化"""

    def __init__(self, config: StreamingConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # 编译正则表达式
        self.sentence_pattern = re.compile(r'[。！？.!?]+')
        self.punctuation_pattern = re.compile(r'[，、；：,;:]+')

    def smart_chunk(self, text: str, is_final: bool = False) -> List[StreamChunk]:
        """智能分块处理 - 优化连贯性"""
        try:
            chunks = []
            sequence_id = 0

            if not text.strip():
                return chunks

            # 如果文本很短，直接返回
            if len(text) <= self.config.min_chunk_length:
                return [StreamChunk(
                    content=text,
                    chunk_type=ChunkType.FINAL if is_final else ChunkType.TEXT,
                    sequence_id=sequence_id,
                    is_final=is_final
                )]

            # 按句子分割
            sentences = self._split_by_sentences(text)

            # 合并短句子以减少块数量
            if self.config.merge_short_chunks:
                sentences = self._merge_short_sentences(sentences)

            current_chunk = ""
            for i, sentence in enumerate(sentences):
                # 更智能的分块策略
                potential_chunk = current_chunk + sentence

                # 检查是否需要创建新块
                should_create_chunk = (
                    len(potential_chunk) > self.config.max_chunk_length and
                    len(current_chunk) >= self.config.min_chunk_length
                ) or (
                    # 在句子边界处分块，但确保块足够大
                    self._is_natural_break_point(sentence) and
                    len(current_chunk) >= self.config.min_chunk_length * 1.5
                )

                if should_create_chunk:
                    # 创建当前块
                    if current_chunk.strip():
                        chunks.append(StreamChunk(
                            content=current_chunk.strip(),
                            chunk_type=ChunkType.SENTENCE,
                            sequence_id=sequence_id,
                            is_final=False
                        ))
                        sequence_id += 1
                        current_chunk = sentence
                else:
                    current_chunk = potential_chunk

            # 处理最后一块
            if current_chunk.strip():
                chunks.append(StreamChunk(
                    content=current_chunk.strip(),
                    chunk_type=ChunkType.FINAL if is_final else ChunkType.SENTENCE,
                    sequence_id=sequence_id,
                    is_final=is_final
                ))

            # 后处理：合并过小的块
            if len(chunks) > 1:
                chunks = self._post_process_chunks(chunks)

            return chunks

        except Exception as e:
            self.logger.error(f"智能分块失败: {e}")
            # 返回原始文本作为单个块
            return [StreamChunk(
                content=text,
                chunk_type=ChunkType.FINAL if is_final else ChunkType.TEXT,
                sequence_id=0,
                is_final=is_final
            )]

    def _split_by_sentences(self, text: str) -> List[str]:
        """按句子分割文本"""
        try:
            # 首先按句号等强分隔符分割
            sentences = []
            current_pos = 0

            for match in self.sentence_pattern.finditer(text):
                # 添加句子内容和标点
                sentence = text[current_pos:match.end()]
                if sentence.strip():
                    sentences.append(sentence)
                current_pos = match.end()

            # 添加剩余文本
            if current_pos < len(text):
                remaining = text[current_pos:]
                if remaining.strip():
                    sentences.append(remaining)

            # 如果没有找到句子分隔符，按逗号等弱分隔符分割
            if len(sentences) <= 1 and len(text) > self.config.max_chunk_length:
                sentences = self._split_by_punctuation(text)

            return sentences if sentences else [text]

        except Exception as e:
            self.logger.error(f"句子分割失败: {e}")
            return [text]

    def _split_by_punctuation(self, text: str) -> List[str]:
        """按标点符号分割文本"""
        try:
            parts = []
            current_pos = 0

            for match in self.punctuation_pattern.finditer(text):
                # 添加内容和标点
                part = text[current_pos:match.end()]
                if part.strip():
                    parts.append(part)
                current_pos = match.end()

            # 添加剩余文本
            if current_pos < len(text):
                remaining = text[current_pos:]
                if remaining.strip():
                    parts.append(remaining)

            return parts if parts else [text]

        except Exception as e:
            self.logger.error(f"标点分割失败: {e}")
            return [text]

    def optimize_for_tts(self, text: str) -> str:
        """为TTS优化文本"""
        try:
            # 移除多余的空白字符
            text = re.sub(r'\s+', ' ', text.strip())

            # 处理数字（可以扩展为更复杂的数字读音转换）
            text = re.sub(r'\d+', lambda m: self._number_to_text(m.group()), text)

            # 处理特殊符号
            text = text.replace('&', '和')
            text = text.replace('%', '百分之')
            text = text.replace('@', '在')

            return text

        except Exception as e:
            self.logger.error(f"TTS文本优化失败: {e}")
            return text

    def _number_to_text(self, number_str: str) -> str:
        """数字转文本（简化版）"""
        try:
            # 这里可以实现更复杂的数字转换逻辑
            # 目前只是简单处理
            num = int(number_str)
            if num < 10:
                return number_str
            elif num < 100:
                return number_str
            else:
                return number_str  # 保持原样，让TTS处理

        except ValueError:
            return number_str

    def _merge_short_sentences(self, sentences: List[str]) -> List[str]:
        """合并短句子"""
        try:
            if not sentences:
                return sentences

            merged = []
            current = ""

            for sentence in sentences:
                if len(current + sentence) <= self.config.min_chunk_length * 2:
                    current += sentence
                else:
                    if current:
                        merged.append(current)
                    current = sentence

            if current:
                merged.append(current)

            return merged if merged else sentences

        except Exception as e:
            self.logger.error(f"合并短句子失败: {e}")
            return sentences

    def _is_natural_break_point(self, sentence: str) -> bool:
        """判断是否为自然断点"""
        try:
            # 检查句子是否以强分隔符结尾
            strong_endings = ['。', '！', '？', '.', '!', '?']
            return any(sentence.rstrip().endswith(ending) for ending in strong_endings)

        except Exception as e:
            self.logger.error(f"判断自然断点失败: {e}")
            return False

    def _post_process_chunks(self, chunks: List[StreamChunk]) -> List[StreamChunk]:
        """后处理块：合并过小的块"""
        try:
            if len(chunks) <= 1:
                return chunks

            processed = []
            i = 0

            while i < len(chunks):
                current_chunk = chunks[i]

                # 如果当前块太小且不是最后一块，尝试与下一块合并
                if (i < len(chunks) - 1 and
                    len(current_chunk.content) < self.config.min_chunk_length):

                    next_chunk = chunks[i + 1]
                    merged_content = current_chunk.content + next_chunk.content

                    # 如果合并后不会太大，则合并
                    if len(merged_content) <= self.config.max_chunk_length:
                        merged_chunk = StreamChunk(
                            content=merged_content,
                            chunk_type=next_chunk.chunk_type,
                            sequence_id=len(processed),
                            is_final=next_chunk.is_final
                        )
                        processed.append(merged_chunk)
                        i += 2  # 跳过下一块
                        continue

                # 更新序列ID
                current_chunk.sequence_id = len(processed)
                processed.append(current_chunk)
                i += 1

            return processed

        except Exception as e:
            self.logger.error(f"后处理块失败: {e}")
            return chunks
