#!/usr/bin/env python3
"""
TTS打断处理器
专门处理TTS播放过程中的用户打断，实现智能停止和恢复机制
"""

import asyncio
import time
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from enum import Enum

from .base_service import BaseService, ServiceStatus
from .event_bus import EventBus, EventPriority
from ..interfaces.interruption_interface import InterruptionResult, InterruptionHandlerInterface


class TTSInterruptionMode(Enum):
    """TTS打断模式"""
    IMMEDIATE = "immediate"      # 立即停止
    FADE_OUT = "fade_out"       # 淡出停止
    SENTENCE_END = "sentence_end" # 句子结束后停止
    SMART = "smart"             # 智能选择


@dataclass
class TTSInterruptionContext:
    """TTS打断上下文"""
    session_id: str
    interrupted_text: str = ""
    interrupted_position: int = 0
    interruption_time: float = field(default_factory=time.time)
    tts_start_time: float = 0.0
    resume_text: str = ""
    interruption_reason: str = ""
    can_resume: bool = True


class TTSInterruptionHandler(BaseService, InterruptionHandlerInterface):
    """TTS打断处理器"""
    
    def __init__(self, config: Dict[str, Any], event_bus: EventBus):
        super().__init__("tts_interruption_handler", config)
        self.event_bus = event_bus
        
        # 配置参数
        interruption_config = config.get('tts_interruption', {})
        self.interruption_mode = TTSInterruptionMode(
            interruption_config.get('mode', 'smart')
        )
        self.fade_duration = interruption_config.get('fade_duration', 0.5)
        self.resume_enabled = interruption_config.get('resume_enabled', True)
        self.resume_timeout = interruption_config.get('resume_timeout', 10.0)
        self.smart_threshold = interruption_config.get('smart_threshold', 0.7)
        
        # 状态管理
        self.current_tts_session: Optional[str] = None
        self.is_tts_playing = False
        self.interruption_contexts: Dict[str, TTSInterruptionContext] = {}
        self.interruption_history: List[InterruptionResult] = []
        
        # 任务管理
        self.fade_task: Optional[asyncio.Task] = None
        self.resume_tasks: Dict[str, asyncio.Task] = {}
        
    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            # 订阅相关事件
            await self.event_bus.subscribe("tts_started", self._on_tts_started)
            await self.event_bus.subscribe("tts_finished", self._on_tts_finished)
            await self.event_bus.subscribe("tts_audio_chunk", self._on_tts_audio_chunk)
            await self.event_bus.subscribe("interruption_detected", self._on_interruption_detected)
            
            self.logger.info("TTS打断处理器初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"TTS打断处理器初始化失败: {e}")
            return False
    
    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            self.logger.info("TTS打断处理器已启动")
            return True
            
        except Exception as e:
            self.logger.error(f"TTS打断处理器启动失败: {e}")
            return False
    
    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            # 取消所有任务
            if self.fade_task:
                self.fade_task.cancel()
            
            for task in self.resume_tasks.values():
                task.cancel()
            
            self.logger.info("TTS打断处理器已停止")
            return True
            
        except Exception as e:
            self.logger.error(f"TTS打断处理器停止失败: {e}")
            return False
    
    async def _health_check_impl(self) -> bool:
        """健康检查实现"""
        try:
            return True
        except Exception as e:
            self.logger.error(f"TTS打断处理器健康检查失败: {e}")
            return False
    
    # 事件处理方法
    async def _on_tts_started(self, event_data: Dict[str, Any]):
        """处理TTS开始事件"""
        try:
            self.is_tts_playing = True
            self.current_tts_session = event_data.get('session_id', 'default')
            
            # 创建打断上下文
            if self.current_tts_session not in self.interruption_contexts:
                self.interruption_contexts[self.current_tts_session] = TTSInterruptionContext(
                    session_id=self.current_tts_session,
                    interrupted_text=event_data.get('text', ''),
                    tts_start_time=time.time()
                )
            
            self.logger.debug(f"TTS开始播放: {self.current_tts_session}")
            
        except Exception as e:
            self.logger.error(f"处理TTS开始事件失败: {e}")
    
    async def _on_tts_finished(self, event_data: Dict[str, Any]):
        """处理TTS完成事件"""
        try:
            self.is_tts_playing = False
            session_id = event_data.get('session_id', self.current_tts_session)
            
            # 清理上下文
            if session_id in self.interruption_contexts:
                del self.interruption_contexts[session_id]
            
            # 清理恢复任务
            if session_id in self.resume_tasks:
                self.resume_tasks[session_id].cancel()
                del self.resume_tasks[session_id]
            
            self.current_tts_session = None
            self.logger.debug(f"TTS播放完成: {session_id}")
            
        except Exception as e:
            self.logger.error(f"处理TTS完成事件失败: {e}")
    
    async def _on_tts_audio_chunk(self, event_data: Dict[str, Any]):
        """处理TTS音频块事件"""
        try:
            # 更新播放位置信息（如果可用）
            session_id = event_data.get('session_id', self.current_tts_session)
            if session_id in self.interruption_contexts:
                context = self.interruption_contexts[session_id]
                # 这里可以根据音频块信息更新播放位置
                # context.interrupted_position = ...
                
        except Exception as e:
            self.logger.error(f"处理TTS音频块事件失败: {e}")
    
    async def _on_interruption_detected(self, event_data: Dict[str, Any]):
        """处理打断检测事件"""
        try:
            if not self.is_tts_playing:
                return
            
            # 创建打断结果
            from ..interfaces.interruption_interface import InterruptionResult, InterruptionType, InterruptionSeverity
            
            interruption_result = InterruptionResult(
                detected=True,
                type=InterruptionType(event_data.get('type', 'speech_based')),
                severity=InterruptionSeverity(event_data.get('severity', 'medium')),
                confidence=event_data.get('confidence', 0.0),
                timestamp=event_data.get('timestamp', time.time()),
                reason=event_data.get('reason', ''),
                context=event_data.get('context', {})
            )
            
            # 处理打断
            await self.handle_interruption(interruption_result)
            
        except Exception as e:
            self.logger.error(f"处理打断检测事件失败: {e}")
    
    # 接口实现
    async def handle_interruption(self, result: InterruptionResult) -> bool:
        """处理打断事件"""
        try:
            if not self.is_tts_playing or not self.current_tts_session:
                return False
            
            # 记录打断历史
            self.interruption_history.append(result)
            
            # 获取当前上下文
            context = self.interruption_contexts.get(self.current_tts_session)
            if not context:
                return False
            
            # 更新打断信息
            context.interruption_time = result.timestamp
            context.interruption_reason = result.reason
            
            # 根据模式和严重程度决定处理方式
            interruption_mode = self._determine_interruption_mode(result)
            
            self.logger.info(f"处理TTS打断: {result.reason} (模式: {interruption_mode.value})")
            
            # 执行打断处理
            success = await self._execute_interruption(interruption_mode, context, result)
            
            if success:
                # 发布打断处理完成事件
                await self.event_bus.emit(
                    "tts_interruption_handled",
                    {
                        "session_id": self.current_tts_session,
                        "interruption_mode": interruption_mode.value,
                        "can_resume": context.can_resume,
                        "timestamp": time.time()
                    },
                    priority=EventPriority.HIGH
                )
            
            return success
            
        except Exception as e:
            self.logger.error(f"处理打断失败: {e}")
            return False
    
    def _determine_interruption_mode(self, result: InterruptionResult) -> TTSInterruptionMode:
        """确定打断模式"""
        try:
            if self.interruption_mode != TTSInterruptionMode.SMART:
                return self.interruption_mode
            
            # 智能模式：根据打断严重程度和置信度决定
            if result.severity.value == "critical" or result.confidence > 0.9:
                return TTSInterruptionMode.IMMEDIATE
            elif result.severity.value == "high" or result.confidence > self.smart_threshold:
                return TTSInterruptionMode.FADE_OUT
            elif result.severity.value == "medium":
                return TTSInterruptionMode.SENTENCE_END
            else:
                return TTSInterruptionMode.FADE_OUT
                
        except Exception as e:
            self.logger.error(f"确定打断模式失败: {e}")
            return TTSInterruptionMode.IMMEDIATE

    async def _execute_interruption(self, mode: TTSInterruptionMode,
                                   context: TTSInterruptionContext,
                                   result: InterruptionResult) -> bool:
        """执行打断处理"""
        try:
            if mode == TTSInterruptionMode.IMMEDIATE:
                return await self._immediate_stop(context)
            elif mode == TTSInterruptionMode.FADE_OUT:
                return await self._fade_out_stop(context)
            elif mode == TTSInterruptionMode.SENTENCE_END:
                return await self._sentence_end_stop(context)
            else:
                return await self._immediate_stop(context)

        except Exception as e:
            self.logger.error(f"执行打断处理失败: {e}")
            return False

    async def _immediate_stop(self, context: TTSInterruptionContext) -> bool:
        """立即停止TTS"""
        try:
            # 发送TTS停止事件
            await self.event_bus.emit(
                "tts_interrupt",
                {
                    "session_id": context.session_id,
                    "mode": "immediate",
                    "timestamp": time.time()
                },
                priority=EventPriority.CRITICAL
            )

            self.logger.info("TTS立即停止")
            return True

        except Exception as e:
            self.logger.error(f"立即停止TTS失败: {e}")
            return False

    async def _fade_out_stop(self, context: TTSInterruptionContext) -> bool:
        """淡出停止TTS"""
        try:
            # 启动淡出任务
            self.fade_task = asyncio.create_task(self._fade_out_task(context))
            return True

        except Exception as e:
            self.logger.error(f"淡出停止TTS失败: {e}")
            return False

    async def _fade_out_task(self, context: TTSInterruptionContext):
        """淡出任务"""
        try:
            steps = 10
            step_duration = self.fade_duration / steps

            for i in range(steps):
                volume = 1.0 - (i / steps)

                # 发送音量调整事件
                await self.event_bus.emit(
                    "tts_volume_adjust",
                    {
                        "session_id": context.session_id,
                        "volume": volume,
                        "timestamp": time.time()
                    }
                )

                await asyncio.sleep(step_duration)

            # 最终停止
            await self._immediate_stop(context)
            self.logger.info("TTS淡出停止完成")

        except asyncio.CancelledError:
            self.logger.debug("淡出任务被取消")
        except Exception as e:
            self.logger.error(f"淡出任务失败: {e}")

    async def _sentence_end_stop(self, context: TTSInterruptionContext) -> bool:
        """句子结束后停止TTS"""
        try:
            # 发送句子结束停止事件
            await self.event_bus.emit(
                "tts_interrupt",
                {
                    "session_id": context.session_id,
                    "mode": "sentence_end",
                    "timestamp": time.time()
                },
                priority=EventPriority.HIGH
            )

            self.logger.info("TTS将在句子结束后停止")
            return True

        except Exception as e:
            self.logger.error(f"句子结束停止TTS失败: {e}")
            return False

    async def recover_from_interruption(self, context: Dict[str, Any]) -> bool:
        """从打断中恢复"""
        try:
            session_id = context.get('session_id')
            if not session_id or session_id not in self.interruption_contexts:
                return False

            interruption_context = self.interruption_contexts[session_id]

            if not interruption_context.can_resume or not self.resume_enabled:
                return False

            # 启动恢复任务
            if session_id not in self.resume_tasks:
                self.resume_tasks[session_id] = asyncio.create_task(
                    self._resume_task(interruption_context)
                )

            return True

        except Exception as e:
            self.logger.error(f"从打断中恢复失败: {e}")
            return False

    async def _resume_task(self, context: TTSInterruptionContext):
        """恢复任务"""
        try:
            # 等待恢复超时
            await asyncio.sleep(self.resume_timeout)

            # 检查是否仍然可以恢复
            if context.can_resume and context.resume_text:
                # 发送恢复TTS事件
                await self.event_bus.emit(
                    "tts_resume",
                    {
                        "session_id": context.session_id,
                        "text": context.resume_text,
                        "position": context.interrupted_position,
                        "timestamp": time.time()
                    },
                    priority=EventPriority.HIGH
                )

                self.logger.info(f"TTS恢复播放: {context.session_id}")

        except asyncio.CancelledError:
            self.logger.debug("恢复任务被取消")
        except Exception as e:
            self.logger.error(f"恢复任务失败: {e}")

    def get_interruption_history(self) -> List[InterruptionResult]:
        """获取打断历史"""
        return self.interruption_history.copy()

    def get_interruption_statistics(self) -> Dict[str, Any]:
        """获取打断统计信息"""
        try:
            recent_interruptions = [
                result for result in self.interruption_history
                if time.time() - result.timestamp < 300  # 最近5分钟
            ]

            return {
                "total_interruptions": len(self.interruption_history),
                "recent_interruptions": len(recent_interruptions),
                "active_sessions": len(self.interruption_contexts),
                "is_tts_playing": self.is_tts_playing,
                "current_session": self.current_tts_session,
                "resume_enabled": self.resume_enabled,
                "interruption_mode": self.interruption_mode.value,
                "fade_duration": self.fade_duration,
                "resume_timeout": self.resume_timeout
            }

        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}

    async def set_interruption_mode(self, mode: str):
        """设置打断模式"""
        try:
            self.interruption_mode = TTSInterruptionMode(mode)
            self.logger.info(f"TTS打断模式已设置为: {mode}")

        except ValueError:
            raise ValueError(f"无效的打断模式: {mode}")
        except Exception as e:
            self.logger.error(f"设置打断模式失败: {e}")
            raise
