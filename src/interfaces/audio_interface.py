"""
音频接口定义
定义音频输入输出的抽象接口
"""

from abc import ABC, abstractmethod
from typing import Optional, Callable, List
import numpy as np
from dataclasses import dataclass


@dataclass
class AudioDevice:
    """音频设备信息"""
    id: int
    name: str
    channels: int
    sample_rate: int
    is_input: bool
    is_default: bool


@dataclass
class AudioConfig:
    """音频配置"""
    sample_rate: int = 16000
    channels: int = 1
    chunk_size: int = 1024
    buffer_size: int = 32000
    input_device_id: Optional[int] = None
    output_device_id: Optional[int] = None


@dataclass
class AudioChunk:
    """音频数据块"""
    data: np.ndarray
    timestamp: float
    sample_rate: int
    channels: int


class AudioInterface(ABC):
    """音频处理抽象接口"""
    
    @abstractmethod
    async def initialize(self, config: AudioConfig) -> bool:
        """初始化音频系统"""
        pass
    
    @abstractmethod
    async def start_recording(self) -> bool:
        """开始录音"""
        pass
    
    @abstractmethod
    async def stop_recording(self) -> bool:
        """停止录音"""
        pass
    
    @abstractmethod
    async def play_audio(self, audio_data: np.ndarray) -> bool:
        """播放音频"""
        pass
    
    @abstractmethod
    def register_audio_callback(self, callback: Callable[[np.ndarray], None]) -> None:
        """注册音频数据回调"""
        pass
    
    @abstractmethod
    def get_available_devices(self) -> List[AudioDevice]:
        """获取可用音频设备"""
        pass
    
    @abstractmethod
    def get_recent_audio(self, duration_ms: int) -> np.ndarray:
        """获取最近的音频数据"""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """清理资源"""
        pass


class AudioCallbackInterface(ABC):
    """音频回调接口"""
    
    @abstractmethod
    async def on_audio_data(self, audio_data: np.ndarray) -> None:
        """音频数据回调"""
        pass
    
    @abstractmethod
    async def on_audio_error(self, error: Exception) -> None:
        """音频错误回调"""
        pass
