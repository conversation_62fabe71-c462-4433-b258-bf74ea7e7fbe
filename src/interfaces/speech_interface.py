"""
语音处理接口定义
定义语音识别、唤醒词检测、语音合成的抽象接口
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List
import numpy as np
from dataclasses import dataclass
from enum import Enum


class SpeechEventType(Enum):
    """语音事件类型"""
    WAKE_WORD_DETECTED = "wake_word_detected"
    SPEECH_START = "speech_start"
    SPEECH_END = "speech_end"
    ASR_RESULT = "asr_result"
    TTS_START = "tts_start"
    TTS_END = "tts_end"
    USER_INTERRUPT = "user_interrupt"


@dataclass
class SpeechEvent:
    """语音事件"""
    event_type: SpeechEventType
    timestamp: float
    data: Dict[str, Any]
    confidence: float = 0.0


@dataclass
class WakeWordResult:
    """唤醒词检测结果"""
    detected: bool
    confidence: float
    timestamp: float
    keyword: str


@dataclass
class ASRResult:
    """语音识别结果"""
    text: str
    confidence: float
    segments: List[Dict[str, Any]]
    processing_time: float


@dataclass
class TTSResult:
    """语音合成结果"""
    audio_data: np.ndarray
    sample_rate: int
    duration: float
    processing_time: float


class WakeWordInterface(ABC):
    """唤醒词检测接口"""
    
    @abstractmethod
    async def initialize(self, model_path: str, config: Dict[str, Any]) -> bool:
        """初始化唤醒词检测模型"""
        pass
    
    @abstractmethod
    async def process_audio(self, audio_data: np.ndarray) -> Optional[WakeWordResult]:
        """处理音频数据进行唤醒词检测"""
        pass
    
    @abstractmethod
    def start_detection(self) -> None:
        """开始检测"""
        pass
    
    @abstractmethod
    def stop_detection(self) -> None:
        """停止检测"""
        pass
    
    @abstractmethod
    def update_threshold(self, threshold: float) -> None:
        """更新检测阈值"""
        pass


class SpeechRecognitionInterface(ABC):
    """语音识别接口"""
    
    @abstractmethod
    async def initialize(self, model_name: str, config: Dict[str, Any]) -> bool:
        """初始化语音识别模型"""
        pass
    
    @abstractmethod
    async def recognize(self, audio_data: np.ndarray) -> Optional[ASRResult]:
        """语音识别"""
        pass
    
    @abstractmethod
    async def start_streaming(self) -> None:
        """开始流式识别"""
        pass
    
    @abstractmethod
    async def stop_streaming(self) -> None:
        """停止流式识别"""
        pass


class TTSInterface(ABC):
    """语音合成接口"""
    
    @abstractmethod
    async def initialize(self, model_name: str, config: Dict[str, Any]) -> bool:
        """初始化语音合成模型"""
        pass
    
    @abstractmethod
    async def synthesize(self, text: str) -> Optional[TTSResult]:
        """语音合成"""
        pass
    
    @abstractmethod
    async def synthesize_streaming(self, text: str) -> Optional[np.ndarray]:
        """流式语音合成"""
        pass
    
    @abstractmethod
    def set_voice(self, voice: str) -> None:
        """设置语音"""
        pass
    
    @abstractmethod
    def set_speed(self, speed: float) -> None:
        """设置语速"""
        pass


class VADInterface(ABC):
    """语音活动检测接口"""
    
    @abstractmethod
    async def detect_speech(self, audio_data: np.ndarray) -> bool:
        """检测语音活动"""
        pass
    
    @abstractmethod
    def set_sensitivity(self, sensitivity: float) -> None:
        """设置检测敏感度"""
        pass
