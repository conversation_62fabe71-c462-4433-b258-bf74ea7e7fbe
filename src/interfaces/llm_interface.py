"""
大语言模型接口定义
定义LLM客户端的抽象接口
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List, AsyncGenerator
from dataclasses import dataclass
from enum import Enum


class LLMProvider(Enum):
    """LLM提供商"""
    DIFY = "dify"
    OPENAI = "openai"
    AZURE = "azure"
    LOCAL = "local"


@dataclass
class LLMMessage:
    """LLM消息"""
    role: str  # "user", "assistant", "system"
    content: str
    timestamp: float


@dataclass
class LLMRequest:
    """LLM请求"""
    messages: List[LLMMessage]
    max_tokens: int = 1000
    temperature: float = 0.7
    stream: bool = False
    metadata: Dict[str, Any] = None


@dataclass
class LLMResponse:
    """LLM响应"""
    content: str
    usage: Dict[str, int]
    model: str
    finish_reason: str
    processing_time: float


@dataclass
class LLMStreamChunk:
    """LLM流式响应块"""
    content: str
    is_final: bool
    chunk_id: str


class LLMInterface(ABC):
    """大语言模型接口"""
    
    @abstractmethod
    async def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化LLM客户端"""
        pass
    
    @abstractmethod
    async def chat(self, request: LLMRequest) -> Optional[LLMResponse]:
        """同步对话"""
        pass
    
    @abstractmethod
    async def chat_stream(self, request: LLMRequest) -> AsyncGenerator[LLMStreamChunk, None]:
        """流式对话"""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """健康检查"""
        pass
    
    @abstractmethod
    def get_provider(self) -> LLMProvider:
        """获取提供商"""
        pass
    
    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        pass


class ConversationInterface(ABC):
    """对话管理接口"""
    
    @abstractmethod
    async def start_conversation(self, user_id: str) -> str:
        """开始对话"""
        pass
    
    @abstractmethod
    async def add_message(self, conversation_id: str, message: LLMMessage) -> None:
        """添加消息"""
        pass
    
    @abstractmethod
    async def get_history(self, conversation_id: str, limit: int = 10) -> List[LLMMessage]:
        """获取对话历史"""
        pass
    
    @abstractmethod
    async def clear_history(self, conversation_id: str) -> None:
        """清除对话历史"""
        pass
    
    @abstractmethod
    async def end_conversation(self, conversation_id: str) -> None:
        """结束对话"""
        pass
    
    @abstractmethod
    def is_conversation_active(self, conversation_id: str) -> bool:
        """检查对话是否活跃"""
        pass
