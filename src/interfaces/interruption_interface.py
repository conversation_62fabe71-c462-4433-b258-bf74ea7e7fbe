#!/usr/bin/env python3
"""
打断检测接口定义
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from enum import Enum


class InterruptionType(Enum):
    """打断类型"""
    SPEECH_BASED = "speech_based"
    ENERGY_BASED = "energy_based"
    KEYWORD_BASED = "keyword_based"
    GESTURE_BASED = "gesture_based"


class InterruptionSeverity(Enum):
    """打断严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class InterruptionResult:
    """打断检测结果"""
    detected: bool
    type: InterruptionType
    severity: InterruptionSeverity
    confidence: float
    timestamp: float
    reason: str
    context: Dict[str, Any]


class InterruptionDetectorInterface(ABC):
    """打断检测器接口"""
    
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化打断检测器"""
        pass
    
    @abstractmethod
    async def start_monitoring(self) -> bool:
        """开始监控"""
        pass
    
    @abstractmethod
    async def stop_monitoring(self) -> bool:
        """停止监控"""
        pass
    
    @abstractmethod
    def add_callback(self, callback: Callable[[InterruptionResult], None]):
        """添加打断检测回调"""
        pass
    
    @abstractmethod
    def remove_callback(self, callback: Callable[[InterruptionResult], None]):
        """移除打断检测回调"""
        pass
    
    @abstractmethod
    async def set_sensitivity(self, sensitivity: str):
        """设置敏感度"""
        pass
    
    @abstractmethod
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        pass


class InterruptionHandlerInterface(ABC):
    """打断处理器接口"""
    
    @abstractmethod
    async def handle_interruption(self, result: InterruptionResult) -> bool:
        """处理打断事件"""
        pass
    
    @abstractmethod
    async def recover_from_interruption(self, context: Dict[str, Any]) -> bool:
        """从打断中恢复"""
        pass
    
    @abstractmethod
    def get_interruption_history(self) -> List[InterruptionResult]:
        """获取打断历史"""
        pass
