#!/usr/bin/env python3
"""
流式处理接口定义
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Callable, AsyncGenerator
from dataclasses import dataclass
from enum import Enum


class StreamingState(Enum):
    """流式处理状态"""
    IDLE = "idle"
    PROCESSING = "processing"
    COMPLETED = "completed"
    ERROR = "error"


@dataclass
class StreamingMetrics:
    """流式处理指标"""
    session_id: str
    total_latency: float
    first_chunk_latency: float
    throughput: float
    error_count: int
    chunk_count: int


class StreamingCoordinatorInterface(ABC):
    """流式协调器接口"""
    
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化流式协调器"""
        pass
    
    @abstractmethod
    async def start_streaming_session(self, session_id: str) -> bool:
        """启动流式处理会话"""
        pass
    
    @abstractmethod
    async def stop_streaming_session(self, session_id: str) -> bool:
        """停止流式处理会话"""
        pass
    
    @abstractmethod
    def add_chunk_callback(self, callback: Callable):
        """添加块处理回调"""
        pass
    
    @abstractmethod
    def add_completion_callback(self, callback: Callable):
        """添加完成回调"""
        pass
    
    @abstractmethod
    def get_streaming_statistics(self) -> Dict[str, Any]:
        """获取流式处理统计信息"""
        pass
    
    @abstractmethod
    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话信息"""
        pass


class StreamingProcessorInterface(ABC):
    """流式处理器接口"""
    
    @abstractmethod
    async def process_stream(self, data: Any) -> AsyncGenerator[Any, None]:
        """处理流式数据"""
        pass
    
    @abstractmethod
    async def finalize_stream(self, session_id: str) -> bool:
        """完成流式处理"""
        pass
    
    @abstractmethod
    def get_processing_metrics(self) -> StreamingMetrics:
        """获取处理指标"""
        pass


class TextProcessorInterface(ABC):
    """文本处理器接口"""
    
    @abstractmethod
    def smart_chunk(self, text: str, is_final: bool = False) -> List[Any]:
        """智能分块处理"""
        pass
    
    @abstractmethod
    def optimize_for_tts(self, text: str) -> str:
        """为TTS优化文本"""
        pass
    
    @abstractmethod
    def detect_sentence_boundaries(self, text: str) -> List[int]:
        """检测句子边界"""
        pass
