#!/usr/bin/env python3
"""
语音助手主应用
集成ServiceManager的完整语音助手应用
"""

import asyncio
import signal
import logging
import sounddevice as sd
import numpy as np
from typing import Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum
from pathlib import Path

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.services.wake_word import WakeWordService
from src.services.asr_service import ASRService
from src.services.event_bus import EventBus
from src.core.state_machine import StateMachine, SystemState
from src.services.conversation_service import ConversationService
from src.core.unified_audio_capture import AudioDeviceManager, UnifiedAudioCapture


class VoiceAssistantStatus(Enum):
    """语音助手状态"""
    INITIALIZING = "initializing"
    READY = "ready"
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"


@dataclass
class VoiceAssistantConfig:
    """语音助手配置"""
    # 音频配置
    sample_rate: int = 16000
    channels: int = 1
    blocksize: int = 1024
    device_id: Optional[int] = None
    
    # 服务配置
    config_path: str = "config/config.yaml"
    environment: str = "development"
    enable_monitoring: bool = True
    enable_logging: bool = True


class VoiceAssistant:
    """语音助手主控制器"""
    
    def __init__(self, config: VoiceAssistantConfig = None):
        self.config = config or VoiceAssistantConfig()
        self.status = VoiceAssistantStatus.INITIALIZING
        self.logger = logging.getLogger(__name__)
        
        # 核心组件（使用直接服务管理，类似test_main.py）
        self.event_bus = None
        self.wake_word_service = None
        self.asr_service = None
        self.state_machine = None
        self.conversation_service = None

        # 音频相关
        self.audio_device_manager = AudioDeviceManager()
        self.audio_stream = None
        
        # 运行状态
        self.is_running = False
        self.main_loop = None
        self.shutdown_event = asyncio.Event()
        
        # 设置日志和信号处理
        self._setup_logging()
        self._setup_signal_handlers()
    
    def _setup_logging(self):
        """设置日志"""
        if self.config.enable_logging:
            logging.basicConfig(
                level=logging.WARNING,
                format='%(message)s'
            )
            # 设置特定模块的日志级别
            logging.getLogger('src.services').setLevel(logging.WARNING)
            logging.getLogger('funasr').setLevel(logging.ERROR)
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            signal_names = {2: "SIGINT", 15: "SIGTERM"}
            signal_name = signal_names.get(signum, f"信号{signum}")
            print(f"\n🛑 接收到{signal_name}，正在优雅关闭...")
            if self.main_loop:
                self.main_loop.create_task(self.stop())

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def _setup_audio_device(self, force_select: bool = False) -> Optional[int]:
        """设置音频设备"""
        try:
            print("🎤 设置音频设备...")

            # 使用音频设备管理器
            selected_device = self.audio_device_manager.setup_audio_device(force_select)

            if selected_device:
                print(f"✅ 选择音频设备: [{selected_device.device_id}] {selected_device.name}")
                return selected_device.device_id
            else:
                print("❌ 未能设置音频设备")
                return None

        except Exception as e:
            print(f"❌ 设置音频设备失败: {e}")
            return None
    

    
    async def initialize(self) -> bool:
        """初始化语音助手系统（使用直接服务管理）"""
        try:
            print("🔧 初始化语音助手系统...")

            # 1. 设置音频设备
            if self.config.device_id is None:
                self.config.device_id = self._setup_audio_device()
                if self.config.device_id is None:
                    print("❌ 未能设置音频设备")
                    return False

            # 2. 初始化事件总线
            self.event_bus = EventBus()
            await self.event_bus.initialize()

            # 3. 初始化唤醒词服务
            wake_word_config = {
                'enabled': True,
                'model_path': 'models/wake_word/hey_aibi.onnx',
                'keyword': 'hey aibi',
                'confidence_threshold': 0.85,
                'sample_rate': self.config.sample_rate,
                'chunk_size': self.config.blocksize,
                'window_size': 1.5,
                'hop_size': 0.5,
                'cooldown_period': 2.0
            }

            self.wake_word_service = WakeWordService(wake_word_config, self.event_bus)
            if not await self.wake_word_service.initialize():
                raise RuntimeError("唤醒词服务初始化失败")
            if not await self.wake_word_service.start():
                raise RuntimeError("唤醒词服务启动失败")

            # 3. 初始化ASR服务（使用FunASR完整pipeline）
            asr_config = {
                'enabled': True,
                'model_name': 'models/funasr/iic/SenseVoiceSmall',  # 使用本地模型路径
                'vad_model': 'models/funasr/fsmn-vad',              # 本地VAD模型路径
                'punc_model': 'models/funasr/ct-punc',              # 本地标点恢复模型路径
                'device': 'auto',
                'sample_rate': self.config.sample_rate,
                'max_audio_duration': 30.0,
                'min_audio_duration': 1.0,
                'auto_recognition': False
            }

            self.asr_service = ASRService(asr_config, self.event_bus)
            if not await self.asr_service.initialize():
                raise RuntimeError("语音识别服务初始化失败")
            if not await self.asr_service.start():
                raise RuntimeError("语音识别服务启动失败")

            # 4. 初始化状态机
            state_machine_config = {
                "conversation_timeout": 15.0,
                "speech_timeout": 30.0,
                "error_recovery_timeout": 5.0
            }
            self.state_machine = StateMachine(state_machine_config, self.event_bus, self.asr_service)
            if not await self.state_machine.initialize():
                raise RuntimeError("状态机初始化失败")
            if not await self.state_machine.start():
                raise RuntimeError("状态机启动失败")

            # 5. 初始化对话管理服务
            conversation_config = {
                "max_context_length": 10,
                "session_timeout": 300.0,
                "max_turn_count": 50,
                "enable_interruption": True,
                "interruption_threshold": 0.5
            }
            self.conversation_service = ConversationService(conversation_config, self.event_bus)
            if not await self.conversation_service.initialize():
                raise RuntimeError("对话管理服务初始化失败")
            if not await self.conversation_service.start():
                raise RuntimeError("对话管理服务启动失败")

            self.status = VoiceAssistantStatus.READY
            print("✅ 语音助手系统初始化完成")
            return True

        except Exception as e:
            print(f"❌ 语音助手初始化失败: {e}")
            self.status = VoiceAssistantStatus.ERROR
            return False

    def _audio_callback(self, indata, frames, time, status):
        """音频回调函数"""
        if status:
            return

        if self.is_running and self.event_bus and self.main_loop:
            audio_data = indata[:, 0] if self.config.channels == 1 else indata.flatten()

            # 使用线程安全的方式将事件发布任务调度到主事件循环
            try:
                future = asyncio.run_coroutine_threadsafe(
                    self.event_bus.emit("audio_chunk", {"audio_data": audio_data}),
                    self.main_loop
                )
                # 立即释放future引用，减少内存占用
                del future
            except (RuntimeError, Exception):
                # 静默处理错误，避免影响音频流
                pass

    async def _start_audio_stream(self) -> bool:
        """启动音频流"""
        try:
            print("🎤 启动音频流...")

            # 获取设备信息
            devices = self.audio_device_manager.get_available_input_devices()
            selected_device = None

            # 查找配置的设备
            for device in devices:
                if device.device_id == self.config.device_id:
                    selected_device = device
                    break

            if not selected_device:
                print(f"❌ 未找到设备ID {self.config.device_id}")
                return False

            # 创建统一音频流（基于PipeWire）
            print(f"🎤 使用PipeWire设备: {selected_device.name}")
            self.audio_stream = UnifiedAudioCapture(
                device_info=selected_device,
                samplerate=self.config.sample_rate,
                channels=self.config.channels,
                blocksize=self.config.blocksize,
                dtype=np.float32,
                callback=self._audio_callback
            )

            # 启动音频流
            self.audio_stream.start()
            print(f"✅ 音频流已启动 (设备: {self.config.device_id}, 采样率: {self.config.sample_rate}Hz)")

            return True

        except Exception as e:
            print(f"❌ 启动音频流失败: {e}")
            return False

    async def start(self) -> None:
        """启动语音助手"""
        try:
            if self.status != VoiceAssistantStatus.READY:
                raise RuntimeError("语音助手未就绪，请先调用 initialize()")

            print("🚀 启动语音助手...")
            self.status = VoiceAssistantStatus.RUNNING
            self.is_running = True
            self.main_loop = asyncio.get_running_loop()

            # 启动音频流
            if not await self._start_audio_stream():
                raise RuntimeError("音频流启动失败")

            print("🎉 语音助手已启动！")
            print("💡 说出 'hey aibi' 来唤醒助手")
            print("💡 按 Ctrl+C 退出")

        except Exception as e:
            print(f"❌ 启动语音助手失败: {e}")
            self.status = VoiceAssistantStatus.ERROR
            raise

    async def run(self) -> bool:
        """运行语音助手主循环"""
        try:
            # 初始化
            if not await self.initialize():
                return False

            # 启动
            await self.start()

            # 主循环 - 等待用户中断（简化版，类似test_main.py）
            try:
                while self.is_running:
                    await asyncio.sleep(1)

            except KeyboardInterrupt:
                print("\n🛑 用户中断，正在关闭...")

            return True

        except Exception as e:
            print(f"❌ 运行失败: {e}")
            import traceback
            traceback.print_exc()
            return False

        finally:
            await self.stop()

    async def stop(self) -> None:
        """停止语音助手"""
        try:
            print("🧹 正在停止语音助手...")
            self.status = VoiceAssistantStatus.STOPPING
            self.is_running = False

            # 停止音频流
            if self.audio_stream:
                self.audio_stream.stop()
                self.audio_stream.close()
                self.audio_stream = None
                print("✅ 音频流已停止")

            # 停止所有服务（直接管理方式）
            if self.conversation_service:
                await self.conversation_service.stop()
            if self.state_machine:
                await self.state_machine.stop()
            if self.asr_service:
                await self.asr_service.stop()
            if self.wake_word_service:
                await self.wake_word_service.stop()
            if self.event_bus:
                await self.event_bus.shutdown()
            print("✅ 所有服务已停止")

            self.status = VoiceAssistantStatus.STOPPED
            print("✅ 语音助手已停止")

            # 设置关闭事件
            self.shutdown_event.set()

        except Exception as e:
            print(f"❌ 停止语音助手失败: {e}")
            self.status = VoiceAssistantStatus.ERROR

    def get_status(self) -> VoiceAssistantStatus:
        """获取当前状态"""
        return self.status

    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        info = {
            "status": self.status.value,
            "is_running": self.is_running,
            "audio_device": self.config.device_id,
            "sample_rate": self.config.sample_rate,
            "channels": self.config.channels
        }

        # 添加服务状态信息
        services_status = {
            "wake_word_service": "running" if self.wake_word_service else "stopped",
            "asr_service": "running" if self.asr_service else "stopped",
            "state_machine": "running" if self.state_machine else "stopped",
            "conversation_service": "running" if self.conversation_service else "stopped"
        }
        info["service_status"] = services_status

        return info


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='语音助手')
    parser.add_argument('--select-device', action='store_true',
                       help='重新选择音频设备')
    parser.add_argument('--list-devices', action='store_true',
                       help='列出所有音频设备')

    args = parser.parse_args()

    # 如果只是列出设备
    if args.list_devices:
        from src.core.audio_device_manager import AudioDeviceManager
        manager = AudioDeviceManager()
        devices = manager.get_available_input_devices()
        print("\n🎤 所有音频输入设备:")
        print("=" * 60)
        for device in devices:
            print(device)
        print("=" * 60)
        return 0

    # 创建语音助手
    assistant = VoiceAssistant()

    # 如果需要重新选择设备
    if args.select_device:
        assistant.config.device_id = assistant._setup_audio_device(force_select=True)
        if assistant.config.device_id is None:
            print("❌ 设备选择失败")
            return 1

    try:
        success = await assistant.run()
        return 0 if success else 1

    except Exception as e:
        print(f"❌ 程序异常退出: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
