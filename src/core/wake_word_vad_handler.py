#!/usr/bin/env python3
"""
唤醒词后的VAD处理器
专门解决唤醒后语音收集问题的简化版本
"""

import asyncio
import time
import logging
import numpy as np
from typing import Dict, Any, Optional, Callable
from enum import Enum
import tempfile
import os
import soundfile as sf

from funasr import AutoModel


class WakeVADState(Enum):
    """唤醒后VAD状态"""
    WAITING_FOR_SPEECH = "waiting_for_speech"    # 等待用户开始说话
    SPEECH_ACTIVE = "speech_active"              # 用户正在说话
    SPEECH_COMPLETE = "speech_complete"          # 语音完成，可以转写
    TIMEOUT = "timeout"                          # 超时


class WakeWordVADHandler:
    """唤醒词后的VAD处理器"""
    
    def __init__(self, 
                 waiting_timeout: float = 10.0,
                 speech_end_timeout: float = 1.2,
                 sample_rate: int = 16000):
        """
        初始化VAD处理器
        
        Args:
            waiting_timeout: 唤醒后等待语音的超时时间（秒）
            speech_end_timeout: 语音结束后的静音确认时间（秒）
            sample_rate: 采样率
        """
        self.waiting_timeout = waiting_timeout
        self.speech_end_timeout = speech_end_timeout
        self.sample_rate = sample_rate
        
        self.logger = logging.getLogger(__name__)
        
        # VAD模型
        self.vad_model: Optional[AutoModel] = None
        
        # 状态管理
        self.current_state = WakeVADState.WAITING_FOR_SPEECH
        self.state_start_time = 0.0
        self.last_speech_time = 0.0
        self.speech_detected = False
        
        # 音频缓冲
        self.audio_buffer = []
        self.buffer_lock = asyncio.Lock()
        
        # 控制标志
        self.is_active = False
        self.monitoring_task: Optional[asyncio.Task] = None
        
        # 回调函数
        self.on_speech_complete: Optional[Callable] = None
        self.on_timeout: Optional[Callable] = None
        
    async def initialize(self) -> bool:
        """初始化VAD模型"""
        try:
            if self.vad_model is not None:
                return True
            
            print("🔧 正在初始化唤醒VAD处理器...")
            
            # 初始化FunASR VAD模型
            self.vad_model = AutoModel(
                model="fsmn-vad",
                device="cpu",
                disable_update=True,
                disable_log=True,
                disable_pbar=True
            )
            
            print("✅ 唤醒VAD处理器初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"VAD处理器初始化失败: {e}")
            print(f"❌ VAD处理器初始化失败: {e}")
            return False
    
    async def start_after_wake_word(self) -> bool:
        """唤醒词检测后启动VAD处理"""
        try:
            if not await self.initialize():
                return False
            
            # 重置状态
            self.current_state = WakeVADState.WAITING_FOR_SPEECH
            self.state_start_time = time.time()
            self.last_speech_time = 0.0
            self.speech_detected = False
            
            # 清空缓冲区
            async with self.buffer_lock:
                self.audio_buffer.clear()
            
            # 启动监控
            self.is_active = True
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            
            print(f"🎤 请说出您的指令（{self.waiting_timeout}秒内）...")
            return True
            
        except Exception as e:
            self.logger.error(f"启动VAD处理失败: {e}")
            return False
    
    async def add_audio_chunk(self, audio_data: np.ndarray):
        """添加音频块"""
        if not self.is_active:
            return
        
        async with self.buffer_lock:
            self.audio_buffer.append(audio_data.copy())
            
            # 限制缓冲区大小（保留最近3秒）
            max_chunks = int(3.0 * self.sample_rate / len(audio_data))
            if len(self.audio_buffer) > max_chunks:
                self.audio_buffer.pop(0)
    
    async def stop(self):
        """停止VAD处理"""
        self.is_active = False
        
        if self.monitoring_task and not self.monitoring_task.done():
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
    
    async def _monitoring_loop(self):
        """监控循环"""
        try:
            while self.is_active:
                current_time = time.time()
                
                # 检查超时
                if (self.current_state == WakeVADState.WAITING_FOR_SPEECH and
                    current_time - self.state_start_time >= self.waiting_timeout):
                    
                    print(f"⏰ {self.waiting_timeout}秒内未检测到语音，返回待唤醒状态")
                    self.current_state = WakeVADState.TIMEOUT
                    self.is_active = False
                    
                    if self.on_timeout:
                        await self.on_timeout()
                    break
                
                # 分析音频
                async with self.buffer_lock:
                    if len(self.audio_buffer) >= 5:  # 至少5个块才分析
                        recent_audio = np.concatenate(self.audio_buffer[-10:])
                        has_speech = await self._detect_speech(recent_audio)
                        
                        if self.current_state == WakeVADState.WAITING_FOR_SPEECH:
                            if has_speech:
                                print(f"🎤 检测到语音开始")
                                self.current_state = WakeVADState.SPEECH_ACTIVE
                                self.speech_detected = True
                                self.last_speech_time = current_time
                        
                        elif self.current_state == WakeVADState.SPEECH_ACTIVE:
                            if has_speech:
                                self.last_speech_time = current_time
                            else:
                                # 检查静音时间
                                silence_duration = current_time - self.last_speech_time
                                if silence_duration >= self.speech_end_timeout:
                                    print(f"🔇 检测到{self.speech_end_timeout}秒静音，语音收集完成")
                                    self.current_state = WakeVADState.SPEECH_COMPLETE
                                    self.is_active = False
                                    
                                    if self.on_speech_complete:
                                        await self.on_speech_complete()
                                    break
                
                await asyncio.sleep(0.1)
                
        except asyncio.CancelledError:
            pass
        except Exception as e:
            self.logger.error(f"监控循环错误: {e}")
    
    async def _detect_speech(self, audio_data: np.ndarray) -> bool:
        """检测语音活动"""
        try:
            if len(audio_data) == 0:
                return False
            
            # 保存到临时文件
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_path = temp_file.name
                sf.write(temp_path, audio_data, self.sample_rate)
            
            try:
                # 使用VAD检测
                result = self.vad_model.generate(input=temp_path, cache={})
                
                # 解析结果
                if result and len(result) > 0:
                    segments = result[0].get("value", []) if isinstance(result[0], dict) else result[0]
                    
                    # 检查是否有有效的语音段
                    for segment in segments:
                        if len(segment) >= 2 and segment[1] > 0:
                            duration = (segment[1] - segment[0]) / 1000.0
                            if duration >= 0.2:  # 至少200ms的语音
                                return True
                
                return False
                
            finally:
                try:
                    os.unlink(temp_path)
                except:
                    pass
                    
        except Exception as e:
            self.logger.error(f"语音检测失败: {e}")
            return False
    
    def get_state(self) -> WakeVADState:
        """获取当前状态"""
        return self.current_state
    
    def has_speech_detected(self) -> bool:
        """是否检测到了语音"""
        return self.speech_detected
