#!/usr/bin/env python3
"""
PipeWire设备管理器
通过PipeWire/PulseAudio兼容层发现和管理音频设备
"""

import subprocess
import logging
import re
import json
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
from enum import Enum


class DeviceType(Enum):
    """设备类型"""
    INPUT = "input"
    OUTPUT = "output"
    MONITOR = "monitor"


class DeviceState(Enum):
    """设备状态"""
    RUNNING = "RUNNING"
    IDLE = "IDLE"
    SUSPENDED = "SUSPENDED"
    UNKNOWN = "UNKNOWN"


@dataclass
class PipeWireDevice:
    """PipeWire设备信息"""
    id: int
    name: str
    description: str
    device_type: DeviceType
    state: DeviceState
    sample_format: str
    channels: int
    sample_rate: int
    is_default: bool = False
    is_usb_gadget: bool = False
    node_name: Optional[str] = None
    device_class: Optional[str] = None
    
    def __str__(self):
        status_icon = "🔥" if self.is_usb_gadget else "📱"
        state_icon = "✅" if self.state == DeviceState.RUNNING else "⏸️"
        return f"[{self.id}] {self.description} - {status_icon} - {state_icon} {self.state.value}"


class PipeWireDeviceManager:
    """PipeWire设备管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._devices_cache: List[PipeWireDevice] = []
        self._cache_valid = False
    
    def discover_devices(self, device_type: DeviceType = DeviceType.INPUT, 
                        refresh_cache: bool = False) -> List[PipeWireDevice]:
        """
        发现PipeWire设备
        
        Args:
            device_type: 设备类型
            refresh_cache: 是否刷新缓存
            
        Returns:
            List[PipeWireDevice]: 设备列表
        """
        try:
            if refresh_cache or not self._cache_valid:
                self._refresh_device_cache()
            
            # 过滤指定类型的设备
            filtered_devices = [
                device for device in self._devices_cache 
                if device.device_type == device_type
            ]
            
            # 按优先级排序：USB Gadget > 运行中 > 其他
            filtered_devices.sort(key=lambda d: (
                not d.is_usb_gadget,
                d.state != DeviceState.RUNNING,
                d.id
            ))
            
            return filtered_devices
            
        except Exception as e:
            self.logger.error(f"发现设备失败: {e}")
            return []
    
    def _refresh_device_cache(self):
        """刷新设备缓存"""
        try:
            self.logger.debug("刷新PipeWire设备缓存...")
            
            # 获取PulseAudio源设备（输入）
            input_devices = self._get_pulseaudio_sources()
            
            # 获取PulseAudio输出设备
            output_devices = self._get_pulseaudio_sinks()
            
            # 合并设备列表
            self._devices_cache = input_devices + output_devices
            self._cache_valid = True
            
            self.logger.info(f"发现 {len(self._devices_cache)} 个PipeWire设备")
            
        except Exception as e:
            self.logger.error(f"刷新设备缓存失败: {e}")
            self._devices_cache = []
            self._cache_valid = False
    
    def _get_pulseaudio_sources(self) -> List[PipeWireDevice]:
        """获取PulseAudio输入源"""
        devices = []

        try:
            # 获取简短列表（包含所有必要信息）
            result = subprocess.run(
                ['pactl', 'list', 'sources', 'short'],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode != 0:
                self.logger.warning(f"pactl sources short失败: {result.stderr}")
                return devices

            # 直接从简短列表创建设备
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 4:
                        try:
                            device_id = int(parts[0])
                            device_name = parts[1]
                            sample_format = parts[2]
                            state_str = parts[3]

                            # 创建设备对象
                            device = self._create_device_from_short_format(
                                device_id, device_name, sample_format, state_str, DeviceType.INPUT
                            )

                            if device:
                                devices.append(device)

                        except (ValueError, IndexError) as e:
                            self.logger.debug(f"跳过无效设备行: {line} - {e}")
                            continue

        except subprocess.TimeoutExpired:
            self.logger.error("pactl命令超时")
        except Exception as e:
            self.logger.error(f"获取PulseAudio源失败: {e}")

        return devices
    
    def _get_pulseaudio_sinks(self) -> List[PipeWireDevice]:
        """获取PulseAudio输出设备"""
        devices = []

        try:
            # 获取简短列表
            result = subprocess.run(
                ['pactl', 'list', 'sinks', 'short'],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode != 0:
                return devices

            # 直接从简短列表创建设备
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 4:
                        try:
                            device_id = int(parts[0])
                            device_name = parts[1]
                            sample_format = parts[2]
                            state_str = parts[3]

                            # 创建设备对象
                            device = self._create_device_from_short_format(
                                device_id, device_name, sample_format, state_str, DeviceType.OUTPUT
                            )

                            if device:
                                devices.append(device)

                        except (ValueError, IndexError) as e:
                            self.logger.debug(f"跳过无效设备行: {line} - {e}")
                            continue

        except Exception as e:
            self.logger.error(f"获取PulseAudio输出设备失败: {e}")

        return devices

    def _create_device_from_short_format(self, device_id: int, device_name: str,
                                        sample_format: str, state_str: str,
                                        device_type: DeviceType) -> Optional[PipeWireDevice]:
        """从简短格式创建设备对象"""
        try:
            # 解析状态
            state = self._parse_device_state(state_str)

            # 解析采样格式 (如 "s16le 1ch 16000Hz")
            channels = 1
            sample_rate = 16000

            if sample_format and sample_format != "unknown":
                # 解析声道数
                channel_match = re.search(r'(\d+)ch', sample_format)
                if channel_match:
                    channels = int(channel_match.group(1))

                # 解析采样率
                rate_match = re.search(r'(\d+)Hz', sample_format)
                if rate_match:
                    sample_rate = int(rate_match.group(1))

            # 生成描述（从设备名称推导）
            description = self._generate_device_description(device_name)

            # 检测USB Gadget设备
            is_usb_gadget = (
                'usb' in device_name.lower() and 'gadget' in device_name.lower()
            ) or (
                'usb' in description.lower() and 'gadget' in description.lower()
            )

            return PipeWireDevice(
                id=device_id,
                name=device_name,
                description=description,
                device_type=device_type,
                state=state,
                sample_format=sample_format,
                channels=channels,
                sample_rate=sample_rate,
                is_usb_gadget=is_usb_gadget
            )

        except Exception as e:
            self.logger.error(f"创建设备对象失败: {e}")
            return None

    def _generate_device_description(self, device_name: str) -> str:
        """从设备名称生成友好的描述"""
        # 简单的名称映射
        if 'usb' in device_name.lower() and 'gadget' in device_name.lower():
            return "USB Gadget Audio"
        elif 'hdmi' in device_name.lower():
            return "HDMI Audio"
        elif 'bluez' in device_name.lower():
            return "Bluetooth Audio"
        elif 'monitor' in device_name.lower():
            return "Monitor Audio"
        else:
            # 使用设备名称的最后部分
            parts = device_name.split('.')
            if len(parts) > 1:
                return parts[-1].replace('-', ' ').title()
            return device_name

    def _parse_pulseaudio_devices(self, detailed_output: str,
                                 short_info: Dict, 
                                 device_type: DeviceType) -> List[PipeWireDevice]:
        """解析PulseAudio设备信息"""
        devices = []
        current_device = None
        
        try:
            lines = detailed_output.split('\n')
            
            for line in lines:
                line = line.strip()
                
                # 检测设备开始
                if device_type == DeviceType.INPUT and line.startswith('Source #'):
                    device_id = int(line.split('#')[1])
                    current_device = {'id': device_id, 'type': device_type}
                elif device_type == DeviceType.OUTPUT and line.startswith('Sink #'):
                    device_id = int(line.split('#')[1])
                    current_device = {'id': device_id, 'type': device_type}
                
                if current_device is None:
                    continue
                
                # 解析设备属性
                if line.startswith('Name:'):
                    current_device['name'] = line.split(':', 1)[1].strip()
                elif line.startswith('Description:'):
                    current_device['description'] = line.split(':', 1)[1].strip()
                elif line.startswith('State:'):
                    state_str = line.split(':', 1)[1].strip()
                    current_device['state'] = self._parse_device_state(state_str)
                elif 'node.name' in line:
                    match = re.search(r'node\.name = "([^"]+)"', line)
                    if match:
                        current_device['node_name'] = match.group(1)
                elif 'device.class' in line:
                    match = re.search(r'device\.class = "([^"]+)"', line)
                    if match:
                        current_device['device_class'] = match.group(1)
                
                # 检测设备结束（下一个设备开始）
                if ((device_type == DeviceType.INPUT and line.startswith('Source #')) or
                    (device_type == DeviceType.OUTPUT and line.startswith('Sink #'))) and \
                   '#' in line:
                    new_device_id = int(line.split('#')[1])
                    if current_device['id'] != new_device_id:
                        # 完成当前设备
                        device = self._create_device_from_parsed_data(current_device, short_info)
                        if device:
                            devices.append(device)
                        # 开始新设备
                        current_device = {'id': new_device_id, 'type': device_type}
            
            # 处理最后一个设备
            if current_device:
                device = self._create_device_from_parsed_data(current_device, short_info)
                if device:
                    devices.append(device)
        
        except Exception as e:
            self.logger.error(f"解析PulseAudio设备失败: {e}")
        
        return devices
    
    def _create_device_from_parsed_data(self, device_data: Dict, 
                                       short_info: Dict) -> Optional[PipeWireDevice]:
        """从解析的数据创建设备对象"""
        try:
            device_id = device_data['id']
            name = device_data.get('name', f'device_{device_id}')
            description = device_data.get('description', name)
            device_type = device_data['type']
            state = device_data.get('state', DeviceState.UNKNOWN)
            
            # 从简短信息获取格式信息
            short_data = short_info.get(device_id, {})
            sample_format = short_data.get('format', 'unknown')
            
            # 解析采样格式
            channels = 1
            sample_rate = 16000
            if sample_format != 'unknown':
                # 解析格式如 "s16le 1ch 16000Hz"
                format_match = re.search(r'(\d+)ch', sample_format)
                if format_match:
                    channels = int(format_match.group(1))
                
                rate_match = re.search(r'(\d+)Hz', sample_format)
                if rate_match:
                    sample_rate = int(rate_match.group(1))
            
            # 检测USB Gadget设备
            is_usb_gadget = (
                'usb' in name.lower() and 'gadget' in name.lower()
            ) or (
                'usb' in description.lower() and 'gadget' in description.lower()
            )
            
            return PipeWireDevice(
                id=device_id,
                name=name,
                description=description,
                device_type=device_type,
                state=state,
                sample_format=sample_format,
                channels=channels,
                sample_rate=sample_rate,
                is_usb_gadget=is_usb_gadget,
                node_name=device_data.get('node_name'),
                device_class=device_data.get('device_class')
            )
            
        except Exception as e:
            self.logger.error(f"创建设备对象失败: {e}")
            return None
    
    def _parse_device_state(self, state_str: str) -> DeviceState:
        """解析设备状态"""
        state_str = state_str.upper().strip()
        if state_str == 'RUNNING':
            return DeviceState.RUNNING
        elif state_str == 'IDLE':
            return DeviceState.IDLE
        elif state_str == 'SUSPENDED':
            return DeviceState.SUSPENDED
        else:
            return DeviceState.UNKNOWN
    
    def get_usb_gadget_device(self) -> Optional[PipeWireDevice]:
        """获取USB Gadget设备"""
        input_devices = self.discover_devices(DeviceType.INPUT, refresh_cache=True)
        
        for device in input_devices:
            if device.is_usb_gadget:
                return device
        
        return None
    
    def get_default_input_device(self) -> Optional[PipeWireDevice]:
        """获取默认输入设备"""
        try:
            result = subprocess.run(
                ['pactl', 'get-default-source'],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0:
                default_name = result.stdout.strip()
                input_devices = self.discover_devices(DeviceType.INPUT)
                
                for device in input_devices:
                    if device.name == default_name:
                        device.is_default = True
                        return device
            
        except Exception as e:
            self.logger.error(f"获取默认输入设备失败: {e}")
        
        return None
    
    def set_default_input_device(self, device: PipeWireDevice) -> bool:
        """设置默认输入设备"""
        try:
            result = subprocess.run(
                ['pactl', 'set-default-source', device.name],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0:
                self.logger.info(f"已设置默认输入设备: {device.description}")
                return True
            else:
                self.logger.error(f"设置默认输入设备失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"设置默认输入设备异常: {e}")
            return False
    
    def get_device_info(self, device_id: int) -> Optional[PipeWireDevice]:
        """根据ID获取设备信息"""
        all_devices = self.discover_devices(DeviceType.INPUT) + \
                     self.discover_devices(DeviceType.OUTPUT)
        
        for device in all_devices:
            if device.id == device_id:
                return device
        
        return None
    
    def is_pipewire_available(self) -> bool:
        """检查PipeWire是否可用"""
        try:
            result = subprocess.run(
                ['pactl', 'info'],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0:
                # 检查是否是PipeWire
                if 'PipeWire' in result.stdout:
                    return True
                # 或者PulseAudio兼容层
                elif 'Server Name' in result.stdout:
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查PipeWire可用性失败: {e}")
            return False


def test_pipewire_device_manager():
    """测试PipeWire设备管理器"""
    print("测试PipeWire设备管理器...")
    
    manager = PipeWireDeviceManager()
    
    # 检查PipeWire可用性
    if not manager.is_pipewire_available():
        print("❌ PipeWire不可用")
        return
    
    print("✅ PipeWire可用")
    
    # 发现输入设备
    input_devices = manager.discover_devices(DeviceType.INPUT, refresh_cache=True)
    print(f"\n📱 发现 {len(input_devices)} 个输入设备:")
    for device in input_devices:
        print(f"  {device}")
    
    # 查找USB Gadget设备
    usb_gadget = manager.get_usb_gadget_device()
    if usb_gadget:
        print(f"\n🔥 USB Gadget设备: {usb_gadget}")
    else:
        print("\n❌ 未找到USB Gadget设备")
    
    # 获取默认设备
    default_device = manager.get_default_input_device()
    if default_device:
        print(f"\n🎯 默认输入设备: {default_device}")


if __name__ == "__main__":
    test_pipewire_device_manager()
