#!/usr/bin/env python3
"""
PipeWire音频捕获
使用PipeWire/PulseAudio兼容层进行音频捕获，避免设备冲突
"""

import logging
import numpy as np
import sounddevice as sd
import time
from typing import Optional, Callable, Dict, Any, List
from dataclasses import dataclass
from enum import Enum

from .pipewire_device_manager import PipeWireDeviceManager, PipeWireDevice, DeviceType


class CaptureState(Enum):
    """捕获状态"""
    IDLE = "idle"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


@dataclass
class CaptureConfig:
    """捕获配置"""
    sample_rate: int = 16000
    channels: int = 1
    blocksize: int = 1024
    dtype: type = np.float32
    buffer_size: int = 10
    auto_recover: bool = True
    max_retry_count: int = 3


class PipeWireAudioCapture:
    """PipeWire音频捕获器"""
    
    def __init__(self, config: CaptureConfig = None, callback: Optional[Callable] = None):
        """
        初始化PipeWire音频捕获器
        
        Args:
            config: 捕获配置
            callback: 音频回调函数
        """
        self.config = config or CaptureConfig()
        self.callback = callback
        self.logger = logging.getLogger(__name__)
        
        # 设备管理
        self.device_manager = PipeWireDeviceManager()
        self.current_device: Optional[PipeWireDevice] = None
        self.audio_stream: Optional[sd.InputStream] = None
        
        # 状态管理
        self.state = CaptureState.IDLE
        self.retry_count = 0
        self.last_error: Optional[str] = None
        self.start_time: Optional[float] = None
        
        # 统计信息
        self.stats = {
            'total_frames': 0,
            'callback_count': 0,
            'error_count': 0,
            'last_callback_time': None
        }
    
    def start(self, device: Optional[PipeWireDevice] = None, 
              prefer_usb_gadget: bool = True) -> bool:
        """
        启动音频捕获
        
        Args:
            device: 指定设备，None则自动选择
            prefer_usb_gadget: 是否优先选择USB Gadget设备
            
        Returns:
            bool: 是否启动成功
        """
        try:
            if self.state == CaptureState.RUNNING:
                self.logger.warning("音频捕获已在运行")
                return True
            
            self.state = CaptureState.STARTING
            self.logger.info("启动PipeWire音频捕获...")
            
            # 检查PipeWire可用性
            if not self.device_manager.is_pipewire_available():
                raise RuntimeError("PipeWire不可用")
            
            # 选择设备
            if device is None:
                device = self._select_best_device(prefer_usb_gadget)
            
            if device is None:
                raise RuntimeError("未找到可用的音频设备")
            
            self.current_device = device
            self.logger.info(f"选择设备: {device}")
            
            # 创建音频流
            self._create_audio_stream()
            
            # 启动音频流
            self.audio_stream.start()
            
            self.state = CaptureState.RUNNING
            self.start_time = time.time()
            self.retry_count = 0
            self.last_error = None
            
            self.logger.info("✅ PipeWire音频捕获启动成功")
            return True
            
        except Exception as e:
            error_msg = f"启动音频捕获失败: {e}"
            self.logger.error(error_msg)
            self.last_error = error_msg
            self.state = CaptureState.ERROR
            self._cleanup()
            return False
    
    def stop(self):
        """停止音频捕获"""
        try:
            if self.state == CaptureState.IDLE:
                return
            
            self.state = CaptureState.STOPPING
            self.logger.info("停止PipeWire音频捕获...")
            
            self._cleanup()
            
            self.state = CaptureState.IDLE
            self.current_device = None
            
            # 记录运行时间
            if self.start_time:
                runtime = time.time() - self.start_time
                self.logger.info(f"✅ 音频捕获已停止，运行时间: {runtime:.1f}秒")
                self.start_time = None
            
        except Exception as e:
            self.logger.error(f"停止音频捕获失败: {e}")
            self.state = CaptureState.ERROR
    
    def _select_best_device(self, prefer_usb_gadget: bool) -> Optional[PipeWireDevice]:
        """选择最佳设备"""
        try:
            # 获取输入设备列表
            devices = self.device_manager.discover_devices(
                DeviceType.INPUT, 
                refresh_cache=True
            )
            
            if not devices:
                self.logger.error("未发现任何输入设备")
                return None
            
            # 如果优先选择USB Gadget设备
            if prefer_usb_gadget:
                for device in devices:
                    if device.is_usb_gadget:
                        self.logger.info(f"选择USB Gadget设备: {device.description}")
                        return device
            
            # 选择第一个可用设备
            for device in devices:
                if device.state != CaptureState.ERROR:
                    self.logger.info(f"选择设备: {device.description}")
                    return device
            
            self.logger.warning("所有设备都不可用，选择第一个设备")
            return devices[0]
            
        except Exception as e:
            self.logger.error(f"选择设备失败: {e}")
            return None
    
    def _create_audio_stream(self):
        """创建音频流"""
        try:
            if self.current_device is None:
                raise RuntimeError("未选择设备")

            # 策略1：如果是USB Gadget设备，先设置为默认设备，然后使用pulse
            if self.current_device.is_usb_gadget:
                self.logger.info("设置USB Gadget为默认输入设备...")
                if self.device_manager.set_default_input_device(self.current_device):
                    device_param = "pulse"  # 使用pulse访问默认设备
                    self.logger.info("使用pulse设备访问USB Gadget")
                else:
                    # 回退到直接使用设备名称
                    device_param = self.current_device.name
                    self.logger.warning("设置默认设备失败，尝试直接访问")
            else:
                # 非USB Gadget设备，尝试直接使用设备名称
                device_param = self.current_device.name

            self.logger.debug(f"创建音频流: {device_param}")

            self.audio_stream = sd.InputStream(
                device=device_param,
                samplerate=self.config.sample_rate,
                channels=self.config.channels,
                blocksize=self.config.blocksize,
                dtype=self.config.dtype,
                callback=self._audio_callback
            )

        except Exception as e:
            self.logger.error(f"创建音频流失败: {e}")
            raise
    
    def _audio_callback(self, indata: np.ndarray, frames: int, 
                       time_info: Any, status: sd.CallbackFlags):
        """音频回调函数"""
        try:
            # 更新统计信息
            self.stats['total_frames'] += frames
            self.stats['callback_count'] += 1
            self.stats['last_callback_time'] = time.time()
            
            # 检查状态
            if status:
                self.logger.warning(f"音频回调状态警告: {status}")
                self.stats['error_count'] += 1
            
            # 调用用户回调
            if self.callback and self.state == CaptureState.RUNNING:
                try:
                    self.callback(indata, frames, time_info, status)
                except Exception as e:
                    self.logger.error(f"用户音频回调失败: {e}")
                    self.stats['error_count'] += 1
            
        except Exception as e:
            self.logger.error(f"音频回调处理失败: {e}")
            self.stats['error_count'] += 1
    
    def _cleanup(self):
        """清理资源"""
        try:
            if self.audio_stream:
                self.audio_stream.stop()
                self.audio_stream.close()
                self.audio_stream = None
                
        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")
    
    def restart(self) -> bool:
        """重启音频捕获"""
        try:
            self.logger.info("重启音频捕获...")
            
            # 保存当前设备
            device = self.current_device
            
            # 停止当前捕获
            self.stop()
            
            # 等待一段时间
            time.sleep(0.5)
            
            # 重新启动
            return self.start(device)
            
        except Exception as e:
            self.logger.error(f"重启音频捕获失败: {e}")
            return False
    
    def auto_recover(self) -> bool:
        """自动恢复"""
        try:
            if not self.config.auto_recover:
                return False
            
            if self.retry_count >= self.config.max_retry_count:
                self.logger.error(f"达到最大重试次数 ({self.config.max_retry_count})")
                return False
            
            self.retry_count += 1
            self.logger.info(f"尝试自动恢复 (第{self.retry_count}次)")
            
            return self.restart()
            
        except Exception as e:
            self.logger.error(f"自动恢复失败: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取捕获状态"""
        return {
            'state': self.state.value,
            'device': {
                'id': self.current_device.id if self.current_device else None,
                'name': self.current_device.name if self.current_device else None,
                'description': self.current_device.description if self.current_device else None,
                'is_usb_gadget': self.current_device.is_usb_gadget if self.current_device else False
            },
            'config': {
                'sample_rate': self.config.sample_rate,
                'channels': self.config.channels,
                'blocksize': self.config.blocksize
            },
            'stats': self.stats.copy(),
            'runtime': time.time() - self.start_time if self.start_time else 0,
            'retry_count': self.retry_count,
            'last_error': self.last_error
        }
    
    def get_available_devices(self) -> List[PipeWireDevice]:
        """获取可用设备列表"""
        return self.device_manager.discover_devices(DeviceType.INPUT, refresh_cache=True)
    
    def switch_device(self, device: PipeWireDevice) -> bool:
        """切换设备"""
        try:
            self.logger.info(f"切换到设备: {device.description}")
            
            # 停止当前捕获
            self.stop()
            
            # 启动新设备
            return self.start(device)
            
        except Exception as e:
            self.logger.error(f"切换设备失败: {e}")
            return False
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.stop()


class PipeWireAudioStream:
    """兼容sounddevice.InputStream接口的PipeWire音频流"""
    
    def __init__(self, device=None, samplerate=16000, channels=1, 
                 blocksize=1024, callback=None, dtype=np.float32, **kwargs):
        """
        创建PipeWire音频流
        
        Args:
            device: 设备信息或设备名称
            samplerate: 采样率
            channels: 声道数
            blocksize: 块大小
            callback: 音频回调函数
            dtype: 数据类型
        """
        config = CaptureConfig(
            sample_rate=samplerate,
            channels=channels,
            blocksize=blocksize,
            dtype=dtype
        )
        
        self.capture = PipeWireAudioCapture(config, callback)
        self.device = device
        self.logger = logging.getLogger(__name__)
    
    def start(self):
        """启动音频流"""
        # 解析设备参数
        target_device = None
        prefer_usb_gadget = True
        
        if self.device is not None:
            if isinstance(self.device, str):
                # 设备名称
                devices = self.capture.get_available_devices()
                for dev in devices:
                    if dev.name == self.device:
                        target_device = dev
                        break
            elif hasattr(self.device, 'is_usb_gadget'):
                # 设备信息对象
                prefer_usb_gadget = self.device.is_usb_gadget
        
        success = self.capture.start(target_device, prefer_usb_gadget)
        if not success:
            raise RuntimeError("启动PipeWire音频流失败")
    
    def stop(self):
        """停止音频流"""
        self.capture.stop()
    
    def close(self):
        """关闭音频流"""
        self.capture.stop()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


def test_pipewire_audio_capture():
    """测试PipeWire音频捕获"""
    import time
    
    print("测试PipeWire音频捕获...")
    
    def audio_callback(indata, frames, time_info, status):
        if status:
            print(f"音频状态: {status}")
        
        volume = np.sqrt(np.mean(indata**2))
        if volume > 0.01:
            print(f"音频活动: {volume:.3f}")
    
    try:
        config = CaptureConfig(
            sample_rate=16000,
            channels=1,
            blocksize=1024
        )
        
        capture = PipeWireAudioCapture(config, audio_callback)
        
        if capture.start(prefer_usb_gadget=True):
            print("✅ PipeWire捕获启动成功")
            
            # 显示状态
            status = capture.get_status()
            print(f"设备: {status['device']['description']}")
            print(f"状态: {status['state']}")
            
            print("监听5秒...")
            time.sleep(5)
            
            capture.stop()
            print("✅ PipeWire捕获测试完成")
        else:
            print("❌ PipeWire捕获启动失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    test_pipewire_audio_capture()
