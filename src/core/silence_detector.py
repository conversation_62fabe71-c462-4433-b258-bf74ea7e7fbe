#!/usr/bin/env python3
"""
静音检测器
基于音频能量和零交叉率的语音活动检测
"""

import numpy as np
import time
from typing import Optional, Tuple, List
from dataclasses import dataclass
import logging


@dataclass
class SilenceConfig:
    """静音检测配置"""
    energy_threshold: float = 0.01  # 能量阈值
    zero_crossing_threshold: int = 50  # 零交叉率阈值
    silence_duration_threshold: float = 2.0  # 静音持续时间阈值（秒）
    speech_duration_threshold: float = 0.5  # 语音持续时间阈值（秒）
    window_size: int = 1024  # 分析窗口大小
    sample_rate: int = 16000  # 采样率


@dataclass
class VoiceActivityResult:
    """语音活动检测结果"""
    is_speech: bool  # 是否为语音
    energy: float  # 音频能量
    zero_crossing_rate: int  # 零交叉率
    confidence: float  # 检测置信度
    timestamp: float  # 时间戳


class SilenceDetector:
    """静音检测器"""
    
    def __init__(self, config: SilenceConfig = None):
        """
        初始化静音检测器
        
        Args:
            config: 静音检测配置
        """
        self.config = config or SilenceConfig()
        self.logger = logging.getLogger(__name__)
        
        # 状态管理
        self.last_speech_time = None
        self.last_silence_time = None
        self.current_state = "unknown"  # "speech", "silence", "unknown"
        self.state_start_time = time.time()
        
        # 历史数据用于自适应阈值
        self.energy_history: List[float] = []
        self.zcr_history: List[int] = []
        self.max_history_size = 100
        
        # 自适应阈值
        self.adaptive_energy_threshold = self.config.energy_threshold
        self.adaptive_zcr_threshold = self.config.zero_crossing_threshold
    
    def analyze_audio_chunk(self, audio_data: np.ndarray) -> VoiceActivityResult:
        """
        分析音频块，检测语音活动
        
        Args:
            audio_data: 音频数据 (numpy array)
            
        Returns:
            VoiceActivityResult: 语音活动检测结果
        """
        try:
            current_time = time.time()
            
            # 计算音频特征
            energy = self._calculate_energy(audio_data)
            zcr = self._calculate_zero_crossing_rate(audio_data)
            
            # 更新历史数据
            self._update_history(energy, zcr)
            
            # 自适应阈值调整
            self._update_adaptive_thresholds()
            
            # 语音活动检测
            is_speech, confidence = self._detect_speech_activity(energy, zcr)
            
            # 更新状态
            self._update_state(is_speech, current_time)
            
            return VoiceActivityResult(
                is_speech=is_speech,
                energy=energy,
                zero_crossing_rate=zcr,
                confidence=confidence,
                timestamp=current_time
            )
            
        except Exception as e:
            self.logger.error(f"音频分析失败: {e}")
            return VoiceActivityResult(
                is_speech=False,
                energy=0.0,
                zero_crossing_rate=0,
                confidence=0.0,
                timestamp=time.time()
            )
    
    def _calculate_energy(self, audio_data: np.ndarray) -> float:
        """计算音频能量"""
        try:
            # 计算RMS能量
            energy = np.sqrt(np.mean(audio_data ** 2))
            return float(energy)
        except:
            return 0.0
    
    def _calculate_zero_crossing_rate(self, audio_data: np.ndarray) -> int:
        """计算零交叉率"""
        try:
            # 计算零交叉次数
            zero_crossings = np.sum(np.diff(np.sign(audio_data)) != 0)
            return int(zero_crossings)
        except:
            return 0
    
    def _update_history(self, energy: float, zcr: int):
        """更新历史数据"""
        self.energy_history.append(energy)
        self.zcr_history.append(zcr)
        
        # 限制历史数据大小
        if len(self.energy_history) > self.max_history_size:
            self.energy_history.pop(0)
        if len(self.zcr_history) > self.max_history_size:
            self.zcr_history.pop(0)
    
    def _update_adaptive_thresholds(self):
        """更新自适应阈值"""
        if len(self.energy_history) >= 10:
            # 基于历史数据调整能量阈值
            mean_energy = np.mean(self.energy_history[-20:])  # 最近20个样本
            std_energy = np.std(self.energy_history[-20:])
            
            # 自适应阈值 = 均值 + 标准差
            self.adaptive_energy_threshold = max(
                self.config.energy_threshold,
                mean_energy + std_energy * 0.5
            )
        
        if len(self.zcr_history) >= 10:
            # 基于历史数据调整零交叉率阈值
            mean_zcr = np.mean(self.zcr_history[-20:])
            self.adaptive_zcr_threshold = max(
                self.config.zero_crossing_threshold,
                mean_zcr * 0.8
            )
    
    def _detect_speech_activity(self, energy: float, zcr: int) -> Tuple[bool, float]:
        """
        检测语音活动
        
        Returns:
            Tuple[bool, float]: (是否为语音, 置信度)
        """
        # 能量检测
        energy_score = 1.0 if energy > self.adaptive_energy_threshold else 0.0
        
        # 零交叉率检测（语音通常有适中的零交叉率）
        zcr_score = 1.0 if zcr > self.adaptive_zcr_threshold else 0.0
        
        # 综合评分
        confidence = (energy_score + zcr_score) / 2.0
        
        # 判断是否为语音
        is_speech = confidence > 0.5
        
        return is_speech, confidence
    
    def _update_state(self, is_speech: bool, current_time: float):
        """更新状态"""
        if is_speech:
            self.last_speech_time = current_time
            if self.current_state != "speech":
                self.current_state = "speech"
                self.state_start_time = current_time
        else:
            self.last_silence_time = current_time
            if self.current_state != "silence":
                self.current_state = "silence"
                self.state_start_time = current_time
    
    def get_silence_duration(self) -> float:
        """获取当前静音持续时间"""
        if self.current_state == "silence" and self.last_silence_time:
            return time.time() - self.state_start_time
        return 0.0
    
    def get_speech_duration(self) -> float:
        """获取当前语音持续时间"""
        if self.current_state == "speech" and self.last_speech_time:
            return time.time() - self.state_start_time
        return 0.0
    
    def is_long_silence(self) -> bool:
        """检查是否为长时间静音"""
        silence_duration = self.get_silence_duration()
        return silence_duration >= self.config.silence_duration_threshold
    
    def is_speech_detected(self) -> bool:
        """检查是否检测到语音"""
        speech_duration = self.get_speech_duration()
        return (self.current_state == "speech" and 
                speech_duration >= self.config.speech_duration_threshold)
    
    def reset(self):
        """重置检测器状态"""
        self.last_speech_time = None
        self.last_silence_time = None
        self.current_state = "unknown"
        self.state_start_time = time.time()
        self.energy_history.clear()
        self.zcr_history.clear()
        self.adaptive_energy_threshold = self.config.energy_threshold
        self.adaptive_zcr_threshold = self.config.zero_crossing_threshold
    
    def get_status(self) -> dict:
        """获取检测器状态"""
        return {
            "current_state": self.current_state,
            "silence_duration": self.get_silence_duration(),
            "speech_duration": self.get_speech_duration(),
            "last_speech_time": self.last_speech_time,
            "last_silence_time": self.last_silence_time,
            "adaptive_energy_threshold": self.adaptive_energy_threshold,
            "adaptive_zcr_threshold": self.adaptive_zcr_threshold,
            "energy_history_size": len(self.energy_history),
            "zcr_history_size": len(self.zcr_history)
        }


def test_silence_detector():
    """测试静音检测器"""
    import matplotlib.pyplot as plt
    
    # 创建测试音频数据
    sample_rate = 16000
    duration = 5.0
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # 模拟语音和静音
    audio = np.zeros_like(t)
    # 1-2秒：语音
    audio[sample_rate:2*sample_rate] = 0.1 * np.sin(2 * np.pi * 440 * t[sample_rate:2*sample_rate])
    # 2-3秒：静音
    # 3-4秒：语音
    audio[3*sample_rate:4*sample_rate] = 0.05 * np.sin(2 * np.pi * 880 * t[3*sample_rate:4*sample_rate])
    
    # 测试检测器
    detector = SilenceDetector()
    chunk_size = 1024
    results = []
    
    for i in range(0, len(audio) - chunk_size, chunk_size):
        chunk = audio[i:i+chunk_size]
        result = detector.analyze_audio_chunk(chunk)
        results.append(result)
        print(f"时间: {result.timestamp:.2f}s, 语音: {result.is_speech}, "
              f"能量: {result.energy:.4f}, 置信度: {result.confidence:.2f}")
    
    print(f"\n最终状态: {detector.get_status()}")


if __name__ == "__main__":
    test_silence_detector()
