#!/usr/bin/env python3
"""
统一音频捕获模块
基于PipeWire架构的音频捕获，完全替代ALSA直接访问
"""

import logging
import numpy as np
from typing import Optional, Callable, Dict, Any, Union
from dataclasses import dataclass

from .pipewire_audio_capture import PipeWireAudioCapture, CaptureConfig, PipeWireAudioStream
from .pipewire_device_manager import PipeWireDeviceManager, PipeWireDevice, DeviceType


@dataclass
class AudioDeviceInfo:
    """统一的音频设备信息（向后兼容）"""
    device_id: int
    name: str
    channels: int
    sample_rate: float
    is_usb_gadget: bool = False
    is_available: bool = True
    occupying_processes: list = None
    
    # PipeWire特有属性
    pipewire_device: Optional[PipeWireDevice] = None
    
    def __post_init__(self):
        if self.occupying_processes is None:
            self.occupying_processes = []
    
    def __str__(self):
        status = "✅ 可用" if self.is_available else f"❌ 被占用"
        priority = "🔥 推荐" if self.is_usb_gadget else "📱 普通"
        return f"[{self.device_id}] {self.name} - {priority} - {status}"


class UnifiedAudioDeviceManager:
    """统一音频设备管理器（向后兼容接口）"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.pipewire_manager = PipeWireDeviceManager()
    
    def get_available_input_devices(self) -> list[AudioDeviceInfo]:
        """获取所有可用的音频输入设备（向后兼容）"""
        try:
            # 获取PipeWire设备
            pipewire_devices = self.pipewire_manager.discover_devices(
                DeviceType.INPUT, 
                refresh_cache=True
            )
            
            # 转换为统一格式
            unified_devices = []
            for pw_device in pipewire_devices:
                unified_device = AudioDeviceInfo(
                    device_id=pw_device.id,
                    name=pw_device.description,
                    channels=pw_device.channels,
                    sample_rate=float(pw_device.sample_rate),
                    is_usb_gadget=pw_device.is_usb_gadget,
                    is_available=True,  # PipeWire设备都是可用的
                    pipewire_device=pw_device
                )
                unified_devices.append(unified_device)
            
            # 按优先级排序
            unified_devices.sort(key=lambda d: (not d.is_usb_gadget, d.device_id))
            
            return unified_devices
            
        except Exception as e:
            self.logger.error(f"获取音频设备失败: {e}")
            return []
    
    def setup_audio_device(self, force_select: bool = False) -> Optional[AudioDeviceInfo]:
        """设置音频设备（向后兼容）"""
        try:
            devices = self.get_available_input_devices()
            
            if not devices:
                self.logger.error("未发现任何音频设备")
                return None
            
            # 优先选择USB Gadget设备
            for device in devices:
                if device.is_usb_gadget:
                    self.logger.info(f"自动选择USB Gadget设备: {device.name}")
                    return device
            
            # 如果没有USB Gadget，选择第一个设备
            selected_device = devices[0]
            self.logger.info(f"选择设备: {selected_device.name}")
            return selected_device
            
        except Exception as e:
            self.logger.error(f"设置音频设备失败: {e}")
            return None
    
    def select_device_interactive(self) -> Optional[AudioDeviceInfo]:
        """交互式设备选择（向后兼容）"""
        try:
            devices = self.get_available_input_devices()
            
            if not devices:
                print("❌ 未发现任何音频设备")
                return None
            
            print("\n🎤 可用音频设备:")
            print("=" * 60)
            for i, device in enumerate(devices):
                print(f"{i + 1}. {device}")
            print("=" * 60)
            
            while True:
                try:
                    choice = input(f"请选择设备 (1-{len(devices)}): ").strip()
                    if not choice:
                        continue
                    
                    index = int(choice) - 1
                    if 0 <= index < len(devices):
                        selected_device = devices[index]
                        print(f"✅ 已选择: {selected_device.name}")
                        return selected_device
                    else:
                        print(f"❌ 请输入 1-{len(devices)} 之间的数字")
                        
                except ValueError:
                    print("❌ 请输入有效数字")
                except KeyboardInterrupt:
                    print("\n❌ 用户取消选择")
                    return None
                    
        except Exception as e:
            self.logger.error(f"交互式设备选择失败: {e}")
            return None
    
    def save_device_preference(self, device: AudioDeviceInfo):
        """保存设备偏好（向后兼容）"""
        # 这里可以实现设备偏好保存逻辑
        self.logger.info(f"保存设备偏好: {device.name}")
    
    def is_pipewire_available(self) -> bool:
        """检查PipeWire是否可用"""
        return self.pipewire_manager.is_pipewire_available()


class UnifiedAudioCapture:
    """统一音频捕获（向后兼容接口）"""
    
    def __init__(self, device_info: AudioDeviceInfo, samplerate=16000, channels=1, 
                 blocksize=1024, callback=None, dtype=np.float32):
        """
        创建统一音频捕获
        
        Args:
            device_info: 设备信息
            samplerate: 采样率
            channels: 声道数
            blocksize: 块大小
            callback: 音频回调函数
            dtype: 数据类型
        """
        self.device_info = device_info
        self.logger = logging.getLogger(__name__)
        
        # 创建PipeWire捕获配置
        config = CaptureConfig(
            sample_rate=samplerate,
            channels=channels,
            blocksize=blocksize,
            dtype=dtype
        )
        
        # 创建PipeWire音频捕获
        self.pipewire_capture = PipeWireAudioCapture(config, callback)
        
    def start(self):
        """启动音频捕获"""
        try:
            # 使用PipeWire设备
            pipewire_device = self.device_info.pipewire_device
            
            if pipewire_device is None:
                raise RuntimeError("设备信息中缺少PipeWire设备")
            
            success = self.pipewire_capture.start(
                device=pipewire_device,
                prefer_usb_gadget=self.device_info.is_usb_gadget
            )
            
            if not success:
                raise RuntimeError("PipeWire音频捕获启动失败")
            
            self.logger.info(f"✅ 统一音频捕获启动成功: {self.device_info.name}")
            
        except Exception as e:
            self.logger.error(f"启动统一音频捕获失败: {e}")
            raise
    
    def stop(self):
        """停止音频捕获"""
        try:
            self.pipewire_capture.stop()
            self.logger.info("✅ 统一音频捕获已停止")
        except Exception as e:
            self.logger.error(f"停止统一音频捕获失败: {e}")
    
    def close(self):
        """关闭音频捕获"""
        self.stop()
    
    def get_status(self) -> Dict[str, Any]:
        """获取捕获状态"""
        return self.pipewire_capture.get_status()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


# 向后兼容的别名
AudioDeviceManager = UnifiedAudioDeviceManager


def create_audio_stream(device_info: Union[AudioDeviceInfo, str, int], 
                       samplerate=16000, channels=1, blocksize=1024, 
                       callback=None, dtype=np.float32):
    """
    创建音频流（统一接口）
    
    Args:
        device_info: 设备信息、设备名称或设备ID
        samplerate: 采样率
        channels: 声道数
        blocksize: 块大小
        callback: 音频回调函数
        dtype: 数据类型
        
    Returns:
        音频流对象
    """
    try:
        # 如果是AudioDeviceInfo对象，使用统一捕获
        if isinstance(device_info, AudioDeviceInfo):
            return UnifiedAudioCapture(
                device_info, samplerate, channels, blocksize, callback, dtype
            )
        
        # 否则使用PipeWire音频流
        return PipeWireAudioStream(
            device_info, samplerate, channels, blocksize, callback, dtype
        )
        
    except Exception as e:
        logging.getLogger(__name__).error(f"创建音频流失败: {e}")
        raise


def test_unified_audio_system():
    """测试统一音频系统"""
    import time
    
    print("测试统一音频系统...")
    
    # 测试设备管理器
    manager = UnifiedAudioDeviceManager()
    
    if not manager.is_pipewire_available():
        print("❌ PipeWire不可用")
        return
    
    print("✅ PipeWire可用")
    
    # 获取设备列表
    devices = manager.get_available_input_devices()
    print(f"\n📱 发现 {len(devices)} 个设备:")
    for device in devices:
        print(f"  {device}")
    
    # 自动选择设备
    selected_device = manager.setup_audio_device()
    if not selected_device:
        print("❌ 未能选择设备")
        return
    
    print(f"\n🎯 选择设备: {selected_device.name}")
    
    # 测试音频捕获
    def audio_callback(indata, frames, time_info, status):
        if status:
            print(f"音频状态: {status}")
        
        volume = np.sqrt(np.mean(indata**2))
        if volume > 0.01:
            print(f"音频活动: {volume:.3f}")
    
    try:
        capture = UnifiedAudioCapture(
            selected_device,
            samplerate=16000,
            channels=1,
            blocksize=1024,
            callback=audio_callback
        )
        
        capture.start()
        print("✅ 统一音频捕获启动成功，监听5秒...")
        time.sleep(5)
        capture.stop()
        print("✅ 统一音频系统测试完成")
        
    except Exception as e:
        print(f"❌ 统一音频系统测试失败: {e}")


if __name__ == "__main__":
    test_unified_audio_system()
