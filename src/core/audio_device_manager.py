#!/usr/bin/env python3
"""
音频设备管理器
负责音频设备的检测、选择、配置和进程管理
"""

import os
import subprocess
import signal
import time
import yaml
import sounddevice as sd
from typing import List, Dict, Optional, Tuple
from pathlib import Path
import logging


class AudioDeviceInfo:
    """音频设备信息"""
    def __init__(self, device_id: int, name: str, channels: int, sample_rate: float):
        self.device_id = device_id
        self.name = name
        self.channels = channels
        self.sample_rate = sample_rate
        self.is_usb_gadget = 'gadget' in name.lower() or 'usb gadget' in name.lower()
        self.is_available = True
        self.occupying_processes = []
        self.alsa_device = None  # ALSA设备标识符，如 "hw:1,0"
        self.is_alsa_only = False  # 是否仅通过ALSA检测到
        self.is_compatible = self._check_compatibility()

    def _check_compatibility(self) -> bool:
        """检查设备兼容性"""
        try:
            import sounddevice as sd
            # 测试设备是否支持16kHz采样率
            sd.check_input_settings(
                device=self.device_id,
                channels=1,
                samplerate=16000,
                dtype='float32'
            )
            return True
        except Exception:
            return False

    def __str__(self):
        status = "✅ 可用" if self.is_available else f"❌ 被占用 (进程: {', '.join(map(str, self.occupying_processes))})"
        priority = "🔥 推荐" if self.is_usb_gadget else "📱 普通"
        alsa_info = f" (ALSA: {self.alsa_device})" if self.alsa_device else ""
        return f"[{self.device_id}] {self.name}{alsa_info} - {priority} - {status}"


class AudioDeviceManager:
    """音频设备管理器"""
    
    def __init__(self, config_path: str = "config/audio_device.yaml"):
        self.config_path = Path(config_path)
        self.logger = logging.getLogger(__name__)
        self.config = self._load_config()
        
    def _load_config(self) -> Dict:
        """加载音频设备配置"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f) or {}
            return {}
        except Exception as e:
            self.logger.error(f"加载音频设备配置失败: {e}")
            return {}
    
    def _save_config(self):
        """保存音频设备配置"""
        try:
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
            self.logger.info(f"音频设备配置已保存到: {self.config_path}")
        except Exception as e:
            self.logger.error(f"保存音频设备配置失败: {e}")
    
    def get_available_input_devices(self) -> List[AudioDeviceInfo]:
        """获取所有可用的音频输入设备"""
        devices = []

        # 首先获取ALSA设备
        alsa_devices = self._get_alsa_devices()

        try:
            device_list = sd.query_devices()

            # 添加sounddevice检测到的设备
            for i, device in enumerate(device_list):
                if device['max_input_channels'] > 0:
                    device_info = AudioDeviceInfo(
                        device_id=i,
                        name=device['name'],
                        channels=device['max_input_channels'],
                        sample_rate=device['default_samplerate']
                    )

                    # 检查设备是否被占用
                    device_info.occupying_processes = self._get_device_processes(device_info.name)
                    device_info.is_available = len(device_info.occupying_processes) == 0

                    devices.append(device_info)

            # 检查ALSA设备并更新sounddevice设备信息
            for alsa_device in alsa_devices:
                # 查找对应的sounddevice设备
                found_device = None
                for existing in devices:
                    # 检查设备名称是否匹配
                    if alsa_device['name'].lower() in existing.name.lower() or \
                       alsa_device['device'] in existing.name:
                        found_device = existing
                        break

                if found_device:
                    # 更新现有设备的ALSA信息
                    found_device.alsa_device = alsa_device['device']
                    found_device.is_alsa_only = False
                else:
                    # 如果sounddevice没有检测到，添加为仅ALSA设备
                    new_id = max([d.device_id for d in devices], default=-1) + 1
                    device_info = AudioDeviceInfo(
                        device_id=new_id,
                        name=f"ALSA: {alsa_device['name']}",
                        channels=1,  # 假设单声道
                        sample_rate=16000  # 默认采样率
                    )
                    device_info.alsa_device = alsa_device['device']  # 保存ALSA设备标识
                    device_info.is_alsa_only = True  # 标记为仅ALSA设备

                    # 检查设备是否被占用
                    device_info.occupying_processes = self._get_device_processes(device_info.name)
                    device_info.is_available = len(device_info.occupying_processes) == 0

                    devices.append(device_info)

            # 按优先级排序：USB Gadget > 可用设备 > 被占用设备
            devices.sort(key=lambda d: (not d.is_usb_gadget, not d.is_available, d.device_id))

        except Exception as e:
            self.logger.error(f"获取音频设备列表失败: {e}")

        return devices

    def _get_alsa_devices(self) -> List[Dict]:
        """获取ALSA音频设备"""
        devices = []
        try:
            result = subprocess.run(
                ['arecord', '-l'],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'card' in line and 'device' in line:
                        # 解析类似 "card 1: Gadget [USB Gadget], device 0: USB Audio [USB Audio]" 的行
                        if 'Gadget' in line or 'USB' in line:
                            parts = line.split(':')
                            if len(parts) >= 2:
                                # 提取卡号和设备号
                                card_part = parts[0].strip()
                                device_part = parts[1].strip() if len(parts) > 1 else ""

                                # 提取卡号
                                card_num = None
                                if 'card' in card_part:
                                    try:
                                        card_num = int(card_part.split('card')[1].split(',')[0].strip())
                                    except:
                                        pass

                                # 提取设备号
                                device_num = 0
                                if 'device' in device_part:
                                    try:
                                        device_num = int(device_part.split('device')[1].split(':')[0].strip())
                                    except:
                                        pass

                                if card_num is not None:
                                    # 提取设备名称
                                    name_start = line.find('[')
                                    name_end = line.find(']')
                                    if name_start != -1 and name_end != -1:
                                        device_name = line[name_start+1:name_end]
                                        devices.append({
                                            'name': device_name,
                                            'device': f"hw:{card_num},{device_num}",
                                            'card': card_num,
                                            'device_num': device_num
                                        })

        except subprocess.TimeoutExpired:
            self.logger.warning("arecord命令超时")
        except FileNotFoundError:
            self.logger.warning("arecord命令未找到")
        except Exception as e:
            self.logger.debug(f"获取ALSA设备失败: {e}")

        return devices

    def _get_device_processes(self, device_name: str) -> List[int]:
        """获取占用指定设备的进程ID列表"""
        processes = []
        try:
            # 使用 lsof 查找占用音频设备的进程
            result = subprocess.run(
                ['lsof', '/dev/snd/*'], 
                capture_output=True, 
                text=True, 
                timeout=5
            )
            
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if device_name.lower() in line.lower() or 'pulse' in line.lower():
                        parts = line.split()
                        if len(parts) > 1 and parts[1].isdigit():
                            pid = int(parts[1])
                            if pid not in processes:
                                processes.append(pid)
        
        except subprocess.TimeoutExpired:
            self.logger.warning("lsof 命令超时")
        except FileNotFoundError:
            # 如果没有 lsof，尝试使用 fuser
            try:
                result = subprocess.run(
                    ['fuser', '/dev/snd/*'], 
                    capture_output=True, 
                    text=True, 
                    timeout=5
                )
                if result.returncode == 0:
                    for pid_str in result.stdout.split():
                        if pid_str.isdigit():
                            processes.append(int(pid_str))
            except:
                pass
        except Exception as e:
            self.logger.debug(f"检查设备占用进程失败: {e}")
        
        return processes
    
    def kill_device_processes(self, device_name: str, force: bool = False) -> bool:
        """终止占用指定设备的进程"""
        processes = self._get_device_processes(device_name)
        
        if not processes:
            self.logger.info(f"设备 {device_name} 没有被占用")
            return True
        
        print(f"🔄 发现 {len(processes)} 个进程占用设备 {device_name}: {processes}")
        
        success = True
        for pid in processes:
            try:
                # 首先尝试优雅终止
                print(f"   正在终止进程 {pid}...")
                os.kill(pid, signal.SIGTERM)
                
                # 等待进程退出
                time.sleep(1)
                
                # 检查进程是否还存在
                try:
                    os.kill(pid, 0)  # 检查进程是否存在
                    if force:
                        print(f"   强制终止进程 {pid}...")
                        os.kill(pid, signal.SIGKILL)
                        time.sleep(0.5)
                    else:
                        print(f"   进程 {pid} 仍在运行，使用 --force 参数强制终止")
                        success = False
                except ProcessLookupError:
                    print(f"   ✅ 进程 {pid} 已成功终止")
                    
            except ProcessLookupError:
                print(f"   ✅ 进程 {pid} 不存在")
            except PermissionError:
                print(f"   ❌ 没有权限终止进程 {pid}")
                success = False
            except Exception as e:
                print(f"   ❌ 终止进程 {pid} 失败: {e}")
                success = False
        
        return success
    
    def select_device_interactive(self) -> Optional[AudioDeviceInfo]:
        """交互式设备选择"""
        devices = self.get_available_input_devices()
        
        if not devices:
            print("❌ 没有找到可用的音频输入设备")
            return None
        
        print("\n🎤 可用的音频输入设备:")
        print("=" * 60)
        
        for i, device in enumerate(devices):
            print(f"{i + 1}. {device}")
        
        print("=" * 60)
        
        while True:
            try:
                choice = input("\n请选择音频设备 (输入数字): ").strip()
                
                if not choice.isdigit():
                    print("❌ 请输入有效的数字")
                    continue
                
                index = int(choice) - 1
                if 0 <= index < len(devices):
                    selected_device = devices[index]
                    
                    # 如果设备被占用，询问是否终止占用进程
                    if not selected_device.is_available:
                        kill_choice = input(f"\n设备被占用，是否终止占用进程? (y/N): ").strip().lower()
                        if kill_choice in ['y', 'yes']:
                            if self.kill_device_processes(selected_device.name, force=True):
                                print("✅ 成功释放设备")
                                selected_device.is_available = True
                                selected_device.occupying_processes = []
                            else:
                                print("❌ 释放设备失败")
                                continue
                        else:
                            print("⚠️ 设备仍被占用，可能影响使用")
                    
                    return selected_device
                else:
                    print("❌ 选择超出范围，请重新选择")
                    
            except KeyboardInterrupt:
                print("\n\n❌ 用户取消选择")
                return None
            except Exception as e:
                print(f"❌ 选择失败: {e}")
    
    def get_configured_device(self) -> Optional[AudioDeviceInfo]:
        """获取配置的音频设备"""
        if 'preferred_device' not in self.config:
            return None
        
        preferred = self.config['preferred_device']
        devices = self.get_available_input_devices()
        
        # 首先尝试按设备ID匹配
        if 'device_id' in preferred:
            for device in devices:
                if device.device_id == preferred['device_id']:
                    return device
        
        # 然后尝试按设备名称匹配
        if 'device_name' in preferred:
            for device in devices:
                if preferred['device_name'] in device.name:
                    return device
        
        return None
    
    def save_device_preference(self, device: AudioDeviceInfo):
        """保存设备偏好设置"""
        self.config['preferred_device'] = {
            'device_id': device.device_id,
            'device_name': device.name,
            'selected_at': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        self._save_config()
        print(f"✅ 已保存设备偏好: {device.name}")
    
    def setup_audio_device(self, force_select: bool = False) -> Optional[AudioDeviceInfo]:
        """设置音频设备（主要入口函数）"""
        # 如果不强制选择，先尝试使用配置的设备
        if not force_select:
            configured_device = self.get_configured_device()
            if configured_device:
                print(f"🎤 使用配置的音频设备: {configured_device.name}")
                
                # 检查设备是否被占用
                if not configured_device.is_available:
                    print(f"⚠️ 设备被占用，正在释放...")
                    if self.kill_device_processes(configured_device.name, force=True):
                        print("✅ 设备已释放")
                        configured_device.is_available = True
                    else:
                        print("❌ 设备释放失败，需要重新选择")
                        force_select = True
                
                if configured_device.is_available:
                    return configured_device
        
        # 交互式选择设备
        print("\n🔧 首次启动或需要重新配置音频设备")
        selected_device = self.select_device_interactive()
        
        if selected_device:
            self.save_device_preference(selected_device)
            return selected_device
        
        return None
