#!/usr/bin/env python3
"""
智能VAD管理器
基于FunASR的fsmn-vad模型实现智能语音活动检测和状态管理
解决唤醒后的语音收集问题
"""

import asyncio
import time
import logging
import numpy as np
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, field
from enum import Enum
import soundfile as sf
import tempfile
import os

from funasr import AutoModel


class VoiceState(Enum):
    """语音状态枚举"""
    WAITING_FOR_SPEECH = "waiting_for_speech"  # 唤醒后等待用户开始说话
    SPEECH_DETECTED = "speech_detected"        # 检测到用户开始说话
    SPEECH_ACTIVE = "speech_active"            # 用户正在说话
    SPEECH_ENDED = "speech_ended"              # 用户说话结束，等待静音确认
    TIMEOUT = "timeout"                        # 超时
    ERROR = "error"                            # 错误状态


@dataclass
class VoiceStateConfig:
    """语音状态配置"""
    # 超时配置
    waiting_timeout: float = 10.0              # 唤醒后等待语音的超时时间（秒）
    speech_end_timeout: float = 1.2            # 语音结束后的静音确认时间（秒）
    
    # VAD配置
    vad_chunk_size: int = 200                  # VAD分析块大小（毫秒）
    vad_model: str = "fsmn-vad"               # VAD模型名称
    
    # 语音检测配置
    min_speech_duration: float = 0.3           # 最小语音持续时间（秒）
    max_silence_in_speech: float = 0.5         # 语音中允许的最大静音时间（秒）
    
    # 音频配置
    sample_rate: int = 16000                   # 采样率
    
    # 调试配置
    enable_debug: bool = True                  # 启用调试输出


@dataclass
class VoiceActivity:
    """语音活动信息"""
    timestamp: float
    is_speech: bool
    confidence: float
    state: VoiceState
    duration: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)


class SmartVADManager:
    """智能VAD管理器"""
    
    def __init__(self, config: VoiceStateConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # VAD模型
        self.vad_model: Optional[AutoModel] = None
        
        # 状态管理
        self.current_state = VoiceState.WAITING_FOR_SPEECH
        self.state_start_time = 0.0
        self.last_speech_time = 0.0
        self.last_silence_time = 0.0
        
        # 音频缓冲
        self.audio_buffer: List[np.ndarray] = []
        self.buffer_lock = asyncio.Lock()
        
        # 语音活动历史
        self.speech_history: List[VoiceActivity] = []
        self.speech_segments: List[tuple] = []  # (start_time, end_time)
        
        # 回调函数
        self.state_change_callbacks: List[Callable] = []
        self.speech_activity_callbacks: List[Callable] = []
        
        # 控制标志
        self.is_active = False
        self.is_initialized = False
        
        # 任务管理
        self.monitoring_task: Optional[asyncio.Task] = None
        
    async def initialize(self) -> bool:
        """初始化VAD管理器"""
        try:
            if self.is_initialized:
                return True
            
            self.logger.info("正在初始化智能VAD管理器...")
            
            # 初始化FunASR VAD模型
            self.vad_model = AutoModel(
                model=self.config.vad_model,
                device="cpu",  # VAD模型通常在CPU上运行效果很好
                disable_update=True,
                disable_log=True,
                disable_pbar=True
            )
            
            self.is_initialized = True
            self.logger.info("智能VAD管理器初始化成功")
            
            if self.config.enable_debug:
                print("✅ 智能VAD管理器初始化成功")
            
            return True
            
        except Exception as e:
            self.logger.error(f"智能VAD管理器初始化失败: {e}")
            if self.config.enable_debug:
                print(f"❌ 智能VAD管理器初始化失败: {e}")
            return False
    
    async def start_voice_detection(self) -> bool:
        """开始语音检测（唤醒后调用）"""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            if self.is_active:
                await self.stop_voice_detection()
            
            # 重置状态
            self.current_state = VoiceState.WAITING_FOR_SPEECH
            self.state_start_time = time.time()
            self.last_speech_time = 0.0
            self.last_silence_time = 0.0
            
            # 清空缓冲区和历史
            async with self.buffer_lock:
                self.audio_buffer.clear()
            self.speech_history.clear()
            self.speech_segments.clear()
            
            # 启动监控任务
            self.is_active = True
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            
            if self.config.enable_debug:
                print("🎤 智能VAD：开始语音检测，等待用户说话...")
            
            # 通知状态变化
            await self._notify_state_change(VoiceState.WAITING_FOR_SPEECH)
            
            return True
            
        except Exception as e:
            self.logger.error(f"启动语音检测失败: {e}")
            return False
    
    async def stop_voice_detection(self):
        """停止语音检测"""
        try:
            self.is_active = False
            
            # 取消监控任务
            if self.monitoring_task and not self.monitoring_task.done():
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass
            
            if self.config.enable_debug:
                print("🛑 智能VAD：停止语音检测")
            
        except Exception as e:
            self.logger.error(f"停止语音检测失败: {e}")
    
    async def add_audio_chunk(self, audio_data: np.ndarray):
        """添加音频块进行分析"""
        try:
            if not self.is_active:
                return
            
            async with self.buffer_lock:
                self.audio_buffer.append(audio_data.copy())
                
                # 限制缓冲区大小（保留最近5秒的音频）
                max_chunks = int(5.0 * self.config.sample_rate / len(audio_data))
                if len(self.audio_buffer) > max_chunks:
                    self.audio_buffer.pop(0)
            
        except Exception as e:
            self.logger.error(f"添加音频块失败: {e}")
    
    async def _monitoring_loop(self):
        """监控循环"""
        try:
            while self.is_active:
                try:
                    # 检查是否有足够的音频数据进行分析
                    async with self.buffer_lock:
                        if len(self.audio_buffer) == 0:
                            await asyncio.sleep(0.1)
                            continue
                        
                        # 获取最近的音频数据进行VAD分析
                        recent_audio = np.concatenate(self.audio_buffer[-5:])  # 最近5个块
                    
                    # 进行VAD分析
                    vad_result = await self._analyze_audio_with_vad(recent_audio)
                    
                    # 更新状态机
                    await self._update_state_machine(vad_result)
                    
                    # 检查超时
                    await self._check_timeouts()
                    
                    # 短暂等待
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    self.logger.error(f"监控循环错误: {e}")
                    await asyncio.sleep(0.5)
                    
        except asyncio.CancelledError:
            if self.config.enable_debug:
                print("🔄 智能VAD：监控循环被取消")
        except Exception as e:
            self.logger.error(f"监控循环异常: {e}")
    
    async def _analyze_audio_with_vad(self, audio_data: np.ndarray) -> Dict[str, Any]:
        """使用FunASR VAD分析音频"""
        try:
            if len(audio_data) == 0:
                return {"segments": [], "has_speech": False}
            
            # 保存音频到临时文件
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_path = temp_file.name
                sf.write(temp_path, audio_data, self.config.sample_rate)
            
            try:
                # 使用FunASR VAD进行分析
                result = self.vad_model.generate(
                    input=temp_path,
                    cache={},
                    chunk_size=self.config.vad_chunk_size
                )
                
                # 解析VAD结果
                vad_segments = []
                has_speech = False
                
                if result and len(result) > 0:
                    # FunASR VAD返回格式: [[start_ms, end_ms], ...]
                    segments = result[0].get("value", []) if isinstance(result[0], dict) else result[0]
                    
                    for segment in segments:
                        if len(segment) >= 2:
                            start_ms, end_ms = segment[0], segment[1]
                            if end_ms > 0:  # 有效的语音段
                                vad_segments.append({
                                    "start": start_ms / 1000.0,  # 转换为秒
                                    "end": end_ms / 1000.0,
                                    "duration": (end_ms - start_ms) / 1000.0
                                })
                                has_speech = True
                
                return {
                    "segments": vad_segments,
                    "has_speech": has_speech,
                    "timestamp": time.time()
                }
                
            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_path)
                except:
                    pass
                    
        except Exception as e:
            self.logger.error(f"VAD分析失败: {e}")
            return {"segments": [], "has_speech": False}
    
    async def _update_state_machine(self, vad_result: Dict[str, Any]):
        """更新状态机"""
        try:
            current_time = time.time()
            has_speech = vad_result.get("has_speech", False)
            segments = vad_result.get("segments", [])
            
            # 记录语音活动
            activity = VoiceActivity(
                timestamp=current_time,
                is_speech=has_speech,
                confidence=1.0 if has_speech else 0.0,
                state=self.current_state,
                metadata={"segments": segments}
            )
            
            self.speech_history.append(activity)
            
            # 限制历史记录长度
            if len(self.speech_history) > 100:
                self.speech_history.pop(0)
            
            # 状态机逻辑
            if self.current_state == VoiceState.WAITING_FOR_SPEECH:
                if has_speech and segments:
                    # 检查是否有足够长的语音段
                    valid_speech = any(seg["duration"] >= self.config.min_speech_duration for seg in segments)
                    if valid_speech:
                        await self._transition_to_state(VoiceState.SPEECH_DETECTED)
                        self.last_speech_time = current_time
                        
                        if self.config.enable_debug:
                            print(f"🎤 智能VAD：检测到语音开始")
            
            elif self.current_state == VoiceState.SPEECH_DETECTED:
                if has_speech:
                    # 继续检测到语音，转换到活跃状态
                    await self._transition_to_state(VoiceState.SPEECH_ACTIVE)
                    self.last_speech_time = current_time
                    
                    if self.config.enable_debug:
                        print(f"🗣️ 智能VAD：用户正在说话")
                else:
                    # 短暂没有语音，可能是停顿，继续等待
                    pass
            
            elif self.current_state == VoiceState.SPEECH_ACTIVE:
                if has_speech:
                    # 继续有语音活动
                    self.last_speech_time = current_time
                else:
                    # 没有语音，可能是说话结束
                    await self._transition_to_state(VoiceState.SPEECH_ENDED)
                    self.last_silence_time = current_time
                    
                    if self.config.enable_debug:
                        print(f"🤫 智能VAD：语音结束，等待静音确认...")
            
            elif self.current_state == VoiceState.SPEECH_ENDED:
                if has_speech:
                    # 又开始说话，回到活跃状态
                    await self._transition_to_state(VoiceState.SPEECH_ACTIVE)
                    self.last_speech_time = current_time
                    
                    if self.config.enable_debug:
                        print(f"🗣️ 智能VAD：继续说话")
                else:
                    # 继续静音，检查是否达到确认时间
                    silence_duration = current_time - self.last_silence_time
                    if silence_duration >= self.config.speech_end_timeout:
                        # 确认语音结束，可以开始转写
                        await self._finalize_speech_collection()
            
            # 通知语音活动
            await self._notify_speech_activity(activity)
            
        except Exception as e:
            self.logger.error(f"更新状态机失败: {e}")
    
    async def _check_timeouts(self):
        """检查超时"""
        try:
            current_time = time.time()
            state_duration = current_time - self.state_start_time
            
            # 等待语音超时
            if (self.current_state == VoiceState.WAITING_FOR_SPEECH and 
                state_duration >= self.config.waiting_timeout):
                
                await self._transition_to_state(VoiceState.TIMEOUT)
                
                if self.config.enable_debug:
                    print(f"⏰ 智能VAD：等待语音超时（{self.config.waiting_timeout}秒），返回待唤醒状态")
                
                # 通知超时
                await self._notify_timeout("waiting_for_speech")
            
        except Exception as e:
            self.logger.error(f"检查超时失败: {e}")
    
    async def _transition_to_state(self, new_state: VoiceState):
        """状态转换"""
        try:
            if self.current_state != new_state:
                old_state = self.current_state
                self.current_state = new_state
                self.state_start_time = time.time()
                
                if self.config.enable_debug:
                    print(f"🔄 智能VAD：状态转换 {old_state.value} -> {new_state.value}")
                
                # 通知状态变化
                await self._notify_state_change(new_state, old_state)
                
        except Exception as e:
            self.logger.error(f"状态转换失败: {e}")
    
    async def _finalize_speech_collection(self):
        """完成语音收集"""
        try:
            if self.config.enable_debug:
                print(f"✅ 智能VAD：语音收集完成，开始转写")
            
            # 停止检测
            await self.stop_voice_detection()
            
            # 通知语音收集完成
            await self._notify_speech_collection_complete()
            
        except Exception as e:
            self.logger.error(f"完成语音收集失败: {e}")
    
    # 回调管理
    def add_state_change_callback(self, callback: Callable):
        """添加状态变化回调"""
        self.state_change_callbacks.append(callback)
    
    def add_speech_activity_callback(self, callback: Callable):
        """添加语音活动回调"""
        self.speech_activity_callbacks.append(callback)
    
    async def _notify_state_change(self, new_state: VoiceState, old_state: VoiceState = None):
        """通知状态变化"""
        try:
            for callback in self.state_change_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(new_state, old_state)
                    else:
                        callback(new_state, old_state)
                except Exception as e:
                    self.logger.error(f"状态变化回调错误: {e}")
        except Exception as e:
            self.logger.error(f"通知状态变化失败: {e}")
    
    async def _notify_speech_activity(self, activity: VoiceActivity):
        """通知语音活动"""
        try:
            for callback in self.speech_activity_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(activity)
                    else:
                        callback(activity)
                except Exception as e:
                    self.logger.error(f"语音活动回调错误: {e}")
        except Exception as e:
            self.logger.error(f"通知语音活动失败: {e}")
    
    async def _notify_timeout(self, timeout_type: str):
        """通知超时"""
        try:
            # 这里可以发送事件或调用回调
            if self.config.enable_debug:
                print(f"⏰ 智能VAD：超时类型 - {timeout_type}")
        except Exception as e:
            self.logger.error(f"通知超时失败: {e}")
    
    async def _notify_speech_collection_complete(self):
        """通知语音收集完成"""
        try:
            # 这里可以发送事件或调用回调
            if self.config.enable_debug:
                print(f"✅ 智能VAD：语音收集完成通知")
        except Exception as e:
            self.logger.error(f"通知语音收集完成失败: {e}")
    
    # 状态查询方法
    def get_current_state(self) -> VoiceState:
        """获取当前状态"""
        return self.current_state
    
    def is_waiting_for_speech(self) -> bool:
        """是否在等待语音"""
        return self.current_state == VoiceState.WAITING_FOR_SPEECH
    
    def is_speech_active(self) -> bool:
        """是否有语音活动"""
        return self.current_state in [VoiceState.SPEECH_DETECTED, VoiceState.SPEECH_ACTIVE]
    
    def is_speech_ended(self) -> bool:
        """语音是否结束"""
        return self.current_state == VoiceState.SPEECH_ENDED
    
    def get_state_duration(self) -> float:
        """获取当前状态持续时间"""
        return time.time() - self.state_start_time
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            return {
                "current_state": self.current_state.value,
                "state_duration": self.get_state_duration(),
                "is_active": self.is_active,
                "speech_history_count": len(self.speech_history),
                "audio_buffer_size": len(self.audio_buffer),
                "last_speech_time": self.last_speech_time,
                "last_silence_time": self.last_silence_time,
                "config": {
                    "waiting_timeout": self.config.waiting_timeout,
                    "speech_end_timeout": self.config.speech_end_timeout,
                    "min_speech_duration": self.config.min_speech_duration
                }
            }
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}
