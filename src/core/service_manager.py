#!/usr/bin/env python3
"""
服务管理器
负责所有服务的初始化、启动、停止和健康检查
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from ..services.base_service import BaseService, ServiceStatus
from ..services.event_bus import EventBus
from ..services.wake_word import WakeWordService
from ..services.asr_service import ASRService
from .state_machine import StateMachine
from ..services.conversation_service import ConversationService


class ServiceType(Enum):
    """服务类型"""
    EVENT_BUS = "event_bus"
    WAKE_WORD = "wake_word"
    ASR = "asr"
    STATE_MACHINE = "state_machine"
    CONVERSATION = "conversation"


@dataclass
class ServiceInfo:
    """服务信息"""
    name: str
    service_type: ServiceType
    instance: BaseService
    dependencies: List[ServiceType]
    startup_order: int
    is_critical: bool = True


class ServiceManager:
    """服务管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 服务实例
        self.services: Dict[ServiceType, ServiceInfo] = {}
        self.event_bus: Optional[EventBus] = None
        
        # 状态管理
        self.is_initialized = False
        self.is_running = False
        self.startup_time = 0.0
        
        # 健康检查
        self.health_check_interval = config.get('health_check_interval', 30.0)
        self.health_check_task: Optional[asyncio.Task] = None
        
        # 统计信息
        self.startup_count = 0
        self.restart_count = 0
        self.error_count = 0
        
    async def initialize(self) -> bool:
        """初始化所有服务"""
        try:
            self.logger.info("开始初始化服务管理器...")
            start_time = time.time()
            
            # 1. 初始化事件总线
            if not await self._initialize_event_bus():
                return False
            
            # 2. 初始化其他服务
            if not await self._initialize_services():
                return False
            
            # 3. 验证服务依赖
            if not self._validate_dependencies():
                return False
            
            self.is_initialized = True
            init_time = time.time() - start_time
            self.logger.info(f"服务管理器初始化完成，耗时: {init_time:.2f}s")
            
            return True
            
        except Exception as e:
            self.logger.error(f"服务管理器初始化失败: {e}")
            return False
    
    async def start_all_services(self) -> bool:
        """启动所有服务"""
        try:
            if not self.is_initialized:
                self.logger.error("服务管理器未初始化")
                return False
            
            self.logger.info("开始启动所有服务...")
            self.startup_time = time.time()
            
            # 按启动顺序启动服务
            services_by_order = sorted(
                self.services.values(), 
                key=lambda s: s.startup_order
            )
            
            for service_info in services_by_order:
                if not await self._start_service(service_info):
                    if service_info.is_critical:
                        self.logger.error(f"关键服务启动失败: {service_info.name}")
                        return False
                    else:
                        self.logger.warning(f"非关键服务启动失败: {service_info.name}")
            
            # 启动健康检查
            self.health_check_task = asyncio.create_task(self._health_check_loop())
            
            self.is_running = True
            self.startup_count += 1
            startup_duration = time.time() - self.startup_time
            
            self.logger.info(f"所有服务启动完成，耗时: {startup_duration:.2f}s")
            print("🚀 语音助手系统已启动")
            
            return True
            
        except Exception as e:
            self.logger.error(f"启动服务失败: {e}")
            return False
    
    async def stop_all_services(self) -> bool:
        """停止所有服务"""
        try:
            self.logger.info("开始停止所有服务...")
            
            # 停止健康检查
            if self.health_check_task:
                self.health_check_task.cancel()
                try:
                    await self.health_check_task
                except asyncio.CancelledError:
                    pass
                self.health_check_task = None
            
            # 按相反顺序停止服务
            services_by_order = sorted(
                self.services.values(), 
                key=lambda s: s.startup_order,
                reverse=True
            )
            
            for service_info in services_by_order:
                await self._stop_service(service_info)
            
            # 停止事件总线
            if self.event_bus:
                await self.event_bus.shutdown()
            
            self.is_running = False
            self.logger.info("所有服务已停止")
            print("🛑 语音助手系统已停止")
            
            return True
            
        except Exception as e:
            self.logger.error(f"停止服务失败: {e}")
            return False
    
    async def _initialize_event_bus(self) -> bool:
        """初始化事件总线"""
        try:
            event_bus_config = self.config.get('event_bus', {})
            max_queue_size = event_bus_config.get('max_queue_size', 1000)
            
            self.event_bus = EventBus(max_queue_size)
            await self.event_bus.initialize()
            
            self.logger.info("事件总线初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"事件总线初始化失败: {e}")
            return False
    
    async def _initialize_services(self) -> bool:
        """初始化所有服务"""
        try:
            # 唤醒词服务
            wake_word_config = self.config.get('wake_word', {})
            wake_word_service = WakeWordService(wake_word_config, self.event_bus)
            await wake_word_service.initialize()
            
            self.services[ServiceType.WAKE_WORD] = ServiceInfo(
                name="唤醒词服务",
                service_type=ServiceType.WAKE_WORD,
                instance=wake_word_service,
                dependencies=[ServiceType.EVENT_BUS],
                startup_order=1,
                is_critical=True
            )
            
            # ASR服务
            asr_config = self.config.get('asr', {})
            asr_service = ASRService(asr_config, self.event_bus)
            await asr_service.initialize()
            
            self.services[ServiceType.ASR] = ServiceInfo(
                name="语音识别服务",
                service_type=ServiceType.ASR,
                instance=asr_service,
                dependencies=[ServiceType.EVENT_BUS],
                startup_order=2,
                is_critical=True
            )
            
            # 状态机服务
            state_machine_config = self.config.get('state_machine', {})
            state_machine = StateMachine(state_machine_config, self.event_bus, asr_service)
            await state_machine.initialize()
            
            self.services[ServiceType.STATE_MACHINE] = ServiceInfo(
                name="状态机服务",
                service_type=ServiceType.STATE_MACHINE,
                instance=state_machine,
                dependencies=[ServiceType.EVENT_BUS, ServiceType.ASR],
                startup_order=3,
                is_critical=True
            )
            
            # 对话管理服务
            conversation_config = self.config.get('conversation', {})
            conversation_service = ConversationService(conversation_config, self.event_bus)
            await conversation_service.initialize()
            
            self.services[ServiceType.CONVERSATION] = ServiceInfo(
                name="对话管理服务",
                service_type=ServiceType.CONVERSATION,
                instance=conversation_service,
                dependencies=[ServiceType.EVENT_BUS, ServiceType.STATE_MACHINE],
                startup_order=4,
                is_critical=True
            )
            
            self.logger.info("所有服务初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"服务初始化失败: {e}")
            return False
    
    def _validate_dependencies(self) -> bool:
        """验证服务依赖"""
        try:
            for service_info in self.services.values():
                for dependency in service_info.dependencies:
                    if dependency not in self.services and dependency != ServiceType.EVENT_BUS:
                        self.logger.error(f"服务 {service_info.name} 的依赖 {dependency} 未找到")
                        return False
            
            self.logger.info("服务依赖验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"服务依赖验证失败: {e}")
            return False
    
    async def _start_service(self, service_info: ServiceInfo) -> bool:
        """启动单个服务"""
        try:
            self.logger.info(f"启动服务: {service_info.name}")
            
            if not await service_info.instance.start():
                self.logger.error(f"服务启动失败: {service_info.name}")
                return False
            
            self.logger.info(f"服务启动成功: {service_info.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动服务 {service_info.name} 失败: {e}")
            return False
    
    async def _stop_service(self, service_info: ServiceInfo) -> bool:
        """停止单个服务"""
        try:
            self.logger.info(f"停止服务: {service_info.name}")
            
            if not await service_info.instance.stop():
                self.logger.warning(f"服务停止失败: {service_info.name}")
                return False
            
            self.logger.info(f"服务停止成功: {service_info.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"停止服务 {service_info.name} 失败: {e}")
            return False
    
    async def _health_check_loop(self):
        """健康检查循环"""
        try:
            while self.is_running:
                await asyncio.sleep(self.health_check_interval)
                await self._perform_health_check()
                
        except asyncio.CancelledError:
            pass
        except Exception as e:
            self.logger.error(f"健康检查循环失败: {e}")
    
    async def _perform_health_check(self):
        """执行健康检查"""
        try:
            unhealthy_services = []
            
            for service_info in self.services.values():
                try:
                    if not await service_info.instance._health_check_impl():
                        unhealthy_services.append(service_info.name)
                except Exception as e:
                    self.logger.error(f"服务 {service_info.name} 健康检查失败: {e}")
                    unhealthy_services.append(service_info.name)
            
            if unhealthy_services:
                self.logger.warning(f"发现不健康的服务: {unhealthy_services}")
                self.error_count += 1
            else:
                self.logger.debug("所有服务健康检查通过")
                
        except Exception as e:
            self.logger.error(f"健康检查执行失败: {e}")
    
    # 公共接口方法
    def get_service(self, service_type: ServiceType) -> Optional[BaseService]:
        """获取服务实例"""
        service_info = self.services.get(service_type)
        return service_info.instance if service_info else None
    
    def get_service_status(self, service_type: ServiceType) -> Optional[ServiceStatus]:
        """获取服务状态"""
        service = self.get_service(service_type)
        return service.status if service else None
    
    def get_all_service_status(self) -> Dict[str, str]:
        """获取所有服务状态"""
        status = {}
        for service_type, service_info in self.services.items():
            status[service_info.name] = service_info.instance.status.value
        return status
    
    def get_system_statistics(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        uptime = time.time() - self.startup_time if self.is_running else 0
        
        return {
            "is_running": self.is_running,
            "uptime": uptime,
            "startup_count": self.startup_count,
            "restart_count": self.restart_count,
            "error_count": self.error_count,
            "service_count": len(self.services),
            "healthy_services": sum(1 for s in self.services.values() 
                                  if s.instance.status == ServiceStatus.RUNNING)
        }
    
    async def restart_service(self, service_type: ServiceType) -> bool:
        """重启指定服务"""
        try:
            service_info = self.services.get(service_type)
            if not service_info:
                self.logger.error(f"服务类型 {service_type} 不存在")
                return False
            
            self.logger.info(f"重启服务: {service_info.name}")
            
            # 停止服务
            await self._stop_service(service_info)
            
            # 等待一段时间
            await asyncio.sleep(1.0)
            
            # 启动服务
            if await self._start_service(service_info):
                self.restart_count += 1
                self.logger.info(f"服务重启成功: {service_info.name}")
                return True
            else:
                self.logger.error(f"服务重启失败: {service_info.name}")
                return False
                
        except Exception as e:
            self.logger.error(f"重启服务失败: {e}")
            return False
