"""
性能监控器
支持关键指标收集、性能分析和告警
"""

import asyncio
import logging
import time
import psutil
import threading
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum
import json


class MetricType(Enum):
    """指标类型"""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


@dataclass
class Metric:
    """指标数据"""
    name: str
    value: float
    metric_type: MetricType
    timestamp: float = field(default_factory=time.time)
    labels: Dict[str, str] = field(default_factory=dict)
    unit: str = ""


@dataclass
class Alert:
    """告警信息"""
    id: str
    name: str
    message: str
    severity: str
    timestamp: float
    metric_name: str
    threshold: float
    current_value: float


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 指标存储
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.current_values: Dict[str, float] = {}
        
        # 系统指标
        self.system_metrics_enabled = self.config.get('enable_system_metrics', True)
        self.collection_interval = self.config.get('collection_interval', 10)  # 秒
        
        # 告警配置
        self.alert_thresholds: Dict[str, Dict[str, float]] = {}
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_callbacks: List[Callable] = []
        
        # 运行状态
        self.is_running = False
        self.collection_task: Optional[asyncio.Task] = None
        
        # 性能统计
        self.stats = {
            'total_metrics_collected': 0,
            'alerts_triggered': 0,
            'start_time': None,
            'uptime': 0
        }
        
        # 线程锁
        self._lock = threading.Lock()
        
        # 初始化默认告警阈值
        self._setup_default_thresholds()
    
    def _setup_default_thresholds(self) -> None:
        """设置默认告警阈值"""
        self.alert_thresholds = {
            'cpu_usage': {'warning': 70.0, 'critical': 90.0},
            'memory_usage': {'warning': 80.0, 'critical': 95.0},
            'response_time': {'warning': 1000.0, 'critical': 2000.0},  # ms
            'error_rate': {'warning': 0.05, 'critical': 0.1},  # 5%, 10%
            'queue_size': {'warning': 100, 'critical': 500}
        }
    
    async def initialize(self) -> bool:
        """初始化性能监控器"""
        try:
            self.logger.info("初始化性能监控器...")
            
            # 记录启动时间
            self.stats['start_time'] = time.time()
            
            self.logger.info("性能监控器初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"性能监控器初始化失败: {e}")
            return False
    
    async def start(self) -> None:
        """启动性能监控"""
        if self.is_running:
            return
        
        self.is_running = True
        self.logger.info("启动性能监控")
        
        # 启动系统指标收集
        if self.system_metrics_enabled:
            self.collection_task = asyncio.create_task(self._collect_system_metrics())
    
    async def stop(self) -> None:
        """停止性能监控"""
        self.is_running = False
        
        if self.collection_task:
            self.collection_task.cancel()
            try:
                await self.collection_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("性能监控已停止")
    
    async def _collect_system_metrics(self) -> None:
        """收集系统指标"""
        self.logger.debug("开始收集系统指标")
        
        while self.is_running:
            try:
                # CPU使用率
                cpu_percent = psutil.cpu_percent(interval=1)
                self.record_gauge('cpu_usage', cpu_percent, unit='%')
                
                # 内存使用率
                memory = psutil.virtual_memory()
                self.record_gauge('memory_usage', memory.percent, unit='%')
                self.record_gauge('memory_available', memory.available / 1024 / 1024, unit='MB')
                
                # 磁盘使用率
                disk = psutil.disk_usage('/')
                disk_percent = (disk.used / disk.total) * 100
                self.record_gauge('disk_usage', disk_percent, unit='%')
                
                # 网络IO
                net_io = psutil.net_io_counters()
                self.record_counter('network_bytes_sent', net_io.bytes_sent, unit='bytes')
                self.record_counter('network_bytes_recv', net_io.bytes_recv, unit='bytes')
                
                # 进程信息
                process = psutil.Process()
                self.record_gauge('process_memory', process.memory_info().rss / 1024 / 1024, unit='MB')
                self.record_gauge('process_cpu', process.cpu_percent(), unit='%')
                
                # 更新运行时间
                if self.stats['start_time']:
                    self.stats['uptime'] = time.time() - self.stats['start_time']
                    self.record_gauge('uptime', self.stats['uptime'], unit='seconds')
                
                # 检查告警
                await self._check_alerts()
                
                await asyncio.sleep(self.collection_interval)
                
            except Exception as e:
                self.logger.error(f"收集系统指标失败: {e}")
                await asyncio.sleep(self.collection_interval)
    
    def record_counter(self, name: str, value: float, labels: Optional[Dict[str, str]] = None, unit: str = "") -> None:
        """记录计数器指标"""
        self._record_metric(name, value, MetricType.COUNTER, labels, unit)
    
    def record_gauge(self, name: str, value: float, labels: Optional[Dict[str, str]] = None, unit: str = "") -> None:
        """记录仪表指标"""
        self._record_metric(name, value, MetricType.GAUGE, labels, unit)
    
    def record_histogram(self, name: str, value: float, labels: Optional[Dict[str, str]] = None, unit: str = "") -> None:
        """记录直方图指标"""
        self._record_metric(name, value, MetricType.HISTOGRAM, labels, unit)
    
    def record_timer(self, name: str, duration: float, labels: Optional[Dict[str, str]] = None) -> None:
        """记录计时器指标"""
        self._record_metric(name, duration, MetricType.TIMER, labels, "ms")
    
    def _record_metric(self, name: str, value: float, metric_type: MetricType, 
                      labels: Optional[Dict[str, str]] = None, unit: str = "") -> None:
        """记录指标"""
        with self._lock:
            metric = Metric(
                name=name,
                value=value,
                metric_type=metric_type,
                labels=labels or {},
                unit=unit
            )
            
            self.metrics[name].append(metric)
            self.current_values[name] = value
            self.stats['total_metrics_collected'] += 1
            
            self.logger.debug(f"记录指标: {name}={value}{unit}")
    
    def get_metric(self, name: str) -> Optional[float]:
        """获取当前指标值"""
        return self.current_values.get(name)
    
    def get_metric_history(self, name: str, limit: int = 100) -> List[Metric]:
        """获取指标历史"""
        with self._lock:
            history = list(self.metrics.get(name, []))
            return history[-limit:] if limit > 0 else history
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        with self._lock:
            summary = {}
            
            for name, values in self.metrics.items():
                if not values:
                    continue
                
                recent_values = [m.value for m in list(values)[-10:]]  # 最近10个值
                
                summary[name] = {
                    'current': self.current_values.get(name, 0),
                    'count': len(values),
                    'avg': sum(recent_values) / len(recent_values),
                    'min': min(recent_values),
                    'max': max(recent_values),
                    'unit': values[-1].unit if values else ""
                }
            
            return summary
    
    def set_alert_threshold(self, metric_name: str, warning: float, critical: float) -> None:
        """设置告警阈值"""
        self.alert_thresholds[metric_name] = {
            'warning': warning,
            'critical': critical
        }
        self.logger.info(f"设置告警阈值: {metric_name} warning={warning}, critical={critical}")
    
    def add_alert_callback(self, callback: Callable[[Alert], None]) -> None:
        """添加告警回调"""
        self.alert_callbacks.append(callback)
    
    async def _check_alerts(self) -> None:
        """检查告警条件"""
        for metric_name, current_value in self.current_values.items():
            thresholds = self.alert_thresholds.get(metric_name)
            if not thresholds:
                continue
            
            # 检查严重告警
            if current_value >= thresholds['critical']:
                await self._trigger_alert(metric_name, current_value, 'critical', thresholds['critical'])
            # 检查警告告警
            elif current_value >= thresholds['warning']:
                await self._trigger_alert(metric_name, current_value, 'warning', thresholds['warning'])
            else:
                # 清除告警
                await self._clear_alert(metric_name)
    
    async def _trigger_alert(self, metric_name: str, current_value: float, 
                           severity: str, threshold: float) -> None:
        """触发告警"""
        alert_id = f"{metric_name}_{severity}"
        
        # 避免重复告警
        if alert_id in self.active_alerts:
            return
        
        alert = Alert(
            id=alert_id,
            name=f"{metric_name.replace('_', ' ').title()} {severity.title()}",
            message=f"{metric_name} 超过{severity}阈值: {current_value:.2f} >= {threshold:.2f}",
            severity=severity,
            timestamp=time.time(),
            metric_name=metric_name,
            threshold=threshold,
            current_value=current_value
        )
        
        self.active_alerts[alert_id] = alert
        self.stats['alerts_triggered'] += 1
        
        self.logger.warning(f"触发告警: {alert.message}")
        
        # 调用告警回调
        for callback in self.alert_callbacks:
            try:
                await callback(alert)
            except Exception as e:
                self.logger.error(f"告警回调执行失败: {e}")
    
    async def _clear_alert(self, metric_name: str) -> None:
        """清除告警"""
        alerts_to_clear = [
            alert_id for alert_id in self.active_alerts.keys()
            if alert_id.startswith(metric_name)
        ]
        
        for alert_id in alerts_to_clear:
            del self.active_alerts[alert_id]
            self.logger.info(f"清除告警: {alert_id}")
    
    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警"""
        return list(self.active_alerts.values())
    
    def get_stats(self) -> Dict[str, Any]:
        """获取监控统计"""
        return {
            'stats': self.stats.copy(),
            'metrics_count': len(self.metrics),
            'active_alerts_count': len(self.active_alerts),
            'current_values': self.current_values.copy()
        }
    
    def export_metrics(self, format_type: str = "json") -> str:
        """导出指标数据"""
        if format_type == "json":
            return json.dumps({
                'timestamp': time.time(),
                'metrics': self.get_metrics_summary(),
                'alerts': [
                    {
                        'id': alert.id,
                        'name': alert.name,
                        'message': alert.message,
                        'severity': alert.severity,
                        'timestamp': alert.timestamp
                    }
                    for alert in self.active_alerts.values()
                ],
                'stats': self.stats
            }, indent=2)
        else:
            raise ValueError(f"不支持的导出格式: {format_type}")
    
    def clear_metrics(self, metric_name: Optional[str] = None) -> None:
        """清除指标数据"""
        with self._lock:
            if metric_name:
                if metric_name in self.metrics:
                    self.metrics[metric_name].clear()
                    self.current_values.pop(metric_name, None)
                    self.logger.info(f"清除指标: {metric_name}")
            else:
                self.metrics.clear()
                self.current_values.clear()
                self.logger.info("清除所有指标")


class TimerContext:
    """计时器上下文管理器"""
    
    def __init__(self, monitor: PerformanceMonitor, metric_name: str, 
                 labels: Optional[Dict[str, str]] = None):
        self.monitor = monitor
        self.metric_name = metric_name
        self.labels = labels
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = (time.time() - self.start_time) * 1000  # 转换为毫秒
            self.monitor.record_timer(self.metric_name, duration, self.labels)
