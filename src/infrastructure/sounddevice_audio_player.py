#!/usr/bin/env python3
"""
基于sounddevice的连续音频播放器
解决PyAudio的ALSA兼容性问题
"""

import asyncio
import time
import logging
import threading
import numpy as np
from typing import Optional, Callable, Any
from collections import deque
import sounddevice as sd
from dataclasses import dataclass

from ..interfaces.audio_interface import AudioConfig


@dataclass
class AudioChunk:
    """音频数据块"""
    data: np.ndarray
    sample_rate: int
    channels: int
    timestamp: float = 0.0
    is_final: bool = False


class SoundDeviceAudioPlayer:
    """基于sounddevice的连续音频播放器"""
    
    def __init__(self, config: AudioConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 音频缓冲区
        self.audio_queue = deque()
        self.buffer_lock = threading.Lock()
        self.min_buffer_size = 1  # 最小缓冲区大小（块数）
        self.max_buffer_size = 20  # 最大缓冲区大小
        
        # 播放状态
        self.is_playing = False
        self.is_initialized = False
        self.stream = None
        
        # 音频参数
        self.current_sample_rate = config.sample_rate
        self.current_channels = config.channels
        self.current_device = config.output_device_id
        
        # 统计信息
        self.total_chunks_played = 0
        self.total_audio_duration = 0.0
        self.buffer_underruns = 0
        
        # 回调函数
        self.on_playback_start: Optional[Callable] = None
        self.on_playback_stop: Optional[Callable] = None
        self.on_chunk_played: Optional[Callable] = None
        
        # 当前播放的音频数据
        self.current_audio_data = None
        self.current_position = 0
        
    async def initialize(self) -> bool:
        """初始化音频播放器"""
        try:
            if self.is_initialized:
                return True
            
            # 检查可用的音频设备
            devices = sd.query_devices()
            self.logger.info(f"可用音频设备: {len(devices)} 个")
            
            # 如果指定了设备ID，检查是否有效
            if self.current_device is not None:
                if self.current_device < len(devices):
                    device_info = devices[self.current_device]
                    self.logger.info(f"使用音频输出设备: {device_info['name']}")
                else:
                    self.logger.warning(f"指定的设备ID {self.current_device} 无效，使用默认设备")
                    self.current_device = None
            
            # 设置默认参数
            sd.default.samplerate = self.current_sample_rate
            sd.default.channels = self.current_channels
            if self.current_device is not None:
                sd.default.device = self.current_device
            
            self.is_initialized = True
            self.logger.info("SoundDevice音频播放器初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"SoundDevice音频播放器初始化失败: {e}")
            return False
    
    def add_audio_chunk(self, chunk: AudioChunk) -> bool:
        """添加音频块到播放队列"""
        try:
            with self.buffer_lock:
                # 检查缓冲区大小
                if len(self.audio_queue) >= self.max_buffer_size:
                    # 移除最旧的块
                    self.audio_queue.popleft()
                    self.logger.warning("音频缓冲区满，丢弃最旧的音频块")
                
                # 添加新块
                self.audio_queue.append(chunk)
                
                # 如果还没开始播放且缓冲区有数据，开始播放
                if not self.is_playing and len(self.audio_queue) >= self.min_buffer_size:
                    self._start_playback()
                
                self.logger.debug(f"🔊 添加音频块，队列大小: {len(self.audio_queue)}, 播放中: {self.is_playing}")
                
                return True
                
        except Exception as e:
            self.logger.error(f"添加音频块失败: {e}")
            return False
    
    def force_start_playback(self):
        """强制启动播放（即使缓冲区不满）"""
        try:
            with self.buffer_lock:
                if not self.is_playing and len(self.audio_queue) > 0:
                    self._start_playback()
                    self.logger.info("🔊 强制启动音频播放")
                    return True
                return False
        except Exception as e:
            self.logger.error(f"强制启动播放失败: {e}")
            return False
    
    def _start_playback(self):
        """开始播放"""
        try:
            if self.is_playing:
                return
            
            self.is_playing = True
            
            # 启动音频流
            self._start_audio_stream()
            
            # 调用回调
            if self.on_playback_start:
                self.on_playback_start()
            
            self.logger.info("开始SoundDevice音频播放")
            
        except Exception as e:
            self.logger.error(f"启动播放失败: {e}")
            self.is_playing = False
    
    def _start_audio_stream(self):
        """启动音频流"""
        try:
            # 使用sounddevice的OutputStream
            self.stream = sd.OutputStream(
                samplerate=self.current_sample_rate,
                channels=self.current_channels,
                dtype=np.float32,
                callback=self._audio_callback,
                blocksize=self.config.chunk_size,
                device=self.current_device
            )
            
            self.stream.start()
            self.logger.info(f"🔊 SoundDevice音频流已启动: {self.current_sample_rate}Hz, {self.current_channels}声道")
            print(f"🔊 SoundDevice音频流已启动: {self.current_sample_rate}Hz, {self.current_channels}声道")
            
        except Exception as e:
            self.logger.error(f"启动音频流失败: {e}")
            raise
    
    def _audio_callback(self, outdata, frames, time, status):
        """音频回调函数"""
        try:
            if status:
                self.logger.warning(f"音频回调状态: {status}")
            
            # 初始化输出数据
            outdata.fill(0)
            
            # 如果没有当前音频数据，尝试获取下一个块
            if self.current_audio_data is None or self.current_position >= len(self.current_audio_data):
                self._load_next_chunk()
            
            # 如果有音频数据，填充输出缓冲区
            if self.current_audio_data is not None:
                remaining_frames = len(self.current_audio_data) - self.current_position
                frames_to_copy = min(frames, remaining_frames)
                
                if frames_to_copy > 0:
                    # 复制音频数据
                    if self.current_channels == 1:
                        outdata[:frames_to_copy, 0] = self.current_audio_data[self.current_position:self.current_position + frames_to_copy]
                    else:
                        # 多声道处理
                        for ch in range(self.current_channels):
                            outdata[:frames_to_copy, ch] = self.current_audio_data[self.current_position:self.current_position + frames_to_copy]
                    
                    self.current_position += frames_to_copy
                    
                    # 如果当前块播放完了，标记为已播放
                    if self.current_position >= len(self.current_audio_data):
                        self.total_chunks_played += 1
                        self.total_audio_duration += len(self.current_audio_data) / self.current_sample_rate
                        
                        self.logger.info(f"🔊 成功播放音频块: {len(self.current_audio_data)} 样本, 时长: {len(self.current_audio_data)/self.current_sample_rate:.3f}s")
                        
                        # 调用回调
                        if self.on_chunk_played and hasattr(self, '_current_chunk'):
                            try:
                                self.on_chunk_played(self._current_chunk)
                            except Exception as e:
                                self.logger.error(f"块播放回调错误: {e}")
                        
                        # 清除当前数据
                        self.current_audio_data = None
                        self.current_position = 0
            else:
                # 没有音频数据，记录下溢
                self.buffer_underruns += 1
                if self.buffer_underruns % 100 == 1:  # 每100次记录一次
                    self.logger.debug(f"音频缓冲区下溢: {self.buffer_underruns}")
            
        except Exception as e:
            self.logger.error(f"音频回调错误: {e}")
            outdata.fill(0)
    
    def _load_next_chunk(self):
        """加载下一个音频块"""
        try:
            with self.buffer_lock:
                if self.audio_queue:
                    chunk = self.audio_queue.popleft()
                    self._current_chunk = chunk
                    
                    # 处理音频数据
                    audio_data = self._process_audio_chunk(chunk)
                    
                    if audio_data is not None and len(audio_data) > 0:
                        self.current_audio_data = audio_data
                        self.current_position = 0
                        self.logger.debug(f"🔊 加载音频块: {len(audio_data)} 样本")
                    else:
                        self.current_audio_data = None
                        self.current_position = 0
                        
        except Exception as e:
            self.logger.error(f"加载音频块失败: {e}")
            self.current_audio_data = None
            self.current_position = 0
    
    def _process_audio_chunk(self, chunk: AudioChunk) -> Optional[np.ndarray]:
        """处理音频块"""
        try:
            # 确保数据格式正确
            audio_data = chunk.data.astype(np.float32)
            
            # 检查数据是否为空
            if len(audio_data) == 0:
                self.logger.warning("音频数据为空")
                return None
            
            # 确保数据在正确范围内（-1.0到1.0）
            max_val = np.max(np.abs(audio_data))
            if max_val > 1.0:
                # 归一化到-1.0到1.0范围
                audio_data = audio_data / max_val
                self.logger.debug(f"音频数据归一化: max_val={max_val}")
            elif max_val == 0:
                self.logger.warning("音频数据全为零")
                return None
            
            # 处理采样率不匹配
            if chunk.sample_rate != self.current_sample_rate:
                audio_data = self._resample_audio(audio_data, chunk.sample_rate, self.current_sample_rate)
            
            # 处理声道不匹配
            if chunk.channels != self.current_channels:
                audio_data = self._convert_channels(audio_data, chunk.channels, self.current_channels)
            
            # 最终确保是float32格式
            audio_data = audio_data.astype(np.float32)
            
            self.logger.debug(f"处理音频块: 长度={len(audio_data)}, 范围=[{np.min(audio_data):.3f}, {np.max(audio_data):.3f}]")
            
            return audio_data
            
        except Exception as e:
            self.logger.error(f"处理音频块失败: {e}")
            return None
    
    def _resample_audio(self, audio_data: np.ndarray, from_rate: int, to_rate: int) -> np.ndarray:
        """重采样音频（简单线性插值）"""
        try:
            if from_rate == to_rate:
                return audio_data
            
            # 计算重采样比例
            ratio = to_rate / from_rate
            new_length = int(len(audio_data) * ratio)
            
            # 简单线性插值重采样
            old_indices = np.linspace(0, len(audio_data) - 1, new_length)
            new_audio = np.interp(old_indices, np.arange(len(audio_data)), audio_data)
            
            return new_audio.astype(np.float32)
            
        except Exception as e:
            self.logger.error(f"音频重采样失败: {e}")
            return audio_data
    
    def _convert_channels(self, audio_data: np.ndarray, from_channels: int, to_channels: int) -> np.ndarray:
        """转换声道数"""
        try:
            if from_channels == to_channels:
                return audio_data
            
            if from_channels == 1 and to_channels == 2:
                # 单声道转立体声
                return np.repeat(audio_data, 2)
            elif from_channels == 2 and to_channels == 1:
                # 立体声转单声道
                return audio_data[::2]  # 简单取左声道
            else:
                # 其他情况，返回原数据
                return audio_data
                
        except Exception as e:
            self.logger.error(f"声道转换失败: {e}")
            return audio_data
    
    def stop_playback(self):
        """停止播放"""
        try:
            if not self.is_playing:
                return
            
            self.is_playing = False
            
            # 停止音频流
            if self.stream:
                self.stream.stop()
                self.stream.close()
                self.stream = None
            
            # 调用回调
            if self.on_playback_stop:
                self.on_playback_stop()
            
            self.logger.info("SoundDevice音频播放已停止")
            
        except Exception as e:
            self.logger.error(f"停止播放失败: {e}")
    
    def pause(self):
        """暂停播放"""
        try:
            if self.stream and self.stream.active:
                self.stream.stop()
                self.logger.info("音频播放已暂停")
        except Exception as e:
            self.logger.error(f"暂停播放失败: {e}")
    
    def resume(self):
        """恢复播放"""
        try:
            if self.stream and not self.stream.active:
                self.stream.start()
                self.logger.info("音频播放已恢复")
        except Exception as e:
            self.logger.error(f"恢复播放失败: {e}")
    
    def clear_buffer(self):
        """清空缓冲区"""
        try:
            with self.buffer_lock:
                self.audio_queue.clear()
                self.current_audio_data = None
                self.current_position = 0
                self.logger.info("音频缓冲区已清空")
        except Exception as e:
            self.logger.error(f"清空缓冲区失败: {e}")
    
    def get_buffer_status(self) -> dict:
        """获取缓冲区状态"""
        try:
            with self.buffer_lock:
                return {
                    "queue_size": len(self.audio_queue),
                    "min_buffer_size": self.min_buffer_size,
                    "max_buffer_size": self.max_buffer_size,
                    "is_playing": self.is_playing,
                    "total_chunks_played": self.total_chunks_played,
                    "total_audio_duration": self.total_audio_duration,
                    "buffer_underruns": self.buffer_underruns,
                    "current_audio_length": len(self.current_audio_data) if self.current_audio_data is not None else 0,
                    "current_position": self.current_position
                }
        except Exception as e:
            self.logger.error(f"获取缓冲区状态失败: {e}")
            return {}
    
    def cleanup(self):
        """清理资源"""
        try:
            self.stop_playback()
            self.logger.info("SoundDevice音频播放器资源已清理")
            
        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")
