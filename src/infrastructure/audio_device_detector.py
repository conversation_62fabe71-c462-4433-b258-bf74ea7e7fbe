"""
音频设备检测器
检测和测试音频设备功能
"""

import logging
import time
from typing import Dict, List, Optional, Tuple
import numpy as np
import sounddevice as sd
import soundfile as sf
from dataclasses import dataclass

from src.interfaces.audio_interface import AudioDevice, AudioConfig


@dataclass
class DeviceTestResult:
    """设备测试结果"""
    device_id: int
    device_name: str
    is_input: bool
    success: bool
    latency_ms: Optional[float] = None
    error_message: Optional[str] = None
    sample_rate_supported: List[int] = None
    channels_supported: List[int] = None


class AudioDeviceDetector:
    """音频设备检测器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 常用采样率
        self.test_sample_rates = [8000, 16000, 22050, 44100, 48000]
        
        # 测试音频参数
        self.test_duration = 1.0  # 秒
        self.test_frequency = 440  # Hz (A4音符)
    
    def detect_all_devices(self) -> List[AudioDevice]:
        """检测所有音频设备"""
        try:
            devices = []
            device_list = sd.query_devices()
            
            self.logger.info(f"检测到 {len(device_list)} 个音频设备")
            
            for i, device_info in enumerate(device_list):
                self.logger.debug(f"设备 {i}: {device_info}")
                
                # 跳过无效设备
                if (device_info['max_input_channels'] == 0 and 
                    device_info['max_output_channels'] == 0):
                    continue
                
                # 输入设备
                if device_info['max_input_channels'] > 0:
                    devices.append(AudioDevice(
                        id=i,
                        name=device_info['name'],
                        channels=device_info['max_input_channels'],
                        sample_rate=int(device_info['default_samplerate']),
                        is_input=True,
                        is_default=(i == sd.default.device[0])
                    ))
                
                # 输出设备
                if device_info['max_output_channels'] > 0:
                    devices.append(AudioDevice(
                        id=i,
                        name=device_info['name'],
                        channels=device_info['max_output_channels'],
                        sample_rate=int(device_info['default_samplerate']),
                        is_input=False,
                        is_default=(i == sd.default.device[1])
                    ))
            
            return devices
            
        except Exception as e:
            self.logger.error(f"检测音频设备失败: {e}")
            return []
    
    def get_default_devices(self) -> Tuple[Optional[AudioDevice], Optional[AudioDevice]]:
        """获取默认输入和输出设备"""
        devices = self.detect_all_devices()
        
        default_input = None
        default_output = None
        
        for device in devices:
            if device.is_default:
                if device.is_input:
                    default_input = device
                else:
                    default_output = device
        
        return default_input, default_output
    
    def test_device(self, device: AudioDevice, config: AudioConfig) -> DeviceTestResult:
        """测试单个设备"""
        self.logger.info(f"测试设备: {device.name} ({'输入' if device.is_input else '输出'})")
        
        if device.is_input:
            return self._test_input_device(device, config)
        else:
            return self._test_output_device(device, config)
    
    def _test_input_device(self, device: AudioDevice, config: AudioConfig) -> DeviceTestResult:
        """测试输入设备"""
        try:
            start_time = time.time()
            
            # 测试录音
            duration = 0.5  # 测试录音0.5秒
            audio_data = sd.rec(
                int(duration * config.sample_rate),
                samplerate=config.sample_rate,
                channels=min(config.channels, device.channels),
                device=device.id,
                dtype=np.float32
            )
            sd.wait()  # 等待录音完成
            
            latency = (time.time() - start_time) * 1000  # 转换为毫秒
            
            # 检查录音数据
            if audio_data is None or len(audio_data) == 0:
                return DeviceTestResult(
                    device_id=device.id,
                    device_name=device.name,
                    is_input=True,
                    success=False,
                    error_message="录音数据为空"
                )
            
            # 检查音频信号
            rms = np.sqrt(np.mean(audio_data ** 2))
            if rms < 1e-6:  # 非常小的信号可能表示设备问题
                self.logger.warning(f"设备 {device.name} 录音信号很弱: RMS={rms}")
            
            # 测试支持的采样率
            supported_rates = self._test_sample_rates(device, True)
            
            # 测试支持的声道数
            supported_channels = self._test_channels(device, config.sample_rate, True)
            
            return DeviceTestResult(
                device_id=device.id,
                device_name=device.name,
                is_input=True,
                success=True,
                latency_ms=latency,
                sample_rate_supported=supported_rates,
                channels_supported=supported_channels
            )
            
        except Exception as e:
            self.logger.error(f"测试输入设备失败 {device.name}: {e}")
            return DeviceTestResult(
                device_id=device.id,
                device_name=device.name,
                is_input=True,
                success=False,
                error_message=str(e)
            )
    
    def _test_output_device(self, device: AudioDevice, config: AudioConfig) -> DeviceTestResult:
        """测试输出设备"""
        try:
            start_time = time.time()
            
            # 生成测试音频（正弦波）
            duration = 0.5  # 播放0.5秒
            samples = int(duration * config.sample_rate)
            t = np.linspace(0, duration, samples, False)
            
            # 生成440Hz正弦波
            audio_data = 0.1 * np.sin(2 * np.pi * self.test_frequency * t)
            
            # 调整声道数
            channels = min(config.channels, device.channels)
            if channels > 1:
                audio_data = np.column_stack([audio_data] * channels)
            
            # 播放音频
            sd.play(
                audio_data,
                samplerate=config.sample_rate,
                device=device.id
            )
            sd.wait()  # 等待播放完成
            
            latency = (time.time() - start_time) * 1000  # 转换为毫秒
            
            # 测试支持的采样率
            supported_rates = self._test_sample_rates(device, False)
            
            # 测试支持的声道数
            supported_channels = self._test_channels(device, config.sample_rate, False)
            
            return DeviceTestResult(
                device_id=device.id,
                device_name=device.name,
                is_input=False,
                success=True,
                latency_ms=latency,
                sample_rate_supported=supported_rates,
                channels_supported=supported_channels
            )
            
        except Exception as e:
            self.logger.error(f"测试输出设备失败 {device.name}: {e}")
            return DeviceTestResult(
                device_id=device.id,
                device_name=device.name,
                is_input=False,
                success=False,
                error_message=str(e)
            )
    
    def _test_sample_rates(self, device: AudioDevice, is_input: bool) -> List[int]:
        """测试设备支持的采样率"""
        supported_rates = []
        
        for rate in self.test_sample_rates:
            try:
                if is_input:
                    # 测试录音
                    sd.check_input_settings(
                        device=device.id,
                        channels=1,
                        samplerate=rate
                    )
                else:
                    # 测试播放
                    sd.check_output_settings(
                        device=device.id,
                        channels=1,
                        samplerate=rate
                    )
                
                supported_rates.append(rate)
                
            except Exception:
                # 不支持此采样率
                pass
        
        return supported_rates
    
    def _test_channels(self, device: AudioDevice, sample_rate: int, is_input: bool) -> List[int]:
        """测试设备支持的声道数"""
        supported_channels = []
        max_channels = device.channels
        
        for channels in range(1, min(max_channels + 1, 9)):  # 测试1-8声道
            try:
                if is_input:
                    sd.check_input_settings(
                        device=device.id,
                        channels=channels,
                        samplerate=sample_rate
                    )
                else:
                    sd.check_output_settings(
                        device=device.id,
                        channels=channels,
                        samplerate=sample_rate
                    )
                
                supported_channels.append(channels)
                
            except Exception:
                # 不支持此声道数
                break  # 通常声道数是连续的，如果某个不支持，后面的也不会支持
        
        return supported_channels
    
    def test_all_devices(self, config: AudioConfig) -> List[DeviceTestResult]:
        """测试所有设备"""
        devices = self.detect_all_devices()
        results = []
        
        self.logger.info(f"开始测试 {len(devices)} 个音频设备")
        
        for device in devices:
            result = self.test_device(device, config)
            results.append(result)
            
            if result.success:
                self.logger.info(f"✅ {device.name} 测试通过 (延迟: {result.latency_ms:.1f}ms)")
            else:
                self.logger.warning(f"❌ {device.name} 测试失败: {result.error_message}")
        
        return results
    
    def get_recommended_devices(self, config: AudioConfig) -> Tuple[Optional[AudioDevice], Optional[AudioDevice]]:
        """获取推荐的输入和输出设备"""
        test_results = self.test_all_devices(config)
        
        # 筛选成功的设备
        successful_input = [r for r in test_results if r.success and r.is_input]
        successful_output = [r for r in test_results if r.success and not r.is_input]
        
        # 按延迟排序，选择最低延迟的设备
        if successful_input:
            best_input_result = min(successful_input, key=lambda x: x.latency_ms or float('inf'))
            devices = self.detect_all_devices()
            best_input = next((d for d in devices if d.id == best_input_result.device_id and d.is_input), None)
        else:
            best_input = None
        
        if successful_output:
            best_output_result = min(successful_output, key=lambda x: x.latency_ms or float('inf'))
            devices = self.detect_all_devices()
            best_output = next((d for d in devices if d.id == best_output_result.device_id and not d.is_input), None)
        else:
            best_output = None
        
        return best_input, best_output
    
    def generate_device_report(self, config: AudioConfig) -> str:
        """生成设备检测报告"""
        test_results = self.test_all_devices(config)
        
        report = ["音频设备检测报告", "=" * 50, ""]
        
        # 默认设备
        default_input, default_output = self.get_default_devices()
        if default_input:
            report.append(f"默认输入设备: {default_input.name}")
        if default_output:
            report.append(f"默认输出设备: {default_output.name}")
        report.append("")
        
        # 推荐设备
        rec_input, rec_output = self.get_recommended_devices(config)
        if rec_input:
            report.append(f"推荐输入设备: {rec_input.name}")
        if rec_output:
            report.append(f"推荐输出设备: {rec_output.name}")
        report.append("")
        
        # 详细测试结果
        report.append("详细测试结果:")
        report.append("-" * 30)
        
        for result in test_results:
            status = "✅ 通过" if result.success else "❌ 失败"
            device_type = "输入" if result.is_input else "输出"
            
            report.append(f"{status} {result.device_name} ({device_type})")
            
            if result.success:
                if result.latency_ms:
                    report.append(f"    延迟: {result.latency_ms:.1f}ms")
                if result.sample_rate_supported:
                    report.append(f"    支持采样率: {result.sample_rate_supported}")
                if result.channels_supported:
                    report.append(f"    支持声道: {result.channels_supported}")
            else:
                if result.error_message:
                    report.append(f"    错误: {result.error_message}")
            
            report.append("")
        
        return "\n".join(report)
