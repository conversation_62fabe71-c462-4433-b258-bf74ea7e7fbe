"""
音频管理器
跨平台音频输入输出管理，支持实时音频流处理
"""

import asyncio
import logging
import threading
import time
from typing import Dict, List, Optional, Callable, Any
from collections import deque
import numpy as np
import sounddevice as sd
import soundfile as sf
from dataclasses import dataclass

from src.interfaces.audio_interface import (
    AudioInterface, AudioDevice, AudioConfig, AudioCallbackInterface
)
from src.services.base_service import BaseService


@dataclass
class AudioBuffer:
    """音频缓冲区"""
    data: np.ndarray
    timestamp: float
    sample_rate: int
    channels: int


class AudioManager(BaseService, AudioInterface):
    """音频管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("AudioManager", config)
        
        # 音频配置
        self.audio_config: Optional[AudioConfig] = None
        
        # 音频流
        self.input_stream: Optional[sd.InputStream] = None
        self.output_stream: Optional[sd.OutputStream] = None
        
        # 音频缓冲区
        self.input_buffer: deque = deque(maxlen=1000)  # 最多保存1000个音频块
        self.output_buffer: deque = deque(maxlen=100)   # 输出缓冲区
        
        # 回调函数
        self.audio_callbacks: List[Callable[[np.ndarray], None]] = []
        self.callback_interfaces: List[AudioCallbackInterface] = []
        
        # 录音状态
        self.is_recording = False
        self.is_playing = False
        
        # 线程锁
        self._lock = threading.Lock()
        
        # 音频统计
        self.stats = {
            'input_frames_processed': 0,
            'output_frames_processed': 0,
            'buffer_overruns': 0,
            'buffer_underruns': 0,
            'callback_errors': 0
        }
        
        # 设备信息缓存
        self._device_cache: Optional[List[AudioDevice]] = None
        self._cache_time = 0
        self._cache_ttl = 30  # 缓存30秒
    
    async def _initialize_impl(self) -> bool:
        """初始化音频管理器"""
        try:
            # 设置默认音频配置
            if not self.audio_config:
                self.audio_config = AudioConfig(
                    sample_rate=self.config.get('sample_rate', 16000),
                    channels=self.config.get('channels', 1),
                    chunk_size=self.config.get('chunk_size', 1024),
                    buffer_size=self.config.get('buffer_size', 32000),
                    input_device_id=self.config.get('input_device'),
                    output_device_id=self.config.get('output_device')
                )
            
            # 检查音频设备
            devices = self.get_available_devices()
            if not devices:
                self.logger.error("未找到可用的音频设备")
                return False
            
            # 验证设备配置
            if not self._validate_device_config():
                return False
            
            self.logger.info(f"音频管理器初始化成功，配置: {self.audio_config}")
            return True
            
        except Exception as e:
            self.logger.error(f"音频管理器初始化失败: {e}")
            return False
    
    async def _start_impl(self) -> bool:
        """启动音频管理器"""
        try:
            self.logger.info("启动音频管理器...")
            return True
        except Exception as e:
            self.logger.error(f"启动音频管理器失败: {e}")
            return False
    
    async def _stop_impl(self) -> bool:
        """停止音频管理器"""
        try:
            # 停止录音和播放
            await self.stop_recording()
            await self._stop_playback()
            
            self.logger.info("音频管理器已停止")
            return True
        except Exception as e:
            self.logger.error(f"停止音频管理器失败: {e}")
            return False
    
    async def _health_check_impl(self) -> bool:
        """健康检查"""
        try:
            # 检查设备可用性
            devices = self.get_available_devices()
            if not devices:
                return False
            
            # 检查流状态
            if self.input_stream and not self.input_stream.active:
                self.logger.warning("输入流不活跃")
                return False
            
            return True
        except Exception as e:
            self.logger.error(f"音频管理器健康检查失败: {e}")
            return False
    
    async def initialize(self, config: AudioConfig) -> bool:
        """初始化音频系统"""
        self.audio_config = config
        return await super().initialize()
    
    def get_available_devices(self) -> List[AudioDevice]:
        """获取可用音频设备"""
        try:
            # 检查缓存
            current_time = time.time()
            if (self._device_cache and 
                current_time - self._cache_time < self._cache_ttl):
                return self._device_cache
            
            devices = []
            device_list = sd.query_devices()
            
            for i, device_info in enumerate(device_list):
                # 跳过无效设备
                if device_info['max_input_channels'] == 0 and device_info['max_output_channels'] == 0:
                    continue
                
                # 输入设备
                if device_info['max_input_channels'] > 0:
                    devices.append(AudioDevice(
                        id=i,
                        name=device_info['name'],
                        channels=device_info['max_input_channels'],
                        sample_rate=int(device_info['default_samplerate']),
                        is_input=True,
                        is_default=(i == sd.default.device[0])
                    ))
                
                # 输出设备
                if device_info['max_output_channels'] > 0:
                    devices.append(AudioDevice(
                        id=i,
                        name=device_info['name'],
                        channels=device_info['max_output_channels'],
                        sample_rate=int(device_info['default_samplerate']),
                        is_input=False,
                        is_default=(i == sd.default.device[1])
                    ))
            
            # 更新缓存
            self._device_cache = devices
            self._cache_time = current_time
            
            self.logger.debug(f"发现 {len(devices)} 个音频设备")
            return devices
            
        except Exception as e:
            self.logger.error(f"获取音频设备失败: {e}")
            return []
    
    def _validate_device_config(self) -> bool:
        """验证设备配置"""
        try:
            devices = self.get_available_devices()
            
            # 验证输入设备
            if self.audio_config.input_device_id is not None:
                input_devices = [d for d in devices if d.is_input and d.id == self.audio_config.input_device_id]
                if not input_devices:
                    self.logger.error(f"指定的输入设备不存在: {self.audio_config.input_device_id}")
                    return False
            
            # 验证输出设备
            if self.audio_config.output_device_id is not None:
                output_devices = [d for d in devices if not d.is_input and d.id == self.audio_config.output_device_id]
                if not output_devices:
                    self.logger.error(f"指定的输出设备不存在: {self.audio_config.output_device_id}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证设备配置失败: {e}")
            return False
    
    async def start_recording(self) -> bool:
        """开始录音"""
        if self.is_recording:
            self.logger.warning("录音已在进行中")
            return True
        
        try:
            self.logger.info("开始录音...")
            
            # 创建输入流
            self.input_stream = sd.InputStream(
                device=self.audio_config.input_device_id,
                channels=self.audio_config.channels,
                samplerate=self.audio_config.sample_rate,
                blocksize=self.audio_config.chunk_size,
                callback=self._audio_input_callback,
                dtype=np.float32
            )
            
            # 启动流
            self.input_stream.start()
            self.is_recording = True
            
            self.logger.info("录音启动成功")
            return True
            
        except Exception as e:
            self.logger.error(f"启动录音失败: {e}")
            self.is_recording = False
            return False
    
    async def stop_recording(self) -> bool:
        """停止录音"""
        if not self.is_recording:
            return True
        
        try:
            self.logger.info("停止录音...")
            
            if self.input_stream:
                self.input_stream.stop()
                self.input_stream.close()
                self.input_stream = None
            
            self.is_recording = False
            self.logger.info("录音已停止")
            return True
            
        except Exception as e:
            self.logger.error(f"停止录音失败: {e}")
            return False
    
    def _audio_input_callback(self, indata: np.ndarray, frames: int, 
                            time_info: Any, status: sd.CallbackFlags) -> None:
        """音频输入回调"""
        try:
            if status:
                self.logger.warning(f"音频输入状态: {status}")
                if status.input_overflow:
                    self.stats['buffer_overruns'] += 1
            
            # 转换为单声道（如果需要）
            if indata.shape[1] > 1 and self.audio_config.channels == 1:
                audio_data = np.mean(indata, axis=1)
            else:
                audio_data = indata.flatten()
            
            # 添加到缓冲区
            buffer = AudioBuffer(
                data=audio_data.copy(),
                timestamp=time.time(),
                sample_rate=self.audio_config.sample_rate,
                channels=self.audio_config.channels
            )
            
            with self._lock:
                self.input_buffer.append(buffer)
                self.stats['input_frames_processed'] += frames
            
            # 调用回调函数
            self._trigger_audio_callbacks(audio_data)
            
        except Exception as e:
            self.logger.error(f"音频输入回调错误: {e}")
            self.stats['callback_errors'] += 1
    
    def _trigger_audio_callbacks(self, audio_data: np.ndarray) -> None:
        """触发音频回调"""
        # 同步回调
        for callback in self.audio_callbacks:
            try:
                callback(audio_data)
            except Exception as e:
                self.logger.error(f"音频回调执行失败: {e}")
                self.stats['callback_errors'] += 1
        
        # 异步回调接口
        for callback_interface in self.callback_interfaces:
            try:
                asyncio.create_task(callback_interface.on_audio_data(audio_data))
            except Exception as e:
                self.logger.error(f"异步音频回调执行失败: {e}")
                self.stats['callback_errors'] += 1
    
    async def play_audio(self, audio_data: np.ndarray) -> bool:
        """播放音频"""
        try:
            self.logger.debug(f"播放音频，长度: {len(audio_data)}")
            
            # 确保音频数据格式正确
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
            
            # 如果是单声道，转换为适合输出的格式
            if len(audio_data.shape) == 1:
                audio_data = audio_data.reshape(-1, 1)
            
            # 播放音频并等待完成
            duration = len(audio_data) / self.audio_config.sample_rate
            self.logger.info(f"🔊 开始播放音频，时长: {duration:.2f}秒")

            sd.play(audio_data, samplerate=self.audio_config.sample_rate,
                   device=self.audio_config.output_device_id)

            # 等待播放完成
            sd.wait()

            self.logger.info("🔊 音频播放完成")
            self.stats['output_frames_processed'] += len(audio_data)
            return True
            
        except Exception as e:
            self.logger.error(f"播放音频失败: {e}")
            return False
    
    async def _stop_playback(self) -> None:
        """停止播放"""
        try:
            sd.stop()
            self.is_playing = False
        except Exception as e:
            self.logger.error(f"停止播放失败: {e}")
    
    def register_audio_callback(self, callback: Callable[[np.ndarray], None]) -> None:
        """注册音频数据回调"""
        self.audio_callbacks.append(callback)
        self.logger.debug("注册音频回调函数")
    
    def register_callback_interface(self, callback_interface: AudioCallbackInterface) -> None:
        """注册音频回调接口"""
        self.callback_interfaces.append(callback_interface)
        self.logger.debug("注册音频回调接口")
    
    def get_recent_audio(self, duration_ms: int) -> np.ndarray:
        """获取最近的音频数据"""
        try:
            with self._lock:
                if not self.input_buffer:
                    return np.array([])
                
                # 计算需要的帧数
                frames_needed = int(duration_ms * self.audio_config.sample_rate / 1000)
                
                # 从缓冲区收集音频数据
                audio_chunks = []
                total_frames = 0
                
                # 从最新的数据开始收集
                for buffer in reversed(self.input_buffer):
                    audio_chunks.insert(0, buffer.data)
                    total_frames += len(buffer.data)
                    
                    if total_frames >= frames_needed:
                        break
                
                if not audio_chunks:
                    return np.array([])
                
                # 合并音频数据
                combined_audio = np.concatenate(audio_chunks)
                
                # 截取所需长度
                if len(combined_audio) > frames_needed:
                    combined_audio = combined_audio[-frames_needed:]
                
                return combined_audio
                
        except Exception as e:
            self.logger.error(f"获取最近音频数据失败: {e}")
            return np.array([])
    
    def get_audio_stats(self) -> Dict[str, Any]:
        """获取音频统计信息"""
        with self._lock:
            return {
                **self.stats,
                'buffer_size': len(self.input_buffer),
                'is_recording': self.is_recording,
                'is_playing': self.is_playing,
                'config': {
                    'sample_rate': self.audio_config.sample_rate,
                    'channels': self.audio_config.channels,
                    'chunk_size': self.audio_config.chunk_size
                }
            }
    
    def clear_buffer(self) -> None:
        """清空音频缓冲区"""
        with self._lock:
            self.input_buffer.clear()
            self.output_buffer.clear()
            self.logger.debug("音频缓冲区已清空")
    
    async def cleanup(self) -> None:
        """清理资源"""
        await self.stop_recording()
        await self._stop_playback()
        self.clear_buffer()
        self.audio_callbacks.clear()
        self.callback_interfaces.clear()
        self.logger.info("音频管理器资源清理完成")
