"""
日志配置系统
支持结构化日志、多输出目标、动态配置
"""

import logging
import logging.handlers
import json
import sys
import os
from typing import Dict, Any, Optional
from pathlib import Path
import structlog
from datetime import datetime


class JSONFormatter(logging.Formatter):
    """JSON格式化器"""
    
    def format(self, record):
        """格式化日志记录为JSON"""
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        # 添加进程和线程信息
        log_entry['process_id'] = record.process
        log_entry['thread_id'] = record.thread
        
        return json.dumps(log_entry, ensure_ascii=False)


class StructuredFormatter(logging.Formatter):
    """结构化格式化器"""
    
    def __init__(self, format_string=None):
        super().__init__()
        self.format_string = format_string or (
            "%(asctime)s | %(levelname)-8s | %(name)-20s | %(funcName)-15s:%(lineno)-4d | %(message)s"
        )
    
    def format(self, record):
        """格式化日志记录"""
        # 基础格式化
        formatted = super().format(record)
        
        # 添加额外字段
        if hasattr(record, 'extra_fields'):
            extra_str = " | ".join([f"{k}={v}" for k, v in record.extra_fields.items()])
            formatted += f" | {extra_str}"
        
        return formatted


class LoggingConfig:
    """日志配置管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.loggers: Dict[str, logging.Logger] = {}
        
        # 默认配置
        self.default_level = config.get('level', 'INFO')
        self.default_format = config.get('format', 'structured')
        self.enable_console = config.get('enable_console', True)
        self.file_config = config.get('file', {})
        self.logger_configs = config.get('loggers', {})
    
    def setup_logging(self) -> None:
        """设置日志系统"""
        # 清除现有处理器
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 设置根日志级别
        root_logger.setLevel(self._get_log_level(self.default_level))
        
        # 设置控制台处理器
        if self.enable_console:
            self._setup_console_handler(root_logger)
        
        # 设置文件处理器
        if self.file_config:
            self._setup_file_handler(root_logger)
        
        # 设置特定日志器
        self._setup_specific_loggers()
        
        # 设置structlog
        self._setup_structlog()
        
        logging.info("日志系统配置完成")
    
    def _setup_console_handler(self, logger: logging.Logger) -> None:
        """设置控制台处理器"""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self._get_log_level(self.default_level))
        
        # 设置格式化器
        if self.default_format == 'json':
            formatter = JSONFormatter()
        else:
            formatter = StructuredFormatter()
        
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    def _setup_file_handler(self, logger: logging.Logger) -> None:
        """设置文件处理器"""
        file_path = self.file_config.get('path', 'logs/app.log')
        max_bytes = self._parse_size(self.file_config.get('max_size', '10MB'))
        backup_count = self.file_config.get('backup_count', 5)
        
        # 确保日志目录存在
        log_dir = Path(file_path).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建轮转文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            file_path,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        
        file_level = self.file_config.get('level', self.default_level)
        file_handler.setLevel(self._get_log_level(file_level))
        
        # 设置格式化器
        file_format = self.file_config.get('format', self.default_format)
        if file_format == 'json':
            formatter = JSONFormatter()
        else:
            formatter = StructuredFormatter()
        
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    def _setup_specific_loggers(self) -> None:
        """设置特定日志器"""
        for logger_name, logger_config in self.logger_configs.items():
            logger = logging.getLogger(logger_name)
            level = logger_config.get('level', self.default_level)
            logger.setLevel(self._get_log_level(level))
            
            # 防止日志传播到根日志器（避免重复）
            propagate = logger_config.get('propagate', True)
            logger.propagate = propagate
            
            self.loggers[logger_name] = logger
    
    def _setup_structlog(self) -> None:
        """设置structlog"""
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
    
    def _get_log_level(self, level_str: str) -> int:
        """获取日志级别"""
        level_map = {
            'DEBUG': logging.DEBUG,
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR,
            'CRITICAL': logging.CRITICAL
        }
        return level_map.get(level_str.upper(), logging.INFO)
    
    def _parse_size(self, size_str: str) -> int:
        """解析大小字符串"""
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取日志器"""
        return logging.getLogger(name)
    
    def add_context_filter(self, logger_name: str, context: Dict[str, Any]) -> None:
        """添加上下文过滤器"""
        logger = logging.getLogger(logger_name)
        
        class ContextFilter(logging.Filter):
            def filter(self, record):
                if not hasattr(record, 'extra_fields'):
                    record.extra_fields = {}
                record.extra_fields.update(context)
                return True
        
        logger.addFilter(ContextFilter())
    
    def set_log_level(self, logger_name: str, level: str) -> None:
        """动态设置日志级别"""
        logger = logging.getLogger(logger_name)
        logger.setLevel(self._get_log_level(level))
        logging.info(f"设置日志器 {logger_name} 级别为 {level}")


class ContextualLogger:
    """上下文日志器"""
    
    def __init__(self, logger: logging.Logger, context: Optional[Dict[str, Any]] = None):
        self.logger = logger
        self.context = context or {}
    
    def _log_with_context(self, level: int, message: str, *args, **kwargs) -> None:
        """带上下文的日志记录"""
        extra = kwargs.get('extra', {})
        if 'extra_fields' not in extra:
            extra['extra_fields'] = {}
        extra['extra_fields'].update(self.context)
        kwargs['extra'] = extra
        
        self.logger.log(level, message, *args, **kwargs)
    
    def debug(self, message: str, *args, **kwargs) -> None:
        self._log_with_context(logging.DEBUG, message, *args, **kwargs)
    
    def info(self, message: str, *args, **kwargs) -> None:
        self._log_with_context(logging.INFO, message, *args, **kwargs)
    
    def warning(self, message: str, *args, **kwargs) -> None:
        self._log_with_context(logging.WARNING, message, *args, **kwargs)
    
    def error(self, message: str, *args, **kwargs) -> None:
        self._log_with_context(logging.ERROR, message, *args, **kwargs)
    
    def critical(self, message: str, *args, **kwargs) -> None:
        self._log_with_context(logging.CRITICAL, message, *args, **kwargs)
    
    def with_context(self, **context) -> 'ContextualLogger':
        """添加上下文"""
        new_context = self.context.copy()
        new_context.update(context)
        return ContextualLogger(self.logger, new_context)


def setup_logging_from_config(config: Dict[str, Any]) -> LoggingConfig:
    """从配置设置日志系统"""
    logging_config = LoggingConfig(config)
    logging_config.setup_logging()
    return logging_config


def get_contextual_logger(name: str, **context) -> ContextualLogger:
    """获取上下文日志器"""
    logger = logging.getLogger(name)
    return ContextualLogger(logger, context)
