"""
配置管理器
支持多环境配置、热重载、配置验证
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path
import asyncio
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler


class ConfigFileHandler(FileSystemEventHandler):
    """配置文件变更处理器"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
    
    def on_modified(self, event):
        """文件修改事件"""
        if not event.is_directory and event.src_path.endswith('.yaml'):
            self.logger.info(f"配置文件变更: {event.src_path}")
            asyncio.create_task(self.config_manager._reload_config())


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 配置数据
        self.config: Dict[str, Any] = {}
        self.config_path: Optional[str] = None
        self.environment: str = "development"
        
        # 热重载
        self.enable_hot_reload = True
        self.observer: Optional[Observer] = None
        self.file_handler: Optional[ConfigFileHandler] = None
        
        # 配置验证
        self.config_schema: Optional[Dict[str, Any]] = None
        
        # 环境变量前缀
        self.env_prefix = "AIBI_"
    
    async def load_config(self, config_path: str, environment: str = "development") -> bool:
        """加载配置文件"""
        try:
            self.config_path = config_path
            self.environment = environment
            
            self.logger.info(f"加载配置文件: {config_path}, 环境: {environment}")
            
            # 加载主配置文件
            main_config = await self._load_yaml_file(config_path)
            if not main_config:
                return False
            
            self.config = main_config.copy()
            
            # 加载环境特定配置
            env_config_path = self._get_env_config_path(config_path, environment)
            if os.path.exists(env_config_path):
                env_config = await self._load_yaml_file(env_config_path)
                if env_config:
                    self._merge_config(self.config, env_config)
                    self.logger.info(f"加载环境配置: {env_config_path}")
            
            # 应用环境变量覆盖
            self._apply_env_overrides()
            
            # 验证配置
            if not self._validate_config():
                return False
            
            # 启动热重载
            if self.enable_hot_reload:
                await self._start_hot_reload()
            
            self.logger.info("配置加载完成")
            return True
            
        except Exception as e:
            self.logger.error(f"配置加载失败: {e}")
            return False
    
    async def _load_yaml_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """加载YAML文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 处理环境变量替换
                content = self._substitute_env_vars(content)
                
                return yaml.safe_load(content)
                
        except Exception as e:
            self.logger.error(f"加载YAML文件失败 {file_path}: {e}")
            return None
    
    def _substitute_env_vars(self, content: str) -> str:
        """替换环境变量"""
        import re
        
        # 匹配 ${VAR_NAME} 或 ${VAR_NAME:default_value} 格式
        pattern = r'\$\{([^}:]+)(?::([^}]*))?\}'
        
        def replace_var(match):
            var_name = match.group(1)
            default_value = match.group(2) if match.group(2) is not None else ""
            return os.getenv(var_name, default_value)
        
        return re.sub(pattern, replace_var, content)
    
    def _get_env_config_path(self, main_path: str, environment: str) -> str:
        """获取环境配置文件路径"""
        path = Path(main_path)
        return str(path.parent / f"{path.stem}.{environment}{path.suffix}")
    
    def _merge_config(self, base_config: Dict[str, Any], override_config: Dict[str, Any]) -> None:
        """合并配置"""
        for key, value in override_config.items():
            if key in base_config and isinstance(base_config[key], dict) and isinstance(value, dict):
                self._merge_config(base_config[key], value)
            else:
                base_config[key] = value
    
    def _apply_env_overrides(self) -> None:
        """应用环境变量覆盖"""
        for key, value in os.environ.items():
            if key.startswith(self.env_prefix):
                config_key = key[len(self.env_prefix):].lower().replace('_', '.')
                self._set_nested_value(self.config, config_key, value)
    
    def _set_nested_value(self, config: Dict[str, Any], key_path: str, value: str) -> None:
        """设置嵌套配置值"""
        keys = key_path.split('.')
        current = config
        
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        # 尝试转换值类型
        final_key = keys[-1]
        try:
            # 尝试转换为数字
            if value.isdigit():
                current[final_key] = int(value)
            elif value.replace('.', '').isdigit():
                current[final_key] = float(value)
            elif value.lower() in ['true', 'false']:
                current[final_key] = value.lower() == 'true'
            else:
                current[final_key] = value
        except:
            current[final_key] = value
    
    def _validate_config(self) -> bool:
        """验证配置"""
        try:
            # 基本验证
            required_sections = ['system', 'audio', 'logging']
            for section in required_sections:
                if section not in self.config:
                    self.logger.error(f"缺少必需的配置节: {section}")
                    return False
            
            # 验证音频配置
            audio_config = self.config.get('audio', {})
            if audio_config.get('sample_rate', 0) <= 0:
                self.logger.error("音频采样率配置无效")
                return False
            
            # 验证系统配置
            system_config = self.config.get('system', {})
            if not system_config.get('name'):
                self.logger.error("系统名称配置缺失")
                return False
            
            self.logger.debug("配置验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    async def _start_hot_reload(self) -> None:
        """启动热重载"""
        try:
            if not self.config_path:
                return
            
            config_dir = os.path.dirname(os.path.abspath(self.config_path))
            
            self.file_handler = ConfigFileHandler(self)
            self.observer = Observer()
            self.observer.schedule(self.file_handler, config_dir, recursive=False)
            self.observer.start()
            
            self.logger.info("配置热重载已启动")
            
        except Exception as e:
            self.logger.error(f"启动配置热重载失败: {e}")
    
    async def _reload_config(self) -> None:
        """重新加载配置"""
        try:
            self.logger.info("重新加载配置...")
            
            if await self.load_config(self.config_path, self.environment):
                self.logger.info("配置重新加载成功")
                # TODO: 通知其他组件配置已更新
            else:
                self.logger.error("配置重新加载失败")
                
        except Exception as e:
            self.logger.error(f"重新加载配置异常: {e}")
    
    def get_config(self, key_path: Optional[str] = None) -> Any:
        """获取配置值"""
        if not key_path:
            return self.config
        
        keys = key_path.split('.')
        current = self.config
        
        try:
            for key in keys:
                current = current[key]
            return current
        except (KeyError, TypeError):
            return None
    
    def set_config(self, key_path: str, value: Any) -> None:
        """设置配置值"""
        self._set_nested_value(self.config, key_path, value)
    
    def get_environment(self) -> str:
        """获取当前环境"""
        return self.environment
    
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.environment == "development"
    
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.environment == "production"
    
    async def cleanup(self) -> None:
        """清理资源"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            self.logger.info("配置热重载已停止")
