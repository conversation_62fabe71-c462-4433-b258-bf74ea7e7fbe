#!/usr/bin/env python3
"""
测试完整对话流程
模拟唤醒词 -> 语音识别 -> LLM响应 -> TTS合成的完整流程
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.event_bus import EventBus


async def test_conversation_flow():
    """测试完整对话流程"""
    print("🧪 开始测试完整对话流程...")
    
    # 连接到运行中的事件总线（如果可能）
    # 这里我们创建一个新的事件总线来模拟事件
    event_bus = EventBus()
    await event_bus.initialize()
    
    # 记录事件
    events_received = []
    
    async def log_event(event_name, data):
        events_received.append((event_name, data, time.time()))
        print(f"📨 收到事件: {event_name}")
    
    # 订阅事件
    await event_bus.subscribe("wake_word_detected", lambda data: log_event("wake_word_detected", data))
    await event_bus.subscribe("speech_recognized", lambda data: log_event("speech_recognized", data))
    await event_bus.subscribe("user_input", lambda data: log_event("user_input", data))
    await event_bus.subscribe("llm_response_complete", lambda data: log_event("llm_response_complete", data))
    await event_bus.subscribe("text_to_synthesize", lambda data: log_event("text_to_synthesize", data))
    
    print("\n🎭 模拟完整对话流程:")
    
    # 1. 模拟唤醒词检测
    print("1️⃣ 模拟唤醒词检测...")
    await event_bus.emit("wake_word_detected", {
        "confidence": 0.95,
        "timestamp": time.time(),
        "source": "test"
    })
    await asyncio.sleep(1)
    
    # 2. 模拟语音识别
    print("2️⃣ 模拟语音识别...")
    await event_bus.emit("speech_recognized", {
        "text": "你好，请介绍一下自己",
        "confidence": 0.90,
        "timestamp": time.time(),
        "source": "test"
    })
    await asyncio.sleep(1)
    
    # 3. 模拟用户输入处理
    print("3️⃣ 模拟用户输入处理...")
    await event_bus.emit("user_input", {
        "text": "你好，请介绍一下自己",
        "session_id": "test_session",
        "timestamp": time.time()
    })
    await asyncio.sleep(1)
    
    # 4. 模拟LLM响应
    print("4️⃣ 模拟LLM响应...")
    await event_bus.emit("llm_response_complete", {
        "session_id": "test_session",
        "response": "你好！我是艾比，艾凯控股集团旗下的智能管家。我可以为您提供各种生活服务支持。",
        "timestamp": time.time()
    })
    await asyncio.sleep(1)
    
    # 5. 模拟TTS合成
    print("5️⃣ 模拟TTS合成...")
    await event_bus.emit("text_to_synthesize", {
        "text": "你好！我是艾比，艾凯控股集团旗下的智能管家。我可以为您提供各种生活服务支持。",
        "session_id": "test_session",
        "streaming": True,
        "timestamp": time.time()
    })
    await asyncio.sleep(2)
    
    # 分析结果
    print(f"\n📊 测试结果分析:")
    print(f"  总事件数: {len(events_received)}")
    
    event_types = {}
    for event_name, _, _ in events_received:
        event_types[event_name] = event_types.get(event_name, 0) + 1
    
    for event_type, count in event_types.items():
        print(f"  {event_type}: {count}次")
    
    # 验证完整流程
    expected_events = [
        "wake_word_detected",
        "speech_recognized", 
        "user_input",
        "llm_response_complete",
        "text_to_synthesize"
    ]
    
    success = all(event in event_types for event in expected_events)
    
    print(f"\n🎯 完整对话流程测试: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        print("🎉 所有关键事件都已触发，对话流程完整！")
        print("✨ 事件流程:")
        for i, (event_name, data, timestamp) in enumerate(events_received, 1):
            time_str = time.strftime('%H:%M:%S', time.localtime(timestamp))
            print(f"   {i}. [{time_str}] {event_name}")
    else:
        missing_events = [e for e in expected_events if e not in event_types]
        print(f"❌ 缺失事件: {missing_events}")
    
    await event_bus.shutdown()
    return success


if __name__ == "__main__":
    success = asyncio.run(test_conversation_flow())
    sys.exit(0 if success else 1)
