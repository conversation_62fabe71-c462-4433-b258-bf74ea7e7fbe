[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "hey-aibi"
version = "1.0.0"
description = "智能语音助手系统 - 基于三层架构的端到端语音交互系统"
authors = [
    {name = "Hey-AIBI Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.8"
keywords = ["voice", "assistant", "ai", "speech", "nlp"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Multimedia :: Sound/Audio :: Speech",
]

dependencies = [
    "asyncio-mqtt>=0.13.0",
    "pydantic>=2.0.0",
    "pyyaml>=6.0",
    "python-dotenv>=1.0.0",
    "funasr>=1.0.0",
    "soundfile>=0.12.1",
    "librosa>=0.10.0",
    "pyaudio>=0.2.11",
    "sounddevice>=0.4.6",
    "onnxruntime>=1.16.0",
    "onnx>=1.15.0",
    "numpy>=1.24.0",
    "torch>=2.0.0",
    "torchaudio>=2.0.0",
    "jieba>=0.42.1",
    "httpx>=0.25.0",
    "aiohttp>=3.9.0",
    "requests>=2.31.0",
    "structlog>=23.1.0",
    "prometheus-client>=0.17.0",
    "cerberus>=1.3.4",
    "python-decouple>=3.8",
    "watchdog>=3.0.0",
    "dataclasses-json>=0.6.0",
    "typing-extensions>=4.7.0",
    "pathlib2>=2.3.7",
    "psutil>=5.9.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "memory-profiler>=0.61.0",
    "jupyter>=1.0.0",
    "ipython>=8.0.0",
    "sphinx>=7.0.0",
    "autopep8>=2.0.0",
]

test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
    "pytest-benchmark>=4.0.0",
]

docs = [
    "sphinx>=7.0.0",
    "sphinx-rtd-theme>=1.3.0",
    "myst-parser>=2.0.0",
]

[project.urls]
Homepage = "https://github.com/hey-aibi/hey-aibi"
Documentation = "https://hey-aibi.readthedocs.io"
Repository = "https://github.com/hey-aibi/hey-aibi"
Issues = "https://github.com/hey-aibi/hey-aibi/issues"

[project.scripts]
hey-aibi = "src.main:main"

# Black代码格式化配置
[tool.black]
line-length = 100
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # 排除目录
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | models
)/
'''

# isort导入排序配置
[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
known_first_party = ["src"]
known_third_party = ["pytest", "numpy", "torch", "funasr"]

# MyPy类型检查配置
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

# 忽略第三方库的类型检查
[[tool.mypy.overrides]]
module = [
    "funasr.*",
    "soundfile.*",
    "librosa.*",
    "pyaudio.*",
    "sounddevice.*",
    "onnxruntime.*",
    "torch.*",
    "torchaudio.*",
    "psutil.*",
    "watchdog.*",
]
ignore_missing_imports = true

# Flake8配置
[tool.flake8]
max-line-length = 100
extend-ignore = [
    "E203",  # whitespace before ':'
    "E501",  # line too long (handled by black)
    "W503",  # line break before binary operator
]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    "venv",
    "models",
]
per-file-ignores = [
    "__init__.py:F401",  # imported but unused
    "tests/*:F401,F811",  # test files
]

# Coverage配置
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__init__.py",
    "*/setup.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "if TYPE_CHECKING:",
]

# Bandit安全检查配置
[tool.bandit]
exclude_dirs = ["tests", "build", "dist"]
skips = ["B101", "B601"]  # 跳过assert和shell注入检查（测试文件中常见）

# setuptools配置
[tool.setuptools.packages.find]
where = ["."]
include = ["src*"]
exclude = ["tests*"]
