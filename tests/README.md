# 测试目录结构说明

## 📁 目录结构

```
tests/
├── __init__.py                    # 测试包初始化
├── conftest.py                    # pytest配置文件和共享fixtures
├── README.md                      # 本说明文档
├── test_main.py                   # 集成测试（需要真实设备）
└── unit/                          # 单元测试
    ├── __init__.py
    ├── test_state_machine.py      # 状态机单元测试
    ├── test_conversation_service.py # 对话服务单元测试
    ├── test_event_bus.py          # 事件总线单元测试
    ├── test_audio_manager.py      # 音频管理器单元测试
    ├── test_config_manager.py     # 配置管理器单元测试
    ├── test_llm_service.py        # LLM服务单元测试
    ├── test_tts_service.py        # TTS服务单元测试
    ├── test_wake_word_service.py  # 唤醒词服务单元测试
    └── test_audio_device_detector.py # 音频设备检测单元测试
```

## 📋 关键文件说明

### conftest.py
`conftest.py` 是pytest的配置文件，提供：
- **共享fixtures**: 事件总线、模拟服务等测试工具
- **pytest标记定义**: 自动为测试添加标记
- **测试配置**: 统一的测试环境配置
- **作用范围**: 对tests目录下所有测试文件生效

## 🎯 测试分类说明

### 单元测试 (Unit Tests)
- **目录**: `tests/unit/`
- **用途**: 测试单个模块、类或函数的功能
- **特点**:
  - 快速执行
  - 使用mock对象隔离依赖
  - 覆盖边界条件和异常情况
- **运行**: `pytest tests/unit/`

### 集成测试 (Integration Test)
- **文件**: `tests/test_main.py`
- **用途**: 需要真实设备的完整系统测试
- **特点**:
  - 需要真实音频设备
  - 需要用户交互
  - 用于演示和验证完整工作流
  - 实时监听唤醒词和语音识别
- **运行**: `python tests/test_main.py`

## 🏷️ 测试标记 (Markers)

使用pytest标记来分类和过滤测试：

- `@pytest.mark.unit` - 单元测试
- `@pytest.mark.manual` - 手动测试
- `@pytest.mark.slow` - 慢速测试
- `@pytest.mark.requires_audio` - 需要音频设备
- `@pytest.mark.requires_network` - 需要网络连接

## 🚀 运行测试

### 运行所有测试
```bash
pytest
```

### 按类型运行测试
```bash
# 只运行单元测试
pytest -m unit

# 只运行集成测试
pytest -m integration

# 只运行快速测试（排除慢速测试）
pytest -m "not slow"

# 运行需要音频设备的测试
pytest -m requires_audio
```

### 按目录运行测试
```bash
# 运行单元测试
pytest tests/unit/

# 运行集成测试
pytest tests/integration/

# 运行性能测试
pytest tests/performance/
```

### 运行特定测试文件
```bash
pytest tests/unit/test_state_machine.py
```

### 运行特定测试方法
```bash
pytest tests/unit/test_state_machine.py::TestStateMachine::test_initial_state
```

## 📊 测试覆盖率

查看测试覆盖率报告：
```bash
# 生成覆盖率报告
pytest --cov=src --cov-report=html

# 查看HTML报告
open htmlcov/index.html
```

## 🛠️ 编写测试指南

### 1. 测试文件命名
- 单元测试: `test_<module_name>.py`
- 集成测试: `test_<feature>_integration.py`
- 性能测试: `test_<feature>_performance.py`

### 2. 测试类命名
```python
class TestModuleName:
    """模块测试类"""
    pass
```

### 3. 测试方法命名
```python
async def test_specific_functionality(self):
    """测试特定功能"""
    pass
```

### 4. 使用fixtures
```python
async def test_with_fixtures(self, event_bus, state_machine):
    """使用共享fixtures的测试"""
    pass
```

### 5. 添加适当的标记
```python
@pytest.mark.unit
@pytest.mark.slow
async def test_complex_operation(self):
    """复杂操作测试"""
    pass
```

## 🔧 调试测试

### 详细输出
```bash
pytest -v -s
```

### 只运行失败的测试
```bash
pytest --lf
```

### 进入调试模式
```bash
pytest --pdb
```

### 显示最慢的测试
```bash
pytest --durations=10
```
