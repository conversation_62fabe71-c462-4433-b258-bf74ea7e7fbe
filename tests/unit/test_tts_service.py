"""
TTS服务单元测试
"""

import pytest
import asyncio
import numpy as np
import time
from unittest.mock import AsyncMock, MagicMock, patch, Mock
from pathlib import Path

from src.services.tts import CosyVoiceTTSService, TTSConfig, TTSRequest, TTSStreamChunk
from src.interfaces.speech_interface import TTSResult


class TestCosyVoiceTTSService:
    """CosyVoice TTS服务测试"""
    
    @pytest.fixture
    def mock_event_bus(self):
        """模拟事件总线"""
        event_bus = AsyncMock()
        event_bus.subscribe = AsyncMock()
        event_bus.publish = AsyncMock()
        return event_bus

    @pytest.fixture
    def config(self):
        """测试配置"""
        return {
            "model_name": "CosyVoice-300M",
            "device": "cpu",
            "num_threads": 4,
            "streaming": True,
            "voice": "中文女",
            "speed": 1.0,
            "volume": 1.0
        }

    @pytest.fixture
    def service(self, config, mock_event_bus):
        """创建TTS服务实例"""
        return CosyVoiceTTSService(config, mock_event_bus)
    
    @pytest.fixture
    def tts_config(self):
        """TTS配置"""
        return TTSConfig(
            model_path="models/tts/CosyVoice-300M",
            device="cpu",
            num_threads=4,
            streaming=True,
            voice="中文女",
            speed=1.0,
            volume=1.0
        )
    
    def test_tts_config_creation(self, tts_config):
        """测试TTS配置创建"""
        assert tts_config.model_path == "models/tts/CosyVoice-300M"
        assert tts_config.device == "cpu"
        assert tts_config.num_threads == 4
        assert tts_config.streaming is True
        assert tts_config.voice == "中文女"
        assert tts_config.speed == 1.0
        assert tts_config.volume == 1.0
        assert tts_config.sample_rate == 22050
    
    def test_tts_request_creation(self):
        """测试TTS请求创建"""
        request = TTSRequest(
            text="你好，世界",
            voice="中文女",
            speed=1.2,
            volume=0.8,
            streaming=True
        )
        
        assert request.text == "你好，世界"
        assert request.voice == "中文女"
        assert request.speed == 1.2
        assert request.volume == 0.8
        assert request.streaming is True
    
    def test_tts_stream_chunk_creation(self):
        """测试TTS流式块创建"""
        audio_data = np.random.random(1024).astype(np.float32)
        chunk = TTSStreamChunk(
            audio_data=audio_data,
            sample_rate=22050,
            is_final=False,
            chunk_id="chunk_1"
        )
        
        assert chunk.audio_data.shape == (1024,)
        assert chunk.sample_rate == 22050
        assert chunk.is_final is False
        assert chunk.chunk_id == "chunk_1"
    
    @pytest.mark.asyncio
    async def test_initialization_success(self, service):
        """测试成功初始化"""
        with patch('os.path.exists', return_value=True), \
             patch.object(service, '_load_model', return_value=True):

            result = await service._initialize_impl()

            assert result is True
            assert service.tts_config is not None
            assert service.tts_config.model_path == "models/tts/CosyVoice-300M"
    
    @pytest.mark.asyncio
    async def test_initialization_model_not_found(self, service):
        """测试模型路径不存在的初始化"""
        with patch('os.path.exists', return_value=False):
            result = await service._initialize_impl()

            assert result is False
            assert service.tts_config is None
    
    def test_provide_installation_guidance(self, service, tts_config):
        """测试安装指导"""
        service.tts_config = tts_config

        # 这个方法应该不会抛出异常
        service._provide_installation_guidance()

        # 验证方法可以正常调用
        assert True
    
    @pytest.mark.asyncio
    async def test_synthesize_not_loaded(self, service):
        """测试模型未加载时的合成"""
        service.is_loaded = False
        service.model = None

        result = await service.synthesize("测试文本")

        assert result is None
    
    @pytest.mark.asyncio
    async def test_synthesize_not_loaded(self, service):
        """测试未加载模型时的合成"""
        service.is_loaded = False
        service.model = None

        result = await service.synthesize("测试文本")

        assert result is None
    
    @pytest.mark.asyncio
    async def test_synthesize_streaming_empty_text(self, service, tts_config):
        """测试空文本的流式合成"""
        service.tts_config = tts_config
        service.is_loaded = True
        service.model = Mock()
        service.available_speakers = ["default"]

        chunks = []
        async for chunk in service.synthesize_streaming(""):
            chunks.append(chunk)

        # 空文本应该没有输出
        assert len(chunks) == 0
    
    @pytest.mark.asyncio
    async def test_synthesize_streaming_not_loaded(self, service):
        """测试未加载模型时的流式合成"""
        service.is_loaded = False
        service.model = None

        chunks = []
        async for chunk in service.synthesize_streaming("测试文本"):
            chunks.append(chunk)

        assert len(chunks) == 0
    
    def test_set_voice_valid(self, service, tts_config):
        """测试设置有效语音"""
        service.tts_config = tts_config
        service.available_speakers = ["中文女", "中文男", "英文女"]

        service.set_voice("中文男")

        assert service.tts_config.voice == "中文男"
    
    def test_set_voice_invalid(self, service, tts_config):
        """测试设置无效语音"""
        service.tts_config = tts_config
        service.available_speakers = ["中文女", "中文男"]
        original_voice = service.tts_config.voice

        service.set_voice("不存在的语音")

        assert service.tts_config.voice == original_voice  # 应该保持不变
    
    def test_set_speed(self, service, tts_config):
        """测试设置语速"""
        service.tts_config = tts_config

        # 测试正常范围
        service.set_speed(1.5)
        assert service.tts_config.speed == 1.5

        # 测试边界值
        service.set_speed(0.3)  # 低于最小值
        assert service.tts_config.speed == 0.5

        service.set_speed(3.0)  # 高于最大值
        assert service.tts_config.speed == 2.0
    
    def test_set_volume(self, service, tts_config):
        """测试设置音量"""
        service.tts_config = tts_config

        # 测试正常范围
        service.set_volume(0.8)
        assert service.tts_config.volume == 0.8

        # 测试边界值
        service.set_volume(-0.5)  # 低于最小值
        assert service.tts_config.volume == 0.0

        service.set_volume(3.0)  # 高于最大值
        assert service.tts_config.volume == 2.0
    
    def test_get_available_voices_loaded(self, service):
        """测试获取可用语音（已加载）"""
        service.is_loaded = True
        service.available_speakers = ["中文女", "中文男", "英文女"]
        
        voices = service.get_available_voices()
        
        assert voices == ["中文女", "中文男", "英文女"]
    
    def test_get_available_voices_not_loaded(self, service):
        """测试获取可用语音（未加载）"""
        service.is_loaded = False
        
        voices = service.get_available_voices()
        
        assert voices == []
    
    def test_get_model_info(self, service, tts_config):
        """测试获取模型信息"""
        service.tts_config = tts_config
        service.is_loaded = True
        service.available_speakers = ["中文女", "中文男"]
        
        info = service.get_model_info()
        
        assert info["model_path"] == "models/tts/CosyVoice-300M"
        assert info["is_loaded"] is True
        assert info["device"] == "cpu"
        assert info["available_speakers"] == ["中文女", "中文男"]
        assert info["current_voice"] == "中文女"
        assert info["sample_rate"] == 22050
    
    def test_get_statistics_no_synthesis(self, service):
        """测试获取统计信息（无合成记录）"""
        stats = service.get_statistics()
        
        assert stats["synthesis_count"] == 0
        assert stats["avg_synthesis_time"] == 0.0
        assert stats["avg_audio_duration"] == 0.0
        assert stats["total_synthesis_time"] == 0.0
        assert stats["total_audio_duration"] == 0.0
        assert stats["last_synthesis_time"] is None
    
    def test_get_statistics_with_synthesis(self, service):
        """测试获取统计信息（有合成记录）"""
        # 模拟合成统计
        service.synthesis_count = 3
        service.total_synthesis_time = 1.5
        service.total_audio_duration = 6.0
        service.last_synthesis_time = time.time()
        
        stats = service.get_statistics()
        
        assert stats["synthesis_count"] == 3
        assert stats["avg_synthesis_time"] == 0.5
        assert stats["avg_audio_duration"] == 2.0
        assert stats["total_synthesis_time"] == 1.5
        assert stats["total_audio_duration"] == 6.0
        assert stats["last_synthesis_time"] is not None
    
    @pytest.mark.asyncio
    async def test_on_text_to_synthesize_streaming(self, service, tts_config):
        """测试处理文本合成事件（流式）"""
        service.tts_config = tts_config

        # 创建模拟模型，返回可迭代的结果
        mock_model = Mock()
        mock_result = {'tts_speech': Mock()}
        mock_result['tts_speech'].numpy.return_value.flatten.return_value = np.zeros(1000)
        mock_model.inference_sft.return_value = [mock_result]

        service.model = mock_model
        service.is_loaded = True
        service.available_speakers = ["default"]

        event_data = {
            "text": "测试文本",
            "streaming": True
        }

        await service._on_text_to_synthesize(event_data)

        # 验证事件发布
        assert service.event_bus.publish.called
    
    @pytest.mark.asyncio
    async def test_on_text_to_synthesize_non_streaming(self, service, tts_config):
        """测试处理文本合成事件（非流式）"""
        service.tts_config = tts_config

        # 创建模拟模型，返回可迭代的结果
        mock_model = Mock()
        mock_result = {'tts_speech': Mock()}
        mock_result['tts_speech'].numpy.return_value.flatten.return_value = np.zeros(1000)
        mock_model.inference_sft.return_value = [mock_result]

        service.model = mock_model
        service.is_loaded = True
        service.available_speakers = ["speaker1"]  # 使用非默认说话人
        service.use_zero_shot = False
        service.use_instruct = False

        event_data = {
            "text": "测试文本",
            "streaming": False
        }

        await service._on_text_to_synthesize(event_data)

        # 验证事件发布
        assert service.event_bus.publish.called
    
    @pytest.mark.asyncio
    async def test_on_text_to_synthesize_empty_text(self, service, tts_config):
        """测试处理空文本事件"""
        service.tts_config = tts_config  # 设置配置以避免AttributeError
        event_data = {"text": ""}

        await service._on_text_to_synthesize(event_data)

        # 空文本不应该触发合成
        assert not service.event_bus.publish.called
    
    @pytest.mark.asyncio
    async def test_on_tts_interrupt(self, service):
        """测试TTS中断处理"""
        service.is_synthesizing = True
        
        await service._on_tts_interrupt({})
        
        assert service.is_synthesizing is False
    
    @pytest.mark.asyncio
    async def test_test_synthesis(self, service, tts_config):
        """测试合成功能测试"""
        service.tts_config = tts_config

        # 创建模拟模型，返回可迭代的结果
        mock_model = Mock()
        mock_result = {'tts_speech': Mock()}
        mock_result['tts_speech'].numpy.return_value.flatten.return_value = np.zeros(1000)
        mock_model.inference_sft.return_value = [mock_result]

        service.model = mock_model
        service.is_loaded = True
        service.available_speakers = ["speaker1"]  # 使用非默认说话人
        service.use_zero_shot = False
        service.use_instruct = False

        result = await service.test_synthesis("测试文本")

        assert result is not None
        assert isinstance(result, TTSResult)
    
    @pytest.mark.asyncio
    async def test_test_synthesis_not_loaded(self, service):
        """测试未加载模型时的合成测试"""
        service.is_loaded = False
        
        with pytest.raises(RuntimeError, match="TTS模型未加载"):
            await service.test_synthesis("测试文本")
    
    @pytest.mark.asyncio
    async def test_cleanup(self, service):
        """测试资源清理"""
        service.is_synthesizing = True
        service.model = "test_model"
        service.is_loaded = True
        
        await service.cleanup()
        
        assert service.is_synthesizing is False
        assert service.model is None
        assert service.is_loaded is False


class TestTTSConfig:
    """TTS配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = TTSConfig(model_path="/test/path")
        
        assert config.model_path == "/test/path"
        assert config.device == "cpu"
        assert config.num_threads == 4
        assert config.streaming is True
        assert config.voice == "default"
        assert config.speed == 1.0
        assert config.volume == 1.0
        assert config.sample_rate == 22050


class TestTTSRequest:
    """TTS请求测试"""
    
    def test_minimal_request(self):
        """测试最小请求"""
        request = TTSRequest(text="测试")
        
        assert request.text == "测试"
        assert request.voice is None
        assert request.speed is None
        assert request.volume is None
        assert request.streaming is False


class TestTTSStreamChunk:
    """TTS流式块测试"""
    
    def test_chunk_creation(self):
        """测试块创建"""
        audio_data = np.array([0.1, 0.2, 0.3], dtype=np.float32)
        chunk = TTSStreamChunk(
            audio_data=audio_data,
            sample_rate=16000,
            is_final=True,
            chunk_id="final"
        )
        
        assert np.array_equal(chunk.audio_data, audio_data)
        assert chunk.sample_rate == 16000
        assert chunk.is_final is True
        assert chunk.chunk_id == "final"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
