"""
LLM服务单元测试
"""

import pytest
import asyncio
import json
import time
from unittest.mock import AsyncMock, MagicMock, patch
import aiohttp

from src.services.llm import DifyLLMService, DifyConfig, ConversationContext
from src.interfaces.llm_interface import LLMRequest, LLMMessage, LLMProvider


class TestDifyLLMService:
    """Dify LLM服务测试"""
    
    @pytest.fixture
    def service(self):
        """创建服务实例"""
        return DifyLLMService()
    
    @pytest.fixture
    def config(self):
        """测试配置"""
        return {
            "api_url": "http://11.20.60.13/v1",
            "api_key": "app-NIfC6KMjycOJyp2p85LGzR0u",
            "timeout": 30,
            "retry_attempts": 3
        }
    
    @pytest.fixture
    def sample_request(self):
        """示例请求"""
        return LLMRequest(
            messages=[
                LLMMessage(role="user", content="你好", timestamp=time.time())
            ],
            max_tokens=100,
            temperature=0.7
        )
    
    @pytest.mark.asyncio
    async def test_initialization_success(self, service, config):
        """测试成功初始化"""
        with patch.object(service, 'health_check', return_value=True):
            result = await service.initialize(config)
            
            assert result is True
            assert service.is_initialized is True
            assert service.config.api_url == config["api_url"]
            assert service.config.api_key == config["api_key"]
            assert service.session is not None
    
    @pytest.mark.asyncio
    async def test_initialization_missing_api_key(self, service):
        """测试缺少API密钥的初始化"""
        config = {"api_url": "http://test.com"}
        
        result = await service.initialize(config)
        
        assert result is False
        assert service.is_initialized is False
    
    @pytest.mark.asyncio
    async def test_chat_success(self, service, config, sample_request):
        """测试成功的对话请求"""
        # 初始化服务
        await service.initialize(config)
        
        # Mock响应数据
        mock_response_data = {
            "answer": "你好！我是AI助手。",
            "metadata": {
                "usage": {
                    "prompt_tokens": 10,
                    "completion_tokens": 15,
                    "total_tokens": 25
                }
            },
            "model": "gpt-3.5-turbo"
        }
        
        with patch.object(service, '_send_request_with_retry', return_value=mock_response_data):
            response = await service.chat(sample_request)
            
            assert response is not None
            assert response.content == "你好！我是AI助手。"
            assert response.usage["total_tokens"] == 25
            assert response.model == "gpt-3.5-turbo"
            assert response.processing_time > 0
    
    @pytest.mark.asyncio
    async def test_chat_not_initialized(self, service, sample_request):
        """测试未初始化时的对话请求"""
        with pytest.raises(RuntimeError, match="服务未初始化"):
            await service.chat(sample_request)
    
    @pytest.mark.asyncio
    async def test_chat_stream_success(self, service, config, sample_request):
        """测试流式对话"""
        await service.initialize(config)
        
        # Mock流式响应 - 直接返回LLMStreamChunk对象
        from src.interfaces.llm_interface import LLMStreamChunk
        
        async def mock_stream_generator():
            yield LLMStreamChunk(content="你好", is_final=False, chunk_id="1")
            yield LLMStreamChunk(content="！", is_final=False, chunk_id="2")
            yield LLMStreamChunk(content="", is_final=True, chunk_id="3")
        
        with patch.object(service, '_send_stream_request', return_value=mock_stream_generator()):
            chunks = []
            async for chunk in service.chat_stream(sample_request):
                chunks.append(chunk)
            
            assert len(chunks) >= 2  # 至少有内容块和结束块
            assert any(chunk.content == "你好" for chunk in chunks)
            assert any(chunk.is_final for chunk in chunks)
    
    @pytest.mark.asyncio
    async def test_health_check_success(self, service, config):
        """测试健康检查成功"""
        await service.initialize(config)
        
        with patch.object(service, 'chat', return_value=MagicMock()):
            result = await service.health_check()
            assert result is True
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self, service, config):
        """测试健康检查失败"""
        await service.initialize(config)
        
        with patch.object(service, 'chat', side_effect=Exception("网络错误")):
            result = await service.health_check()
            assert result is False
    
    def test_get_provider(self, service):
        """测试获取提供商"""
        assert service.get_provider() == LLMProvider.DIFY
    
    def test_get_model_info(self, service, config):
        """测试获取模型信息"""
        info = service.get_model_info()
        assert info["provider"] == "dify"
        assert info["initialized"] is False
        assert info["conversations_count"] == 0
    
    def test_build_request_data(self, service, sample_request):
        """测试构建请求数据"""
        data = service._build_request_data(sample_request, stream=False)
        
        assert data["query"] == "你好"
        assert data["response_mode"] == "blocking"
        assert data["user"] == "default_user"
        assert data["conversation_id"] == ""
        assert data["inputs"]["k"] == 2
    
    def test_build_request_data_with_metadata(self, service):
        """测试带元数据的请求数据构建"""
        request = LLMRequest(
            messages=[LLMMessage(role="user", content="测试", timestamp=time.time())],
            metadata={
                "user_id": "user123",
                "conversation_id": "conv456"
            }
        )
        
        data = service._build_request_data(request, stream=True)
        
        assert data["query"] == "测试"
        assert data["response_mode"] == "streaming"
        assert data["user"] == "user123"
        assert data["conversation_id"] == "conv456"
    
    def test_build_request_data_no_user_message(self, service):
        """测试没有用户消息的请求"""
        request = LLMRequest(
            messages=[LLMMessage(role="assistant", content="助手消息", timestamp=time.time())]
        )
        
        with pytest.raises(ValueError, match="请求中没有用户消息"):
            service._build_request_data(request)
    
    @pytest.mark.asyncio
    async def test_send_request_with_retry_success(self, service, config):
        """测试重试机制成功"""
        await service.initialize(config)
        
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {"answer": "成功"}
        
        with patch.object(service.session, 'post') as mock_post:
            mock_post.return_value.__aenter__.return_value = mock_response
            
            data = {"query": "测试"}
            result = await service._send_request_with_retry(data)
            
            assert result == {"answer": "成功"}
            assert mock_post.call_count == 1
    
    @pytest.mark.asyncio
    async def test_send_request_with_retry_failure(self, service, config):
        """测试重试机制失败"""
        await service.initialize(config)
        
        with patch.object(service.session, 'post', side_effect=aiohttp.ClientError("网络错误")):
            data = {"query": "测试"}
            
            with pytest.raises(aiohttp.ClientError):
                await service._send_request_with_retry(data)
    
    def test_parse_response(self, service):
        """测试响应解析"""
        response_data = {
            "answer": "测试回答",
            "metadata": {
                "usage": {
                    "prompt_tokens": 5,
                    "completion_tokens": 10,
                    "total_tokens": 15
                }
            },
            "model": "test-model"
        }
        
        response = service._parse_response(response_data, 1.5)
        
        assert response.content == "测试回答"
        assert response.usage["total_tokens"] == 15
        assert response.model == "test-model"
        assert response.processing_time == 1.5
    
    @pytest.mark.asyncio
    async def test_conversation_management(self, service):
        """测试对话管理功能"""
        # 开始对话
        conversation_id = await service.start_conversation("user123")
        assert service.is_conversation_active(conversation_id)
        
        # 添加消息
        message = LLMMessage(role="user", content="测试消息", timestamp=time.time())
        await service.add_message(conversation_id, message)
        
        # 获取历史
        history = await service.get_history(conversation_id)
        assert len(history) == 1
        assert history[0].content == "测试消息"
        
        # 清除历史
        await service.clear_history(conversation_id)
        history = await service.get_history(conversation_id)
        assert len(history) == 0
        
        # 结束对话
        await service.end_conversation(conversation_id)
        assert not service.is_conversation_active(conversation_id)
    
    @pytest.mark.asyncio
    async def test_conversation_nonexistent(self, service):
        """测试不存在的对话操作"""
        fake_id = "nonexistent"
        
        with pytest.raises(ValueError, match="对话不存在"):
            await service.add_message(fake_id, LLMMessage(role="user", content="test", timestamp=time.time()))
        
        history = await service.get_history(fake_id)
        assert len(history) == 0
        
        assert not service.is_conversation_active(fake_id)
    
    @pytest.mark.asyncio
    async def test_cleanup(self, service, config):
        """测试资源清理"""
        await service.initialize(config)
        conversation_id = await service.start_conversation("user123")
        
        assert service.session is not None
        assert len(service.conversations) == 1
        assert service.is_initialized is True
        
        await service.cleanup()
        
        assert service.session is None
        assert len(service.conversations) == 0
        assert service.is_initialized is False


class TestDifyConfig:
    """Dify配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = DifyConfig(
            api_url="http://test.com",
            api_key="test-key"
        )
        
        assert config.timeout == 30
        assert config.retry_attempts == 3
        assert config.retry_delay == 1.0
        assert config.max_retry_delay == 10.0


class TestConversationContext:
    """对话上下文测试"""
    
    def test_creation(self):
        """测试创建"""
        context = ConversationContext(
            conversation_id="test-id",
            user_id="user123"
        )
        
        assert context.conversation_id == "test-id"
        assert context.user_id == "user123"
        assert len(context.messages) == 0
        assert context.created_at > 0
        assert context.last_activity > 0
        assert isinstance(context.metadata, dict)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
