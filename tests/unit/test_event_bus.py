"""
事件总线单元测试
"""

import pytest
import asyncio
import time
from unittest.mock import AsyncMock, MagicMock

from src.services.event_bus import EventBus, EventPriority, Event


class TestEventBus:
    """事件总线测试类"""
    
    @pytest.fixture
    async def event_bus(self):
        """创建事件总线实例"""
        bus = EventBus(max_queue_size=100)
        await bus.initialize()
        yield bus
        await bus.shutdown()
    
    @pytest.mark.asyncio
    async def test_initialization(self):
        """测试事件总线初始化"""
        bus = EventBus()
        assert not bus.is_running
        
        success = await bus.initialize()
        assert success
        assert bus.is_running
        assert len(bus.worker_tasks) == bus.num_workers
        
        await bus.shutdown()
        assert not bus.is_running
    
    @pytest.mark.asyncio
    async def test_event_emission_and_subscription(self, event_bus):
        """测试事件发送和订阅"""
        received_data = []
        
        async def test_handler(data):
            received_data.append(data)
        
        # 订阅事件
        handler_id = await event_bus.subscribe("test_event", test_handler)
        assert handler_id is not None
        
        # 发送事件
        test_data = {"message": "hello", "value": 42}
        event_id = await event_bus.emit("test_event", test_data)
        assert event_id is not None
        
        # 等待事件处理
        await asyncio.sleep(0.1)
        
        # 验证事件被处理
        assert len(received_data) == 1
        assert received_data[0] == test_data
    
    @pytest.mark.asyncio
    async def test_event_priority(self, event_bus):
        """测试事件优先级"""
        processed_order = []
        
        async def handler(data):
            processed_order.append(data["priority"])
            await asyncio.sleep(0.01)  # 模拟处理时间
        
        await event_bus.subscribe("priority_test", handler)
        
        # 发送不同优先级的事件
        await event_bus.emit("priority_test", {"priority": "low"}, EventPriority.LOW)
        await event_bus.emit("priority_test", {"priority": "critical"}, EventPriority.CRITICAL)
        await event_bus.emit("priority_test", {"priority": "normal"}, EventPriority.NORMAL)
        await event_bus.emit("priority_test", {"priority": "high"}, EventPriority.HIGH)
        
        # 等待所有事件处理完成
        await asyncio.sleep(0.2)
        
        # 验证处理顺序（高优先级先处理）
        assert processed_order[0] == "critical"
        assert processed_order[1] == "high"
        assert processed_order[2] == "normal"
        assert processed_order[3] == "low"
    
    @pytest.mark.asyncio
    async def test_multiple_handlers(self, event_bus):
        """测试多个处理器"""
        handler1_called = False
        handler2_called = False
        
        async def handler1(data):
            nonlocal handler1_called
            handler1_called = True
        
        async def handler2(data):
            nonlocal handler2_called
            handler2_called = True
        
        # 订阅同一事件的多个处理器
        await event_bus.subscribe("multi_handler_test", handler1)
        await event_bus.subscribe("multi_handler_test", handler2)
        
        # 发送事件
        await event_bus.emit("multi_handler_test", {"test": "data"})
        
        # 等待处理
        await asyncio.sleep(0.1)
        
        # 验证所有处理器都被调用
        assert handler1_called
        assert handler2_called
    
    @pytest.mark.asyncio
    async def test_error_handling(self, event_bus):
        """测试错误处理"""
        error_handled = False
        
        async def failing_handler(data):
            raise ValueError("Test error")
        
        async def error_handler(event, error):
            nonlocal error_handled
            error_handled = True
            assert isinstance(error, ValueError)
        
        # 订阅带错误处理器的事件
        await event_bus.subscribe(
            "error_test", 
            failing_handler, 
            error_handler=error_handler
        )
        
        # 发送事件
        await event_bus.emit("error_test", {"test": "data"})
        
        # 等待处理
        await asyncio.sleep(0.1)
        
        # 验证错误被处理
        assert error_handled
    
    @pytest.mark.asyncio
    async def test_event_retry(self, event_bus):
        """测试事件重试"""
        call_count = 0
        
        async def flaky_handler(data):
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise RuntimeError("Temporary failure")
        
        await event_bus.subscribe("retry_test", flaky_handler)
        
        # 发送事件
        await event_bus.emit("retry_test", {"test": "data"})
        
        # 等待重试完成
        await asyncio.sleep(0.3)
        
        # 验证重试次数
        assert call_count >= 2  # 至少重试一次
    
    @pytest.mark.asyncio
    async def test_unsubscribe(self, event_bus):
        """测试取消订阅"""
        handler_called = False
        
        async def test_handler(data):
            nonlocal handler_called
            handler_called = True
        
        # 订阅事件
        handler_id = await event_bus.subscribe("unsubscribe_test", test_handler)
        
        # 取消订阅
        success = await event_bus.unsubscribe("unsubscribe_test", handler_id)
        assert success
        
        # 发送事件
        await event_bus.emit("unsubscribe_test", {"test": "data"})
        
        # 等待处理
        await asyncio.sleep(0.1)
        
        # 验证处理器没有被调用
        assert not handler_called
    
    @pytest.mark.asyncio
    async def test_event_filter(self, event_bus):
        """测试事件过滤器"""
        filtered_events = []
        
        async def test_handler(data):
            filtered_events.append(data)
        
        async def event_filter(event):
            # 只允许包含"allowed"字段的事件
            return "allowed" in event.data
        
        # 添加过滤器
        event_bus.add_filter(event_filter)
        
        # 订阅事件
        await event_bus.subscribe("filter_test", test_handler)
        
        # 发送事件
        await event_bus.emit("filter_test", {"allowed": True, "data": "pass"})
        await event_bus.emit("filter_test", {"data": "block"})
        
        # 等待处理
        await asyncio.sleep(0.1)
        
        # 验证只有符合条件的事件被处理
        assert len(filtered_events) == 1
        assert filtered_events[0]["allowed"] is True
    
    @pytest.mark.asyncio
    async def test_middleware(self, event_bus):
        """测试中间件"""
        middleware_calls = []
        
        async def test_handler(data):
            pass
        
        async def test_middleware(event, phase):
            middleware_calls.append((event.name, phase))
        
        # 添加中间件
        event_bus.add_middleware(test_middleware)
        
        # 订阅事件
        await event_bus.subscribe("middleware_test", test_handler)
        
        # 发送事件
        await event_bus.emit("middleware_test", {"test": "data"})
        
        # 等待处理
        await asyncio.sleep(0.1)
        
        # 验证中间件被调用
        assert len(middleware_calls) == 2
        assert ("middleware_test", "before") in middleware_calls
        assert ("middleware_test", "after") in middleware_calls
    
    @pytest.mark.asyncio
    async def test_statistics(self, event_bus):
        """测试统计信息"""
        async def test_handler(data):
            pass
        
        await event_bus.subscribe("stats_test", test_handler)
        
        # 发送多个事件
        for i in range(5):
            await event_bus.emit("stats_test", {"index": i})
        
        # 等待处理
        await asyncio.sleep(0.2)
        
        # 获取统计信息
        stats = event_bus.get_stats()
        
        # 验证统计信息
        assert stats["stats"]["total_events"] >= 5
        assert stats["stats"]["processed_events"] >= 5
        assert stats["stats"]["success_rate"] > 0
        assert "queue_sizes" in stats
        assert "active_handlers" in stats
    
    @pytest.mark.asyncio
    async def test_circuit_breaker(self, event_bus):
        """测试熔断器"""
        call_count = 0
        
        async def failing_handler(data):
            nonlocal call_count
            call_count += 1
            raise RuntimeError("Always fails")
        
        await event_bus.subscribe("circuit_test", failing_handler)
        
        # 发送多个事件触发熔断器
        for i in range(10):
            await event_bus.emit("circuit_test", {"index": i})
        
        # 等待处理
        await asyncio.sleep(0.5)
        
        # 验证熔断器生效（调用次数应该少于发送的事件数）
        assert call_count < 10
    
    @pytest.mark.asyncio
    async def test_dead_letter_queue(self, event_bus):
        """测试死信队列"""
        async def always_failing_handler(data):
            raise RuntimeError("Always fails")
        
        await event_bus.subscribe("dead_letter_test", always_failing_handler)
        
        # 发送事件
        await event_bus.emit("dead_letter_test", {"test": "data"})
        
        # 等待处理和重试
        await asyncio.sleep(0.5)
        
        # 验证事件进入死信队列
        stats = event_bus.get_stats()
        assert stats["dead_letter_queue_size"] > 0


if __name__ == "__main__":
    pytest.main([__file__])
