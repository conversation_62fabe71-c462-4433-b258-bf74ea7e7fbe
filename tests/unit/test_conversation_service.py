#!/usr/bin/env python3
"""
对话管理服务单元测试
"""

import asyncio
import pytest
import time
from unittest.mock import AsyncMock, MagicMock

from src.services.conversation_service import (
    ConversationService, ConversationState, MessageType, 
    ConversationMessage, ConversationContext
)
from src.services.event_bus import EventBus


@pytest.fixture
def event_loop():
    """事件循环fixture"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def event_bus():
    """事件总线fixture"""
    bus = EventBus(1000)
    await bus.initialize()
    try:
        yield bus
    finally:
        await bus.shutdown()


@pytest.fixture
async def conversation_service(event_bus):
    """对话管理服务fixture"""
    config = {
        "max_context_length": 5,
        "session_timeout": 10.0,
        "max_turn_count": 10,
        "enable_interruption": True,
        "interruption_threshold": 0.5
    }
    service = ConversationService(config, event_bus)
    await service.initialize()
    await service.start()
    try:
        yield service
    finally:
        await service.stop()


@pytest.mark.asyncio
class TestConversationService:
    """对话管理服务测试类"""
    
    async def test_service_initialization(self, conversation_service):
        """测试服务初始化"""
        assert conversation_service.status == ServiceStatus.RUNNING
        assert conversation_service.current_context is None
        assert conversation_service.total_conversations == 0
    
    async def test_start_new_conversation(self, conversation_service):
        """测试开始新对话"""
        session_id = await conversation_service.start_new_conversation("test_user")
        
        assert conversation_service.current_context is not None
        assert conversation_service.current_context.session_id == session_id
        assert conversation_service.current_context.user_id == "test_user"
        assert conversation_service.total_conversations == 1
        assert len(conversation_service.current_context.messages) == 1
        assert conversation_service.current_context.messages[0].type == MessageType.SYSTEM
    
    async def test_handle_user_input(self, conversation_service):
        """测试处理用户输入"""
        await conversation_service.start_new_conversation()
        
        await conversation_service._handle_user_input("你好", 0.9, {})
        
        context = conversation_service.get_current_context()
        assert context.turn_count == 1
        assert len(context.messages) == 2  # 系统消息 + 用户消息
        
        user_message = context.messages[-1]
        assert user_message.type == MessageType.USER
        assert user_message.content == "你好"
        assert user_message.confidence == 0.9
    
    async def test_add_assistant_message(self, conversation_service):
        """测试添加助手消息"""
        await conversation_service.start_new_conversation()
        
        message_id = conversation_service.add_assistant_message("你好！我是AI助手。")
        
        context = conversation_service.get_current_context()
        assert len(context.messages) == 2  # 系统消息 + 助手消息
        
        assistant_message = context.messages[-1]
        assert assistant_message.type == MessageType.ASSISTANT
        assert assistant_message.content == "你好！我是AI助手。"
        assert assistant_message.id == message_id
    
    async def test_text_cleaning(self, conversation_service):
        """测试文本清理"""
        # 测试正常文本
        cleaned = conversation_service._clean_input_text("你好世界")
        assert cleaned == "你好世界"
        
        # 测试包含噪音的文本
        noisy_text = "<|startoftranscript|>你好，，，世界<|endoftranscript|>"
        cleaned = conversation_service._clean_input_text(noisy_text)
        assert cleaned == "你好世界"
        
        # 测试空文本
        cleaned = conversation_service._clean_input_text("。，、")
        assert cleaned == ""
        
        # 测试过短文本
        cleaned = conversation_service._clean_input_text("a")
        assert cleaned == ""
    
    async def test_conversation_context_management(self, conversation_service):
        """测试对话上下文管理"""
        await conversation_service.start_new_conversation()
        
        # 添加多条消息
        for i in range(3):
            await conversation_service._handle_user_input(f"消息{i}", 0.9, {})
            conversation_service.add_assistant_message(f"回复{i}")
        
        context = conversation_service.get_current_context()
        assert context.turn_count == 3
        assert len(context.messages) == 7  # 1系统 + 3用户 + 3助手
        
        # 测试获取最近消息
        recent_messages = conversation_service.get_recent_messages(3)
        assert len(recent_messages) == 3
        assert recent_messages[-1].content == "回复2"
    
    async def test_conversation_history_trimming(self, conversation_service):
        """测试对话历史修剪"""
        await conversation_service.start_new_conversation()
        
        # 添加超过最大长度的消息
        for i in range(10):
            await conversation_service._handle_user_input(f"消息{i}", 0.9, {})
        
        context = conversation_service.get_current_context()
        # 应该被修剪到最大长度
        assert len(context.messages) <= conversation_service.max_context_length
        
        # 系统消息应该被保留
        system_messages = [msg for msg in context.messages if msg.type == MessageType.SYSTEM]
        assert len(system_messages) >= 1
    
    async def test_max_turns_handling(self, conversation_service):
        """测试最大轮数处理"""
        await conversation_service.start_new_conversation()
        
        # 达到最大轮数
        for i in range(conversation_service.max_turn_count + 1):
            await conversation_service._handle_user_input(f"消息{i}", 0.9, {})
        
        # 对话应该被结束
        assert conversation_service.current_context is None
    
    async def test_conversation_statistics(self, conversation_service):
        """测试对话统计"""
        # 开始对话
        await conversation_service.start_new_conversation()
        await conversation_service._handle_user_input("测试消息", 0.9, {})
        conversation_service.add_assistant_message("测试回复")
        
        stats = conversation_service.get_conversation_statistics()
        
        assert stats["total_conversations"] == 1
        assert stats["total_messages"] == 3  # 1系统 + 1用户 + 1助手
        assert stats["current_turn_count"] == 1
        assert stats["is_processing"] == False
        assert stats["is_responding"] == False
    
    async def test_interruption_callback(self, conversation_service):
        """测试打断回调"""
        callback_called = False
        callback_context = None
        
        def interruption_callback(context, event_data):
            nonlocal callback_called, callback_context
            callback_called = True
            callback_context = context
        
        # 添加回调
        conversation_service.add_interruption_callback(interruption_callback)
        
        # 开始对话并设置响应状态
        await conversation_service.start_new_conversation()
        conversation_service.is_responding = True
        
        # 触发打断
        await conversation_service._handle_interruption({"activity_level": 0.8})
        
        assert callback_called == True
        assert callback_context is not None
    
    async def test_conversation_context_data(self, conversation_service):
        """测试对话上下文数据"""
        await conversation_service.start_new_conversation()
        
        # 设置上下文数据
        await conversation_service.set_conversation_context_data("user_preference", "简洁回复")
        await conversation_service.set_conversation_context_data("topic", "天气")
        
        # 获取上下文数据
        preference = conversation_service.get_conversation_context_data("user_preference")
        topic = conversation_service.get_conversation_context_data("topic")
        missing = conversation_service.get_conversation_context_data("missing", "default")
        
        assert preference == "简洁回复"
        assert topic == "天气"
        assert missing == "default"
    
    async def test_force_end_conversation(self, conversation_service):
        """测试强制结束对话"""
        await conversation_service.start_new_conversation()
        await conversation_service._handle_user_input("测试", 0.9, {})
        
        assert conversation_service.current_context is not None
        
        await conversation_service.force_end_conversation("测试结束")
        
        assert conversation_service.current_context is None
    
    async def test_error_handling(self, conversation_service):
        """测试错误处理"""
        await conversation_service.start_new_conversation()
        
        # 测试识别错误处理
        await conversation_service._handle_recognition_error("网络错误")
        
        context = conversation_service.get_current_context()
        error_messages = [msg for msg in context.messages if msg.type == MessageType.ERROR]
        assert len(error_messages) == 1
        assert "网络错误" in error_messages[0].content
    
    async def test_empty_input_handling(self, conversation_service):
        """测试空输入处理"""
        await conversation_service.start_new_conversation()
        
        # 测试空文本
        await conversation_service._handle_user_input("", 0.9, {})
        
        context = conversation_service.get_current_context()
        # 应该只有系统消息，没有用户消息
        user_messages = [msg for msg in context.messages if msg.type == MessageType.USER]
        assert len(user_messages) == 0
    
    async def test_health_check(self, conversation_service):
        """测试健康检查"""
        # 正常状态下应该健康
        assert await conversation_service._health_check_impl() == True
        
        # 开始对话后仍应该健康
        await conversation_service.start_new_conversation()
        assert await conversation_service._health_check_impl() == True


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
