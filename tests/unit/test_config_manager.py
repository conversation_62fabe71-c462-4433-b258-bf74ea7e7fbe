"""
配置管理器单元测试
"""

import pytest
import os
import tempfile
import yaml
from pathlib import Path
from unittest.mock import patch, MagicMock

from src.infrastructure.config_manager import ConfigManager


class TestConfigManager:
    """配置管理器测试类"""
    
    @pytest.fixture
    def temp_config_dir(self):
        """创建临时配置目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    @pytest.fixture
    def sample_config(self):
        """示例配置数据"""
        return {
            "system": {
                "name": "test-system",
                "version": "1.0.0",
                "debug": True
            },
            "audio": {
                "sample_rate": 16000,
                "channels": 1
            },
            "logging": {
                "level": "DEBUG"
            }
        }
    
    @pytest.fixture
    def config_manager(self):
        """创建配置管理器实例"""
        manager = ConfigManager()
        manager.enable_hot_reload = False  # 测试时禁用热重载
        return manager
    
    def create_config_file(self, config_dir: str, filename: str, config_data: dict) -> str:
        """创建配置文件"""
        config_path = os.path.join(config_dir, filename)
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, default_flow_style=False)
        return config_path
    
    @pytest.mark.asyncio
    async def test_load_basic_config(self, config_manager, temp_config_dir, sample_config):
        """测试基本配置加载"""
        # 创建配置文件
        config_path = self.create_config_file(temp_config_dir, "config.yaml", sample_config)
        
        # 加载配置
        success = await config_manager.load_config(config_path, "development")
        assert success
        
        # 验证配置加载
        assert config_manager.get_config("system.name") == "test-system"
        assert config_manager.get_config("audio.sample_rate") == 16000
        assert config_manager.get_config("logging.level") == "DEBUG"
    
    @pytest.mark.asyncio
    async def test_environment_config_override(self, config_manager, temp_config_dir, sample_config):
        """测试环境配置覆盖"""
        # 创建主配置文件
        config_path = self.create_config_file(temp_config_dir, "config.yaml", sample_config)
        
        # 创建开发环境配置
        dev_config = {
            "system": {
                "debug": False  # 覆盖主配置
            },
            "audio": {
                "sample_rate": 22050  # 覆盖主配置
            },
            "new_section": {
                "value": "dev-only"  # 新增配置
            }
        }
        self.create_config_file(temp_config_dir, "config.development.yaml", dev_config)
        
        # 加载配置
        success = await config_manager.load_config(config_path, "development")
        assert success
        
        # 验证环境配置覆盖
        assert config_manager.get_config("system.debug") is False  # 被覆盖
        assert config_manager.get_config("audio.sample_rate") == 22050  # 被覆盖
        assert config_manager.get_config("system.name") == "test-system"  # 保持原值
        assert config_manager.get_config("new_section.value") == "dev-only"  # 新增
    
    @pytest.mark.asyncio
    async def test_environment_variable_substitution(self, config_manager, temp_config_dir):
        """测试环境变量替换"""
        # 设置环境变量
        os.environ["TEST_API_KEY"] = "secret-key-123"
        os.environ["TEST_PORT"] = "8080"
        
        try:
            # 创建包含环境变量的配置
            config_with_env = {
                "api": {
                    "key": "${TEST_API_KEY}",
                    "port": "${TEST_PORT}",
                    "url": "${TEST_URL:http://localhost}"  # 带默认值
                }
            }
            config_path = self.create_config_file(temp_config_dir, "config.yaml", config_with_env)
            
            # 加载配置
            success = await config_manager.load_config(config_path)
            assert success
            
            # 验证环境变量替换
            assert config_manager.get_config("api.key") == "secret-key-123"
            assert config_manager.get_config("api.port") == "8080"
            assert config_manager.get_config("api.url") == "http://localhost"  # 使用默认值
            
        finally:
            # 清理环境变量
            os.environ.pop("TEST_API_KEY", None)
            os.environ.pop("TEST_PORT", None)
    
    @pytest.mark.asyncio
    async def test_environment_variable_override(self, config_manager, temp_config_dir, sample_config):
        """测试环境变量覆盖配置"""
        # 设置环境变量
        os.environ["AIBI_SYSTEM_DEBUG"] = "false"
        os.environ["AIBI_AUDIO_SAMPLE_RATE"] = "44100"
        
        try:
            config_path = self.create_config_file(temp_config_dir, "config.yaml", sample_config)
            
            # 加载配置
            success = await config_manager.load_config(config_path)
            assert success
            
            # 验证环境变量覆盖
            assert config_manager.get_config("system.debug") is False
            assert config_manager.get_config("audio.sample_rate") == 44100
            
        finally:
            # 清理环境变量
            os.environ.pop("AIBI_SYSTEM_DEBUG", None)
            os.environ.pop("AIBI_AUDIO_SAMPLE_RATE", None)
    
    @pytest.mark.asyncio
    async def test_config_validation_success(self, config_manager, temp_config_dir, sample_config):
        """测试配置验证成功"""
        config_path = self.create_config_file(temp_config_dir, "config.yaml", sample_config)
        
        success = await config_manager.load_config(config_path)
        assert success
    
    @pytest.mark.asyncio
    async def test_config_validation_failure(self, config_manager, temp_config_dir):
        """测试配置验证失败"""
        # 创建无效配置（缺少必需字段）
        invalid_config = {
            "system": {
                "version": "1.0.0"
                # 缺少 name 字段
            }
            # 缺少 audio 和 logging 节
        }
        config_path = self.create_config_file(temp_config_dir, "config.yaml", invalid_config)
        
        success = await config_manager.load_config(config_path)
        assert not success
    
    def test_get_config_nested(self, config_manager):
        """测试嵌套配置获取"""
        # 设置测试配置
        config_manager.config = {
            "level1": {
                "level2": {
                    "level3": "value"
                }
            }
        }
        
        # 测试嵌套获取
        assert config_manager.get_config("level1.level2.level3") == "value"
        assert config_manager.get_config("level1.level2") == {"level3": "value"}
        assert config_manager.get_config("level1") == {"level2": {"level3": "value"}}
        assert config_manager.get_config() == config_manager.config
        
        # 测试不存在的路径
        assert config_manager.get_config("nonexistent") is None
        assert config_manager.get_config("level1.nonexistent") is None
    
    def test_set_config(self, config_manager):
        """测试配置设置"""
        config_manager.config = {}
        
        # 设置嵌套配置
        config_manager.set_config("level1.level2.value", "test")
        assert config_manager.get_config("level1.level2.value") == "test"
        
        # 设置数字配置
        config_manager.set_config("number", "123")
        assert config_manager.get_config("number") == 123
        
        # 设置布尔配置
        config_manager.set_config("boolean", "true")
        assert config_manager.get_config("boolean") is True
    
    def test_environment_detection(self, config_manager):
        """测试环境检测"""
        config_manager.environment = "development"
        assert config_manager.is_development()
        assert not config_manager.is_production()
        
        config_manager.environment = "production"
        assert not config_manager.is_development()
        assert config_manager.is_production()
        
        assert config_manager.get_environment() == "production"
    
    @pytest.mark.asyncio
    async def test_invalid_yaml_file(self, config_manager, temp_config_dir):
        """测试无效YAML文件"""
        # 创建无效YAML文件
        invalid_yaml_path = os.path.join(temp_config_dir, "invalid.yaml")
        with open(invalid_yaml_path, 'w') as f:
            f.write("invalid: yaml: content: [")
        
        success = await config_manager.load_config(invalid_yaml_path)
        assert not success
    
    @pytest.mark.asyncio
    async def test_nonexistent_config_file(self, config_manager):
        """测试不存在的配置文件"""
        success = await config_manager.load_config("/nonexistent/config.yaml")
        assert not success
    
    @pytest.mark.asyncio
    async def test_config_merge_deep(self, config_manager, temp_config_dir):
        """测试深度配置合并"""
        # 主配置
        main_config = {
            "database": {
                "host": "localhost",
                "port": 5432,
                "settings": {
                    "timeout": 30,
                    "pool_size": 10
                }
            }
        }
        
        # 环境配置
        env_config = {
            "database": {
                "host": "prod-server",  # 覆盖
                "settings": {
                    "timeout": 60,  # 覆盖
                    "ssl": True     # 新增
                }
                # port 保持不变
            }
        }
        
        config_path = self.create_config_file(temp_config_dir, "config.yaml", main_config)
        self.create_config_file(temp_config_dir, "config.test.yaml", env_config)
        
        success = await config_manager.load_config(config_path, "test")
        assert success
        
        # 验证深度合并
        assert config_manager.get_config("database.host") == "prod-server"  # 覆盖
        assert config_manager.get_config("database.port") == 5432  # 保持
        assert config_manager.get_config("database.settings.timeout") == 60  # 覆盖
        assert config_manager.get_config("database.settings.pool_size") == 10  # 保持
        assert config_manager.get_config("database.settings.ssl") is True  # 新增
    
    @pytest.mark.asyncio
    async def test_cleanup(self, config_manager):
        """测试资源清理"""
        # 模拟观察者
        config_manager.observer = MagicMock()
        
        await config_manager.cleanup()
        
        # 验证观察者被停止
        config_manager.observer.stop.assert_called_once()
        config_manager.observer.join.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])
