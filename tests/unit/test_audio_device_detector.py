"""
音频设备检测器单元测试
"""

import pytest
from unittest.mock import patch, MagicMock
import sounddevice as sd

from src.infrastructure.audio_device_detector import AudioDeviceDetector, DeviceTestResult
from src.interfaces.audio_interface import AudioConfig, AudioDevice


class TestAudioDeviceDetector:
    """音频设备检测器测试类"""
    
    @pytest.fixture
    def detector(self):
        """创建设备检测器实例"""
        return AudioDeviceDetector()
    
    @pytest.fixture
    def audio_config(self):
        """音频配置"""
        return AudioConfig(
            sample_rate=16000,
            channels=1,
            chunk_size=1024,
            buffer_size=32000
        )
    
    @pytest.fixture
    def mock_device_list(self):
        """模拟设备列表"""
        return [
            {
                'name': 'Mock Microphone',
                'max_input_channels': 2,
                'max_output_channels': 0,
                'default_samplerate': 44100.0
            },
            {
                'name': 'Mock Speaker',
                'max_input_channels': 0,
                'max_output_channels': 2,
                'default_samplerate': 44100.0
            },
            {
                'name': 'Mock Headset',
                'max_input_channels': 1,
                'max_output_channels': 2,
                'default_samplerate': 48000.0
            },
            {
                'name': 'Invalid Device',
                'max_input_channels': 0,
                'max_output_channels': 0,
                'default_samplerate': 44100.0
            }
        ]
    
    @patch('sounddevice.query_devices')
    @patch('sounddevice.default')
    def test_detect_all_devices(self, mock_default, mock_query_devices, detector, mock_device_list):
        """测试检测所有设备"""
        mock_query_devices.return_value = mock_device_list
        mock_default.device = [0, 1]  # 默认输入设备0，输出设备1
        
        devices = detector.detect_all_devices()
        
        # 验证设备数量（排除无效设备）
        assert len(devices) == 4  # 2个输入 + 2个输出
        
        # 验证输入设备
        input_devices = [d for d in devices if d.is_input]
        assert len(input_devices) == 2
        assert any(d.name == "Mock Microphone" for d in input_devices)
        assert any(d.name == "Mock Headset" for d in input_devices)
        
        # 验证输出设备
        output_devices = [d for d in devices if not d.is_input]
        assert len(output_devices) == 2
        assert any(d.name == "Mock Speaker" for d in output_devices)
        assert any(d.name == "Mock Headset" for d in output_devices)
        
        # 验证默认设备标记
        default_input = next((d for d in devices if d.is_input and d.is_default), None)
        assert default_input is not None
        assert default_input.id == 0
        
        default_output = next((d for d in devices if not d.is_input and d.is_default), None)
        assert default_output is not None
        assert default_output.id == 1
    
    @patch('sounddevice.query_devices')
    @patch('sounddevice.default')
    def test_get_default_devices(self, mock_default, mock_query_devices, detector, mock_device_list):
        """测试获取默认设备"""
        mock_query_devices.return_value = mock_device_list
        mock_default.device = [0, 1]
        
        default_input, default_output = detector.get_default_devices()
        
        assert default_input is not None
        assert default_input.is_input
        assert default_input.is_default
        assert default_input.id == 0
        
        assert default_output is not None
        assert not default_output.is_input
        assert default_output.is_default
        assert default_output.id == 1
    
    @patch('sounddevice.rec')
    @patch('sounddevice.wait')
    def test_test_input_device_success(self, mock_wait, mock_rec, detector, audio_config):
        """测试输入设备测试成功"""
        import numpy as np
        
        # 模拟录音数据
        mock_audio_data = np.random.random((8000, 1)).astype(np.float32) * 0.1
        mock_rec.return_value = mock_audio_data
        
        device = AudioDevice(
            id=0,
            name="Test Microphone",
            channels=1,
            sample_rate=16000,
            is_input=True,
            is_default=True
        )
        
        with patch.object(detector, '_test_sample_rates') as mock_test_rates, \
             patch.object(detector, '_test_channels') as mock_test_channels:
            
            mock_test_rates.return_value = [16000, 44100]
            mock_test_channels.return_value = [1, 2]
            
            result = detector.test_device(device, audio_config)
            
            assert result.success
            assert result.device_id == 0
            assert result.device_name == "Test Microphone"
            assert result.is_input
            assert result.latency_ms is not None
            assert result.latency_ms > 0
            assert result.sample_rate_supported == [16000, 44100]
            assert result.channels_supported == [1, 2]
    
    @patch('sounddevice.rec')
    def test_test_input_device_failure(self, mock_rec, detector, audio_config):
        """测试输入设备测试失败"""
        # 模拟录音失败
        mock_rec.side_effect = Exception("Device not available")
        
        device = AudioDevice(
            id=0,
            name="Broken Microphone",
            channels=1,
            sample_rate=16000,
            is_input=True,
            is_default=False
        )
        
        result = detector.test_device(device, audio_config)
        
        assert not result.success
        assert result.device_id == 0
        assert result.device_name == "Broken Microphone"
        assert result.is_input
        assert "Device not available" in result.error_message
    
    @patch('sounddevice.play')
    @patch('sounddevice.wait')
    def test_test_output_device_success(self, mock_wait, mock_play, detector, audio_config):
        """测试输出设备测试成功"""
        device = AudioDevice(
            id=1,
            name="Test Speaker",
            channels=2,
            sample_rate=44100,
            is_input=False,
            is_default=True
        )
        
        with patch.object(detector, '_test_sample_rates') as mock_test_rates, \
             patch.object(detector, '_test_channels') as mock_test_channels:
            
            mock_test_rates.return_value = [16000, 44100]
            mock_test_channels.return_value = [1, 2]
            
            result = detector.test_device(device, audio_config)
            
            assert result.success
            assert result.device_id == 1
            assert result.device_name == "Test Speaker"
            assert not result.is_input
            assert result.latency_ms is not None
            assert result.latency_ms > 0
            
            # 验证播放被调用
            mock_play.assert_called_once()
    
    @patch('sounddevice.check_input_settings')
    def test_test_sample_rates_input(self, mock_check_input, detector):
        """测试输入设备采样率测试"""
        device = AudioDevice(
            id=0,
            name="Test Mic",
            channels=1,
            sample_rate=44100,
            is_input=True,
            is_default=True
        )
        
        # 模拟部分采样率支持
        def check_settings_side_effect(device, channels, samplerate):
            if samplerate in [16000, 44100]:
                return  # 成功
            else:
                raise Exception("Unsupported sample rate")
        
        mock_check_input.side_effect = check_settings_side_effect
        
        supported_rates = detector._test_sample_rates(device, True)
        
        assert 16000 in supported_rates
        assert 44100 in supported_rates
        assert 8000 not in supported_rates  # 假设不支持
    
    @patch('sounddevice.check_output_settings')
    def test_test_sample_rates_output(self, mock_check_output, detector):
        """测试输出设备采样率测试"""
        device = AudioDevice(
            id=1,
            name="Test Speaker",
            channels=2,
            sample_rate=48000,
            is_input=False,
            is_default=True
        )
        
        # 模拟所有采样率都支持
        supported_rates = detector._test_sample_rates(device, False)
        
        # 验证调用了检查函数
        assert mock_check_output.called
        assert len(supported_rates) >= 0  # 至少返回空列表
    
    @patch('sounddevice.check_input_settings')
    def test_test_channels(self, mock_check_input, detector):
        """测试声道数测试"""
        device = AudioDevice(
            id=0,
            name="Test Mic",
            channels=2,
            sample_rate=44100,
            is_input=True,
            is_default=True
        )
        
        # 模拟支持1和2声道
        def check_settings_side_effect(device, channels, samplerate):
            if channels <= 2:
                return  # 成功
            else:
                raise Exception("Unsupported channel count")
        
        mock_check_input.side_effect = check_settings_side_effect
        
        supported_channels = detector._test_channels(device, 44100, True)
        
        assert 1 in supported_channels
        assert 2 in supported_channels
        assert len(supported_channels) <= 2
    
    def test_get_recommended_devices(self, detector, audio_config):
        """测试获取推荐设备"""
        # 模拟测试结果
        test_results = [
            DeviceTestResult(
                device_id=0,
                device_name="Fast Mic",
                is_input=True,
                success=True,
                latency_ms=50.0
            ),
            DeviceTestResult(
                device_id=1,
                device_name="Slow Mic",
                is_input=True,
                success=True,
                latency_ms=100.0
            ),
            DeviceTestResult(
                device_id=2,
                device_name="Fast Speaker",
                is_input=False,
                success=True,
                latency_ms=30.0
            ),
            DeviceTestResult(
                device_id=3,
                device_name="Broken Device",
                is_input=True,
                success=False
            )
        ]
        
        with patch.object(detector, 'test_all_devices') as mock_test_all, \
             patch.object(detector, 'detect_all_devices') as mock_detect_all:
            
            mock_test_all.return_value = test_results
            mock_detect_all.return_value = [
                AudioDevice(id=0, name="Fast Mic", channels=1, sample_rate=16000, is_input=True, is_default=False),
                AudioDevice(id=2, name="Fast Speaker", channels=2, sample_rate=16000, is_input=False, is_default=False)
            ]
            
            rec_input, rec_output = detector.get_recommended_devices(audio_config)
            
            # 应该推荐延迟最低的成功设备
            assert rec_input is not None
            assert rec_input.name == "Fast Mic"
            
            assert rec_output is not None
            assert rec_output.name == "Fast Speaker"
    
    def test_generate_device_report(self, detector, audio_config):
        """测试生成设备报告"""
        with patch.object(detector, 'test_all_devices') as mock_test_all, \
             patch.object(detector, 'get_default_devices') as mock_get_default, \
             patch.object(detector, 'get_recommended_devices') as mock_get_recommended:
            
            # 模拟测试结果
            mock_test_all.return_value = [
                DeviceTestResult(
                    device_id=0,
                    device_name="Test Mic",
                    is_input=True,
                    success=True,
                    latency_ms=50.0,
                    sample_rate_supported=[16000, 44100],
                    channels_supported=[1, 2]
                ),
                DeviceTestResult(
                    device_id=1,
                    device_name="Broken Device",
                    is_input=False,
                    success=False,
                    error_message="Device not found"
                )
            ]
            
            # 模拟默认设备
            mock_get_default.return_value = (
                AudioDevice(id=0, name="Default Mic", channels=1, sample_rate=16000, is_input=True, is_default=True),
                AudioDevice(id=1, name="Default Speaker", channels=2, sample_rate=16000, is_input=False, is_default=True)
            )
            
            # 模拟推荐设备
            mock_get_recommended.return_value = (
                AudioDevice(id=0, name="Best Mic", channels=1, sample_rate=16000, is_input=True, is_default=False),
                AudioDevice(id=1, name="Best Speaker", channels=2, sample_rate=16000, is_input=False, is_default=False)
            )
            
            report = detector.generate_device_report(audio_config)
            
            assert "音频设备检测报告" in report
            assert "默认输入设备: Default Mic" in report
            assert "默认输出设备: Default Speaker" in report
            assert "推荐输入设备: Best Mic" in report
            assert "推荐输出设备: Best Speaker" in report
            assert "✅ 通过 Test Mic" in report
            assert "❌ 失败 Broken Device" in report
            assert "延迟: 50.0ms" in report
            assert "错误: Device not found" in report
    
    @patch('sounddevice.query_devices')
    def test_detect_devices_with_exception(self, mock_query_devices, detector):
        """测试设备检测异常处理"""
        mock_query_devices.side_effect = Exception("Audio system error")
        
        devices = detector.detect_all_devices()
        
        # 应该返回空列表而不是抛出异常
        assert devices == []


if __name__ == "__main__":
    pytest.main([__file__])
