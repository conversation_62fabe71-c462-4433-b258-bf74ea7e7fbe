#!/usr/bin/env python3
"""
状态机单元测试
"""

import asyncio
import pytest
import pytest_asyncio
import time
from unittest.mock import AsyncMock, MagicMock

from src.core.state_machine import StateMachine, SystemState
from src.services.event_bus import EventBus


@pytest.fixture
def event_loop():
    """事件循环fixture"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture
async def event_bus():
    """事件总线fixture"""
    config = {
        "max_queue_size": 1000,
        "worker_count": 2,
        "batch_size": 10,
        "batch_timeout": 0.1
    }
    bus = EventBus(config)
    await bus.initialize()
    try:
        yield bus
    finally:
        await bus.shutdown()


@pytest_asyncio.fixture
async def state_machine(event_bus):
    """状态机fixture"""
    config = {
        "conversation_timeout": 2.0,  # 短超时用于测试
        "speech_timeout": 1.0,
        "error_recovery_timeout": 0.5
    }
    sm = StateMachine(config, event_bus)
    await sm.initialize()
    await sm.start()
    try:
        yield sm
    finally:
        await sm.stop()


@pytest.mark.asyncio
class TestStateMachine:
    """状态机测试类"""
    
    async def test_initial_state(self, state_machine):
        """测试初始状态"""
        assert state_machine.get_current_state() == SystemState.WAKE_WORD_LISTENING
    
    async def test_wake_word_detection_transition(self, state_machine):
        """测试唤醒词检测状态转换"""
        # 模拟唤醒词检测事件
        await state_machine._on_wake_word_detected({"keyword": "hey aibi", "confidence": 0.95})
        
        # 检查状态转换
        assert state_machine.get_current_state() == SystemState.SPEECH_COLLECTING
    
    async def test_speech_recognition_transition(self, state_machine):
        """测试语音识别状态转换"""
        # 先转换到语音处理状态
        await state_machine.transition_to(SystemState.SPEECH_PROCESSING, "test")
        
        # 模拟语音识别成功
        await state_machine._on_speech_recognized({
            "text": "你好",
            "confidence": 0.9
        })
        
        # 检查状态转换到对话模式
        assert state_machine.get_current_state() == SystemState.ACTIVE_CONVERSATION
    
    async def test_conversation_timeout(self, state_machine):
        """测试对话超时"""
        # 转换到对话模式
        await state_machine.transition_to(SystemState.ACTIVE_CONVERSATION, "test")
        
        # 等待超时
        await asyncio.sleep(2.5)  # 超过conversation_timeout
        
        # 检查是否回到监听状态
        assert state_machine.get_current_state() == SystemState.WAKE_WORD_LISTENING
    
    async def test_speech_timeout(self, state_machine):
        """测试语音收集超时"""
        # 转换到语音收集状态
        await state_machine.transition_to(SystemState.SPEECH_COLLECTING, "test")
        
        # 等待超时
        await asyncio.sleep(1.5)  # 超过speech_timeout
        
        # 检查是否回到监听状态
        assert state_machine.get_current_state() == SystemState.WAKE_WORD_LISTENING
    
    async def test_error_recovery(self, state_machine):
        """测试错误恢复"""
        # 转换到错误状态
        await state_machine.transition_to(SystemState.ERROR, "test_error")
        
        # 等待错误恢复
        await asyncio.sleep(1.0)  # 超过error_recovery_timeout
        
        # 检查是否恢复到监听状态
        assert state_machine.get_current_state() == SystemState.WAKE_WORD_LISTENING
    
    async def test_invalid_transition(self, state_machine):
        """测试非法状态转换"""
        # 尝试从监听状态直接转换到响应状态（非法）
        result = await state_machine.transition_to(SystemState.RESPONDING, "invalid_event")
        
        # 应该失败
        assert result == False
        assert state_machine.get_current_state() == SystemState.WAKE_WORD_LISTENING
    
    async def test_state_history(self, state_machine):
        """测试状态历史记录"""
        initial_history_count = len(state_machine.get_state_history())
        
        # 执行几次状态转换
        await state_machine.transition_to(SystemState.SPEECH_COLLECTING, "test1")
        await state_machine.transition_to(SystemState.SPEECH_PROCESSING, "test2")
        await state_machine.transition_to(SystemState.ACTIVE_CONVERSATION, "test3")
        
        # 检查历史记录
        history = state_machine.get_state_history()
        assert len(history) == initial_history_count + 3
        
        # 检查最后一条记录
        last_entry = history[-1]
        assert last_entry["to_state"] == SystemState.ACTIVE_CONVERSATION.value
        assert last_entry["event"] == "test3"
    
    async def test_state_listeners(self, state_machine):
        """测试状态监听器"""
        listener_called = False
        listener_state = None
        
        def test_listener(context):
            nonlocal listener_called, listener_state
            listener_called = True
            listener_state = context.current_state
        
        # 添加监听器
        state_machine.add_state_listener(SystemState.SPEECH_COLLECTING, test_listener)
        
        # 触发状态转换
        await state_machine.transition_to(SystemState.SPEECH_COLLECTING, "test")
        
        # 检查监听器是否被调用
        assert listener_called == True
        assert listener_state == SystemState.SPEECH_COLLECTING
    
    async def test_statistics(self, state_machine):
        """测试统计信息"""
        stats = state_machine.get_statistics()
        
        # 检查统计信息结构
        assert "current_state" in stats
        assert "transition_count" in stats
        assert "conversation_duration" in stats
        assert "state_duration" in stats
        
        # 执行一次转换
        await state_machine.transition_to(SystemState.SPEECH_COLLECTING, "test")
        
        # 检查转换计数增加
        new_stats = state_machine.get_statistics()
        assert new_stats["transition_count"] > stats["transition_count"]
    
    async def test_force_transition(self, state_machine):
        """测试强制状态转换"""
        # 强制转换到错误状态
        await state_machine.force_transition(SystemState.ERROR, "test_force")
        
        # 检查状态
        assert state_machine.get_current_state() == SystemState.ERROR
    
    async def test_reset_state_machine(self, state_machine):
        """测试状态机重置"""
        # 转换到某个状态
        await state_machine.transition_to(SystemState.ACTIVE_CONVERSATION, "test")
        
        # 重置状态机
        await state_machine.reset_state_machine()
        
        # 检查是否回到监听状态
        assert state_machine.get_current_state() == SystemState.WAKE_WORD_LISTENING
    
    async def test_continuous_conversation_flow(self, state_machine):
        """测试连续对话流程"""
        # 模拟完整的对话流程
        
        # 1. 检测唤醒词
        await state_machine._on_wake_word_detected({"keyword": "hey aibi"})
        assert state_machine.get_current_state() == SystemState.SPEECH_COLLECTING
        
        # 2. 完成语音收集
        await state_machine.trigger_speech_collection_complete()
        assert state_machine.get_current_state() == SystemState.SPEECH_PROCESSING
        
        # 3. 语音识别成功
        await state_machine._on_speech_recognized({"text": "你好", "confidence": 0.9})
        assert state_machine.get_current_state() == SystemState.ACTIVE_CONVERSATION
        
        # 4. 检测到新的语音活动
        await state_machine.trigger_speech_activity()
        assert state_machine.get_current_state() == SystemState.SPEECH_COLLECTING
        
        # 5. 再次完成语音收集和识别
        await state_machine.trigger_speech_collection_complete()
        await state_machine._on_speech_recognized({"text": "再见", "confidence": 0.9})
        assert state_machine.get_current_state() == SystemState.ACTIVE_CONVERSATION
    
    async def test_health_check(self, state_machine):
        """测试健康检查"""
        # 正常状态下健康检查应该通过
        assert await state_machine._health_check_impl() == True
        
        # 错误状态下健康检查应该失败
        await state_machine.transition_to(SystemState.ERROR, "test_error")
        assert await state_machine._health_check_impl() == False


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
