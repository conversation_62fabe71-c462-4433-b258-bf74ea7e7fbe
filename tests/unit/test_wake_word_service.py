"""
唤醒词检测服务单元测试
"""

import pytest
import asyncio
import numpy as np
import tempfile
import os
import torch
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch

import sys
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.services.wake_word import WakeWordService, AudioBuffer
from src.services.wake_word import <PERSON><PERSON>ordDetector, AudioFeatureExtractor, WakeWordCNN
from src.services.event_bus import EventBus


class TestAudioBuffer:
    """音频缓冲区测试"""
    
    def test_audio_buffer_initialization(self):
        """测试音频缓冲区初始化"""
        buffer = AudioBuffer(
            sample_rate=16000,
            window_size=1.5,
            hop_size=0.5
        )
        
        assert buffer.sample_rate == 16000
        assert buffer.window_size == 1.5
        assert buffer.hop_size == 0.5
        assert buffer.window_samples == 24000  # 1.5 * 16000
        assert buffer.hop_samples == 8000      # 0.5 * 16000
        assert len(buffer.buffer) == 0
    
    def test_add_audio(self):
        """测试添加音频数据"""
        buffer = AudioBuffer()
        
        # 添加音频数据
        audio_chunk = np.random.randn(1024)
        buffer.add_audio(audio_chunk)
        
        assert buffer.get_buffer_size() == 1024
        
        # 添加更多数据
        buffer.add_audio(audio_chunk)
        assert buffer.get_buffer_size() == 2048
    
    def test_get_windows(self):
        """测试获取滑动窗口"""
        buffer = AudioBuffer(
            sample_rate=16000,
            window_size=0.1,  # 0.1秒 = 1600样本
            hop_size=0.05     # 0.05秒 = 800样本
        )

        # 添加足够的音频数据
        audio_data = np.random.randn(3200)  # 0.2秒的数据
        buffer.add_audio(audio_data)

        windows = buffer.get_windows()

        # 应该有3个窗口: [0:1600], [800:2400], [1600:3200]
        assert len(windows) == 3
        assert len(windows[0]) == 1600
        assert len(windows[1]) == 1600
    
    def test_clear_buffer(self):
        """测试清空缓冲区"""
        buffer = AudioBuffer()
        
        # 添加数据
        audio_chunk = np.random.randn(1024)
        buffer.add_audio(audio_chunk)
        assert buffer.get_buffer_size() == 1024
        
        # 清空
        buffer.clear()
        assert buffer.get_buffer_size() == 0


class TestAudioFeatureExtractor:
    """音频特征提取器测试"""
    
    def test_feature_extractor_initialization(self):
        """测试特征提取器初始化"""
        extractor = AudioFeatureExtractor(
            sample_rate=16000,
            n_mfcc=40
        )
        
        assert extractor.sample_rate == 16000
        assert extractor.n_mfcc == 40
    
    def test_extract_mfcc(self):
        """测试MFCC特征提取"""
        extractor = AudioFeatureExtractor()
        
        # 生成测试音频（1秒）
        audio = np.random.randn(16000)
        
        mfcc = extractor.extract_mfcc(audio)
        
        assert mfcc.shape[0] == extractor.n_mfcc
        assert mfcc.shape[1] > 0  # 时间帧数应该大于0
    
    def test_extract_features_with_padding(self):
        """测试特征提取（带填充）"""
        extractor = AudioFeatureExtractor()
        
        # 生成短音频
        audio = np.random.randn(8000)  # 0.5秒
        target_length = 100
        
        features = extractor.extract_features(audio, target_length)
        
        assert features.shape == (extractor.n_mfcc, target_length)
    
    def test_extract_features_with_truncation(self):
        """测试特征提取（带截断）"""
        extractor = AudioFeatureExtractor()
        
        # 生成长音频
        audio = np.random.randn(32000)  # 2秒
        target_length = 50
        
        features = extractor.extract_features(audio, target_length)
        
        assert features.shape == (extractor.n_mfcc, target_length)


class TestWakeWordCNN:
    """唤醒词CNN模型测试"""
    
    def test_model_initialization(self):
        """测试模型初始化"""
        model = WakeWordCNN(
            input_size=40,
            num_classes=2
        )
        
        assert model.input_size == 40
        assert model.num_classes == 2
    
    def test_forward_pass(self):
        """测试前向传播"""
        model = WakeWordCNN()
        
        # 创建测试输入
        batch_size = 4
        input_size = 40
        sequence_length = 100
        
        x = torch.randn(batch_size, input_size, sequence_length)
        
        # 前向传播
        output = model(x)
        
        assert output.shape == (batch_size, 2)  # 2个类别
    
    def test_model_parameters(self):
        """测试模型参数数量"""
        model = WakeWordCNN()
        
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        assert total_params > 0
        assert trainable_params == total_params  # 所有参数都应该可训练


class TestWakeWordDetector:
    """唤醒词检测器测试"""
    
    def test_detector_initialization(self):
        """测试检测器初始化"""
        detector = WakeWordDetector(
            confidence_threshold=0.85,
            device='cpu'
        )

        assert detector.confidence_threshold == 0.85
        assert detector.device == 'cpu'
        assert not detector.is_loaded
        assert detector.model_type is None
    
    def test_load_model_without_file(self):
        """测试无模型文件的加载"""
        detector = WakeWordDetector()
        
        # 不指定模型路径应该失败
        success = detector.load_model()
        assert not success
        assert not detector.is_loaded
    
    @patch('torch.jit.load')
    @patch('pathlib.Path.exists')
    def test_load_pytorch_model(self, mock_exists, mock_jit_load):
        """测试加载PyTorch模型"""
        # 模拟TorchScript模型
        mock_model = Mock()
        mock_model.eval.return_value = None
        mock_jit_load.return_value = mock_model
        mock_exists.return_value = True

        detector = WakeWordDetector()

        with tempfile.NamedTemporaryFile(suffix='.pt', delete=False) as f:
            model_path = f.name

        try:
            success = detector.load_model(model_path)
            assert success
            assert detector.is_loaded
            assert detector.model_type == 'pytorch'
        finally:
            os.unlink(model_path)

    @patch('onnxruntime.InferenceSession')
    @patch('pathlib.Path.exists')
    def test_load_onnx_model(self, mock_exists, mock_onnx_session):
        """测试加载ONNX模型"""
        mock_exists.return_value = True
        mock_session = Mock()
        mock_session.get_providers.return_value = ['CPUExecutionProvider']
        mock_onnx_session.return_value = mock_session

        detector = WakeWordDetector()

        with tempfile.NamedTemporaryFile(suffix='.onnx', delete=False) as f:
            model_path = f.name

        try:
            success = detector.load_model(model_path)
            assert success
            assert detector.is_loaded
            assert detector.model_type == 'onnx'
        finally:
            os.unlink(model_path)

    def test_detect_without_model(self):
        """测试未加载模型时的检测"""
        detector = WakeWordDetector()
        
        audio = np.random.randn(16000)
        detected, confidence = detector.detect(audio)
        
        assert not detected
        assert confidence == 0.0
    
    def test_get_model_info(self):
        """测试获取模型信息"""
        detector = WakeWordDetector(
            confidence_threshold=0.85,
            device='cpu'
        )

        info = detector.get_model_info()

        assert 'confidence_threshold' in info
        assert 'device' in info
        assert 'is_loaded' in info
        assert 'model_type' in info
        assert 'feature_extractor' in info
        assert info['confidence_threshold'] == 0.85


class TestWakeWordService:
    """唤醒词检测服务测试"""
    
    @pytest.fixture
    async def event_bus(self):
        """事件总线fixture"""
        bus = EventBus()
        await bus.initialize()
        yield bus
        await bus.shutdown()
    
    @pytest.fixture
    def service_config(self):
        """服务配置fixture"""
        return {
            'model_path': 'test_model.onnx',
            'keyword': 'hey aibi',
            'confidence_threshold': 0.85,
            'window_size': 1.5,
            'hop_size': 0.5,
            'enabled': True,
            'sample_rate': 16000,
            'chunk_size': 1024,
            'cooldown_period': 2.0
        }
    
    @pytest.fixture
    def wake_word_service(self, service_config):
        """唤醒词服务fixture"""
        # 创建一个简单的mock事件总线
        mock_event_bus = Mock()
        mock_event_bus.subscribe = AsyncMock()
        mock_event_bus.emit = AsyncMock()

        service = WakeWordService(service_config, mock_event_bus)
        return service
    
    def test_service_initialization(self, wake_word_service, service_config):
        """测试服务初始化"""
        service = wake_word_service
        
        assert service.name == "WakeWordService"
        assert service.keyword == service_config['keyword']
        assert service.confidence_threshold == service_config['confidence_threshold']
        assert service.enabled == service_config['enabled']
    
    @pytest.mark.asyncio
    async def test_service_lifecycle(self, wake_word_service):
        """测试服务生命周期"""
        service = wake_word_service

        # 初始状态
        assert service.get_status().value == 'uninitialized'

        # 模拟模型加载成功
        with patch.object(service.detector, 'load_model', return_value=True):
            # 初始化
            success = await service.initialize()
            assert success
            assert service.get_status().value == 'ready'

            # 启动
            success = await service.start()
            assert success
            assert service.get_status().value == 'running'
            assert service.is_detecting

            # 停止
            success = await service.stop()
            assert success
            assert service.get_status().value == 'stopped'
            assert not service.is_detecting
    
    @pytest.mark.asyncio
    async def test_audio_chunk_processing(self, wake_word_service):
        """测试音频数据块处理"""
        service = wake_word_service
        
        # 模拟音频数据
        audio_data = np.random.randn(1024)
        
        # 处理音频数据块
        await service._on_audio_chunk({'audio_data': audio_data})
        
        # 检查缓冲区
        assert service.audio_buffer.get_buffer_size() == 1024
    
    @pytest.mark.asyncio
    async def test_wake_word_detection(self, wake_word_service):
        """测试唤醒词检测"""
        service = wake_word_service

        # 模拟检测结果
        with patch.object(service.detector, 'detect', return_value=(True, 0.9)):
            # 创建音频窗口
            audio_window = np.random.randn(24000)  # 1.5秒

            # 模拟事件发送
            with patch.object(service.event_bus, 'emit', new_callable=AsyncMock) as emit_mock:
                await service._process_window(audio_window)

                # 检查是否发送了唤醒事件
                emit_mock.assert_called_once()
                call_args = emit_mock.call_args
                assert call_args[0][0] == "wake_word_detected"
    
    @pytest.mark.asyncio
    async def test_cooldown_period(self, wake_word_service):
        """测试冷却期"""
        service = wake_word_service
        service.cooldown_period = 1.0  # 1秒冷却期
        
        # 模拟检测结果
        with patch.object(service.detector, 'detect', return_value=(True, 0.9)):
            audio_window = np.random.randn(24000)
            
            # 第一次检测
            await service._process_window(audio_window)
            assert service.wake_word_count == 1
            
            # 立即第二次检测（应该被冷却期阻止）
            await service._process_window(audio_window)
            assert service.wake_word_count == 1  # 没有增加
    
    def test_detection_callbacks(self, wake_word_service):
        """测试检测回调"""
        service = wake_word_service
        
        # 添加回调
        callback_called = False
        def test_callback(detected, confidence):
            nonlocal callback_called
            callback_called = True
        
        service.add_detection_callback(test_callback)
        assert len(service.detection_callbacks) == 1
        
        # 移除回调
        service.remove_detection_callback(test_callback)
        assert len(service.detection_callbacks) == 0
    
    def test_confidence_threshold_update(self, wake_word_service):
        """测试置信度阈值更新"""
        service = wake_word_service
        
        new_threshold = 0.8
        service.set_confidence_threshold(new_threshold)
        
        assert service.confidence_threshold == new_threshold
        assert service.detector.confidence_threshold == new_threshold
    
    def test_detection_stats(self, wake_word_service):
        """测试检测统计"""
        service = wake_word_service
        
        stats = service.get_detection_stats()
        
        assert 'detection_count' in stats
        assert 'wake_word_count' in stats
        assert 'detection_rate' in stats
        assert 'avg_detection_time' in stats
        assert 'buffer_size' in stats
        assert 'model_info' in stats
    
    def test_reset_stats(self, wake_word_service):
        """测试重置统计"""
        service = wake_word_service
        
        # 设置一些统计数据
        service.detection_count = 10
        service.wake_word_count = 5
        
        # 重置
        service.reset_stats()
        
        assert service.detection_count == 0
        assert service.wake_word_count == 0


if __name__ == "__main__":
    pytest.main([__file__])
