"""
音频管理器单元测试
"""

import pytest
import asyncio
import numpy as np
from unittest.mock import Mock, patch, MagicMock
import sounddevice as sd

from src.infrastructure.audio_manager import AudioManager
from src.interfaces.audio_interface import AudioConfig, AudioDevice, AudioCallbackInterface


class MockAudioCallbackInterface(AudioCallbackInterface):
    """模拟音频回调接口"""
    
    def __init__(self):
        self.audio_data_calls = []
        self.error_calls = []
    
    async def on_audio_data(self, audio_data: np.ndarray) -> None:
        self.audio_data_calls.append(audio_data.copy())
    
    async def on_audio_error(self, error: Exception) -> None:
        self.error_calls.append(error)


class TestAudioManager:
    """音频管理器测试类"""
    
    @pytest.fixture
    def audio_config(self):
        """音频配置"""
        return AudioConfig(
            sample_rate=16000,
            channels=1,
            chunk_size=1024,
            buffer_size=32000
        )
    
    @pytest.fixture
    def mock_devices(self):
        """模拟音频设备"""
        return [
            AudioDevice(id=0, name="Mock Input", channels=2, sample_rate=44100, is_input=True, is_default=True),
            AudioDevice(id=1, name="Mock Output", channels=2, sample_rate=44100, is_input=False, is_default=True),
        ]
    
    @pytest.fixture
    async def audio_manager(self, audio_config):
        """创建音频管理器实例"""
        config = {
            'sample_rate': audio_config.sample_rate,
            'channels': audio_config.channels,
            'chunk_size': audio_config.chunk_size,
            'buffer_size': audio_config.buffer_size
        }
        
        manager = AudioManager(config)
        await manager.initialize(audio_config)
        yield manager
        await manager.cleanup()
    
    @pytest.mark.asyncio
    async def test_initialization(self, audio_config):
        """测试音频管理器初始化"""
        config = {
            'sample_rate': 16000,
            'channels': 1,
            'chunk_size': 1024
        }
        
        manager = AudioManager(config)
        
        # 测试初始化
        success = await manager.initialize(audio_config)
        assert success
        assert manager.audio_config == audio_config
        assert manager.status.value == "ready"
        
        await manager.cleanup()
    
    @pytest.mark.asyncio
    @patch('sounddevice.query_devices')
    async def test_get_available_devices(self, mock_query_devices, audio_manager, mock_devices):
        """测试获取可用设备"""
        # 模拟sounddevice.query_devices返回值
        mock_query_devices.return_value = [
            {
                'name': 'Mock Input',
                'max_input_channels': 2,
                'max_output_channels': 0,
                'default_samplerate': 44100.0
            },
            {
                'name': 'Mock Output',
                'max_input_channels': 0,
                'max_output_channels': 2,
                'default_samplerate': 44100.0
            }
        ]
        
        # 模拟默认设备
        with patch.object(sd, 'default') as mock_default:
            mock_default.device = [0, 1]
            
            devices = audio_manager.get_available_devices()
            
            assert len(devices) == 2
            assert any(d.name == "Mock Input" and d.is_input for d in devices)
            assert any(d.name == "Mock Output" and not d.is_input for d in devices)
    
    @pytest.mark.asyncio
    @patch('sounddevice.InputStream')
    async def test_start_stop_recording(self, mock_input_stream, audio_manager):
        """测试开始和停止录音"""
        # 模拟输入流
        mock_stream = MagicMock()
        mock_input_stream.return_value = mock_stream
        
        # 测试开始录音
        success = await audio_manager.start_recording()
        assert success
        assert audio_manager.is_recording
        
        mock_input_stream.assert_called_once()
        mock_stream.start.assert_called_once()
        
        # 测试停止录音
        success = await audio_manager.stop_recording()
        assert success
        assert not audio_manager.is_recording
        
        mock_stream.stop.assert_called_once()
        mock_stream.close.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('sounddevice.play')
    async def test_play_audio(self, mock_play, audio_manager):
        """测试播放音频"""
        # 创建测试音频数据
        audio_data = np.random.random(1024).astype(np.float32)
        
        # 测试播放
        success = await audio_manager.play_audio(audio_data)
        assert success
        
        mock_play.assert_called_once()
        
        # 验证统计信息更新
        stats = audio_manager.get_audio_stats()
        assert stats['output_frames_processed'] == len(audio_data)
    
    @pytest.mark.asyncio
    async def test_audio_callback_registration(self, audio_manager):
        """测试音频回调注册"""
        callback_called = False
        received_data = None
        
        def test_callback(audio_data):
            nonlocal callback_called, received_data
            callback_called = True
            received_data = audio_data
        
        # 注册回调
        audio_manager.register_audio_callback(test_callback)
        
        # 模拟音频数据
        test_data = np.random.random(1024).astype(np.float32)
        
        # 触发回调
        audio_manager._trigger_audio_callbacks(test_data)
        
        # 验证回调被调用
        assert callback_called
        assert np.array_equal(received_data, test_data)
    
    @pytest.mark.asyncio
    async def test_audio_callback_interface(self, audio_manager):
        """测试音频回调接口"""
        callback_interface = MockAudioCallbackInterface()
        
        # 注册回调接口
        audio_manager.register_callback_interface(callback_interface)
        
        # 模拟音频数据
        test_data = np.random.random(1024).astype(np.float32)
        
        # 触发回调
        audio_manager._trigger_audio_callbacks(test_data)
        
        # 等待异步回调完成
        await asyncio.sleep(0.1)
        
        # 验证回调被调用
        assert len(callback_interface.audio_data_calls) == 1
        assert np.array_equal(callback_interface.audio_data_calls[0], test_data)
    
    @pytest.mark.asyncio
    async def test_audio_buffer_management(self, audio_manager):
        """测试音频缓冲区管理"""
        # 模拟音频输入回调
        test_data = np.random.random((1024, 1)).astype(np.float32)
        
        # 调用音频输入回调
        audio_manager._audio_input_callback(test_data, 1024, None, sd.CallbackFlags())
        
        # 验证缓冲区
        assert len(audio_manager.input_buffer) == 1
        
        # 获取最近的音频数据
        recent_audio = audio_manager.get_recent_audio(1000)  # 1秒
        assert len(recent_audio) > 0
        
        # 清空缓冲区
        audio_manager.clear_buffer()
        assert len(audio_manager.input_buffer) == 0
    
    @pytest.mark.asyncio
    async def test_get_recent_audio(self, audio_manager):
        """测试获取最近音频数据"""
        # 添加一些测试数据到缓冲区
        from src.infrastructure.audio_manager import AudioBuffer
        import time
        
        for i in range(5):
            buffer = AudioBuffer(
                data=np.random.random(1024).astype(np.float32),
                timestamp=time.time(),
                sample_rate=16000,
                channels=1
            )
            audio_manager.input_buffer.append(buffer)
        
        # 获取最近500ms的音频
        recent_audio = audio_manager.get_recent_audio(500)
        
        # 验证返回的音频长度
        expected_frames = int(500 * 16000 / 1000)  # 500ms at 16kHz
        assert len(recent_audio) <= expected_frames * 5  # 最多5个缓冲区的数据
    
    @pytest.mark.asyncio
    async def test_audio_stats(self, audio_manager):
        """测试音频统计信息"""
        # 获取初始统计
        stats = audio_manager.get_audio_stats()
        
        assert 'input_frames_processed' in stats
        assert 'output_frames_processed' in stats
        assert 'buffer_overruns' in stats
        assert 'buffer_underruns' in stats
        assert 'callback_errors' in stats
        assert 'buffer_size' in stats
        assert 'is_recording' in stats
        assert 'is_playing' in stats
        assert 'config' in stats
        
        # 验证配置信息
        assert stats['config']['sample_rate'] == 16000
        assert stats['config']['channels'] == 1
    
    @pytest.mark.asyncio
    async def test_health_check(self, audio_manager):
        """测试健康检查"""
        with patch.object(audio_manager, 'get_available_devices') as mock_get_devices:
            # 测试有设备的情况
            mock_get_devices.return_value = [
                AudioDevice(id=0, name="Test", channels=1, sample_rate=16000, is_input=True, is_default=True)
            ]
            
            health = await audio_manager._health_check_impl()
            assert health is True
            
            # 测试无设备的情况
            mock_get_devices.return_value = []
            
            health = await audio_manager._health_check_impl()
            assert health is False
    
    @pytest.mark.asyncio
    async def test_error_handling_in_callback(self, audio_manager):
        """测试回调中的错误处理"""
        def failing_callback(audio_data):
            raise ValueError("Test error")
        
        # 注册会失败的回调
        audio_manager.register_audio_callback(failing_callback)
        
        # 触发回调
        test_data = np.random.random(1024).astype(np.float32)
        audio_manager._trigger_audio_callbacks(test_data)
        
        # 验证错误统计增加
        stats = audio_manager.get_audio_stats()
        assert stats['callback_errors'] > 0
    
    @pytest.mark.asyncio
    async def test_device_validation(self, audio_config):
        """测试设备配置验证"""
        config = {
            'sample_rate': 16000,
            'channels': 1,
            'input_device': 999,  # 不存在的设备ID
            'output_device': 999
        }
        
        manager = AudioManager(config)
        
        with patch.object(manager, 'get_available_devices') as mock_get_devices:
            # 模拟没有指定ID的设备
            mock_get_devices.return_value = [
                AudioDevice(id=0, name="Test", channels=1, sample_rate=16000, is_input=True, is_default=True)
            ]
            
            # 初始化应该失败
            success = await manager.initialize(audio_config)
            assert not success
        
        await manager.cleanup()
    
    @pytest.mark.asyncio
    async def test_mono_conversion(self, audio_manager):
        """测试立体声到单声道转换"""
        # 模拟立体声输入数据
        stereo_data = np.random.random((1024, 2)).astype(np.float32)
        
        # 调用音频输入回调
        audio_manager._audio_input_callback(stereo_data, 1024, None, sd.CallbackFlags())
        
        # 验证缓冲区中的数据是单声道
        assert len(audio_manager.input_buffer) == 1
        buffer = audio_manager.input_buffer[0]
        assert len(buffer.data.shape) == 1  # 单声道数据应该是1维数组
    
    @pytest.mark.asyncio
    async def test_service_lifecycle(self, audio_config):
        """测试服务生命周期"""
        config = {'sample_rate': 16000, 'channels': 1}
        manager = AudioManager(config)
        
        # 初始化
        success = await manager.initialize(audio_config)
        assert success
        assert manager.get_status().value == "ready"
        
        # 启动
        success = await manager.start()
        assert success
        assert manager.get_status().value == "running"
        
        # 停止
        success = await manager.stop()
        assert success
        assert manager.get_status().value == "stopped"
        
        await manager.cleanup()


if __name__ == "__main__":
    pytest.main([__file__])
