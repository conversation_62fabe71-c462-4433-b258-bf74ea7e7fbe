#!/usr/bin/env python3
"""
快速集成测试
验证完整对话系统的核心功能
"""

import asyncio
import sys
import time
import uuid
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.services.event_bus import EventBus
from src.services.asr_service import ASRService
from src.services.tts import CosyVoiceTTSService
from src.infrastructure.audio_manager import AudioManager
from src.interfaces.audio_interface import AudioConfig


async def test_quick_integration():
    """快速集成测试"""
    print("🚀 开始快速集成测试...")
    
    # 初始化事件总线
    event_bus = EventBus()
    await event_bus.initialize()
    
    # 事件记录
    events = []
    
    async def log_event(event_name, data):
        events.append((event_name, data, time.time()))
        print(f"📨 事件: {event_name}")
    
    # 订阅关键事件
    await event_bus.subscribe("wake_word_detected", lambda data: log_event("wake_word_detected", data))
    await event_bus.subscribe("speech_recognized", lambda data: log_event("speech_recognized", data))
    await event_bus.subscribe("text_to_synthesize", lambda data: log_event("text_to_synthesize", data))
    
    # 初始化音频管理器
    audio_config = AudioConfig(
        sample_rate=16000,
        channels=1,
        chunk_size=1024,
        input_device_id=None,
        output_device_id=None
    )
    
    audio_manager = AudioManager({
        'sample_rate': 16000,
        'channels': 1,
        'chunk_size': 1024,
        'input_device_id': None,
        'output_device_id': None
    })
    
    if not await audio_manager.initialize(audio_config):
        print("❌ 音频管理器初始化失败")
        return False
    
    # 初始化ASR服务
    asr_config = {
        'enabled': True,
        'model_name': 'models/funasr/iic/SenseVoiceSmall',
        'vad_model': 'models/funasr/fsmn-vad',
        'punc_model': 'models/funasr/ct-punc',
        'device': 'auto',
        'sample_rate': 16000,
        'use_smart_vad': True,
        'waiting_timeout': 5.0,
        'speech_end_timeout': 1.0
    }
    
    asr_service = ASRService(asr_config, event_bus)
    # ASR服务不需要设置音频管理器，它使用自己的音频缓冲区

    if not await asr_service.initialize():
        print("❌ ASR服务初始化失败")
        return False

    if not await asr_service.start():
        print("❌ ASR服务启动失败")
        return False

    print("✅ ASR服务启动成功")
    
    # 初始化TTS服务
    tts_config = {
        "model_name": "CosyVoice-300M",
        "device": "cpu",
        "sample_rate": 22050,
        "streaming": True,
        "enable_continuous_playback": True
    }
    
    tts_service = CosyVoiceTTSService(tts_config, event_bus)
    # 设置音频管理器
    if hasattr(tts_service, 'set_audio_manager'):
        tts_service.set_audio_manager(audio_manager)

    if not await tts_service.initialize("CosyVoice-300M", tts_config):
        print("❌ TTS服务初始化失败")
        return False

    if not await tts_service.start():
        print("❌ TTS服务启动失败")
        return False

    print("✅ TTS服务启动成功")
    
    # 测试完整流程
    print("\n🧪 测试完整对话流程...")
    
    # 1. 模拟唤醒词检测
    print("1️⃣ 模拟唤醒词检测...")
    await event_bus.emit("wake_word_detected", {
        "confidence": 0.95,
        "timestamp": time.time()
    })
    await asyncio.sleep(2.0)
    
    # 2. 模拟语音识别
    print("2️⃣ 模拟语音识别...")
    await event_bus.emit("speech_recognized", {
        "text": "你好",
        "confidence": 0.90,
        "timestamp": time.time()
    })
    await asyncio.sleep(1.0)
    
    # 3. 模拟TTS合成
    print("3️⃣ 模拟TTS合成...")
    await event_bus.emit("text_to_synthesize", {
        "text": "你好！我是艾比，很高兴为您服务。",
        "streaming": True,
        "session_id": str(uuid.uuid4()),
        "timestamp": time.time()
    })
    await asyncio.sleep(5.0)
    
    # 检查结果
    wake_events = [e for e in events if e[0] == "wake_word_detected"]
    speech_events = [e for e in events if e[0] == "speech_recognized"]
    tts_events = [e for e in events if e[0] == "text_to_synthesize"]
    
    success = len(wake_events) > 0 and len(speech_events) > 0 and len(tts_events) > 0
    
    print(f"\n📊 测试结果:")
    print(f"   唤醒事件: {len(wake_events)}")
    print(f"   识别事件: {len(speech_events)}")
    print(f"   TTS事件: {len(tts_events)}")
    print(f"   总事件数: {len(events)}")
    
    # 清理
    await tts_service.stop()
    await asr_service.stop()
    await audio_manager.cleanup()
    await event_bus.shutdown()
    
    print(f"\n🎯 快速集成测试: {'✅ 成功' if success else '❌ 失败'}")
    return success


if __name__ == "__main__":
    success = asyncio.run(test_quick_integration())
    sys.exit(0 if success else 1)
