#!/usr/bin/env python3
"""
pytest配置文件和共享fixtures
- 提供单元测试(unit/)和集成测试(test_main.py)通用的测试工具和数据
- 定义pytest标记和自动标记规则
- 提供模拟服务、事件总线等共享fixtures
"""

import asyncio
import pytest
import pytest_asyncio
import sys
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.services.event_bus import EventBus
from src.core.state_machine import StateMachine, SystemState


@pytest.fixture
def event_loop():
    """事件循环fixture"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture
async def event_bus():
    """事件总线fixture"""
    config = {
        "max_queue_size": 1000,
        "worker_count": 2,
        "batch_size": 10,
        "batch_timeout": 0.1
    }
    bus = EventBus(config)
    await bus.initialize()
    try:
        yield bus
    finally:
        await bus.shutdown()


@pytest_asyncio.fixture
async def mock_asr_service():
    """模拟ASR服务fixture"""
    service = MagicMock()
    service.initialize = AsyncMock(return_value=True)
    service.start = AsyncMock(return_value=True)
    service.stop = AsyncMock(return_value=True)
    service.start_speech_collection = AsyncMock()
    service.stop_speech_collection = AsyncMock()
    service.is_enabled = True
    service.is_running = True
    return service


@pytest_asyncio.fixture
async def state_machine(event_bus, mock_asr_service):
    """状态机fixture"""
    config = {
        "conversation_timeout": 15.0,
        "speech_timeout": 10.0,
        "error_recovery_timeout": 5.0
    }
    
    sm = StateMachine(config, event_bus, mock_asr_service)
    await sm.initialize()
    await sm.start()
    
    try:
        yield sm
    finally:
        await sm.stop()


@pytest.fixture
def sample_audio_config():
    """音频配置样本"""
    return {
        "sample_rate": 16000,
        "channels": 1,
        "chunk_size": 1024,
        "format": "float32"
    }


@pytest.fixture
def sample_llm_config():
    """LLM配置样本"""
    return {
        "api_url": "http://test.example.com/v1",
        "api_key": "test_key",
        "model": "test_model",
        "max_tokens": 1000,
        "temperature": 0.7
    }


@pytest.fixture
def sample_tts_config():
    """TTS配置样本"""
    return {
        "model_name": "test_model",
        "device": "cpu",
        "sample_rate": 16000,
        "streaming": True
    }


@pytest.fixture
def mock_audio_data():
    """模拟音频数据"""
    import numpy as np
    # 生成1秒的16kHz音频数据
    return np.random.random(16000).astype(np.float32)


@pytest.fixture
def mock_conversation_context():
    """模拟对话上下文"""
    return {
        "session_id": "test_session",
        "user_id": "test_user",
        "conversation_id": "test_conversation",
        "turn_count": 1,
        "context_length": 0,
        "last_activity": 1234567890.0
    }


# 测试标记定义
pytest_plugins = []

def pytest_configure(config):
    """pytest配置"""
    config.addinivalue_line(
        "markers", "unit: 单元测试标记"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试标记"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试标记"
    )
    config.addinivalue_line(
        "markers", "requires_audio: 需要音频设备的测试"
    )
    config.addinivalue_line(
        "markers", "requires_network: 需要网络连接的测试"
    )


def pytest_collection_modifyitems(config, items):
    """修改测试收集项"""
    for item in items:
        # 根据文件路径自动添加标记
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "test_main.py" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
