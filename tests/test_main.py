#!/usr/bin/env python3
"""
语音助手手动集成测试
实时监听唤醒词，检测到后进行语音识别
需要真实音频设备，用于演示和验证

与tests/test_end_to_end.py互补：
- 本文件：手动集成测试，演示和验证用
- tests/test_end_to_end.py：自动化测试，CI/CD友好

使用方法:
    conda activate aibi
    python scripts/test_main.py

功能:
    - 自动检测最佳麦克风设备
    - 实时监听唤醒词 "hey aibi"
    - 检测到唤醒词后进行语音识别
    - 静默运行，只在有事件时输出信息
"""

import asyncio
import time
import sys
import signal
from datetime import datetime
from pathlib import Path

# 检查依赖
try:
    import numpy as np
    import sounddevice as sd
    import logging
except ImportError as e:
    print(f"❌ 缺少依赖模块: {e}")
    print("请先激活conda环境: conda activate aibi")
    sys.exit(1)

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.services.wake_word import WakeWordService
from src.services.asr_service import ASRService
from src.services.tts import CosyVoiceTTSService
from src.services.llm import DifyLLMService
from src.services.event_bus import EventBus
from src.core.state_machine import StateMachine, SystemState
from src.services.conversation_service import ConversationService
from src.core.unified_audio_capture import AudioDeviceManager, UnifiedAudioCapture
from src.infrastructure.audio_manager import AudioManager
from src.interfaces.audio_interface import AudioConfig


class VoiceAssistantTest:
    """语音助手集成测试"""
    
    def __init__(self):
        # 核心组件
        self.event_bus = None
        self.wake_word_service = None

        self.asr_service = None
        self.llm_service = None
        self.state_machine = None
        self.conversation_service = None
        
        # 音频相关
        self.audio_device_manager = AudioDeviceManager()
        self.audio_stream = None
        self.is_running = False
        self.sample_rate = 16000
        self.chunk_size = 1024
        self.channels = 1
        self.device_id = None
        
        # 状态管理
        self.is_listening_for_speech = False
        self.speech_timeout = 10.0  # 10秒语音输入超时
        self.speech_timeout_task = None
        self.main_loop = None  # 主事件循环引用
        
        # 设置简洁的日志
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志 - 只显示关键信息"""
        logging.basicConfig(
            level=logging.INFO,  # 改为INFO级别以显示音频播放日志
            format='%(message)s'
        )
        # 设置特定模块的日志级别
        logging.getLogger('src.services.event_bus').setLevel(logging.WARNING)
        logging.getLogger('src.services.wake_word').setLevel(logging.WARNING)
        logging.getLogger('src.services.asr_service').setLevel(logging.WARNING)
        logging.getLogger('src.services.tts').setLevel(logging.INFO)  # 显示TTS日志
        logging.getLogger('src.infrastructure.audio_manager').setLevel(logging.INFO)  # 显示音频管理器日志
        logging.getLogger('funasr').setLevel(logging.ERROR)
    
    def get_timestamp(self):
        """获取当前时间戳"""
        return datetime.now().strftime("%H:%M:%S")
    
    async def initialize_services(self):
        """初始化所有服务"""
        try:
            # 1. 初始化事件总线
            self.event_bus = EventBus()
            await self.event_bus.initialize()

            # 2. 初始化唤醒词服务
            wake_word_config = {
                'enabled': True,
                'model_path': 'models/wake_word/hey_aibi.onnx',
                'keyword': 'hey aibi',
                'confidence_threshold': 0.85,  # 使用模型推荐阈值
                'sample_rate': self.sample_rate,
                'chunk_size': self.chunk_size,
                'window_size': 1.5,
                'hop_size': 0.5,
                'cooldown_period': 2.0
            }
            
            self.wake_word_service = WakeWordService(wake_word_config, self.event_bus)
            if not await self.wake_word_service.initialize():
                raise RuntimeError("唤醒词服务初始化失败")
            if not await self.wake_word_service.start():
                raise RuntimeError("唤醒词服务启动失败")

            # 3. 初始化ASR服务（使用FunASR完整pipeline + 智能VAD）
            asr_config = {
                'enabled': True,
                'model_name': 'models/funasr/iic/SenseVoiceSmall',  # 使用本地模型路径
                'vad_model': 'models/funasr/fsmn-vad',              # 本地VAD模型路径
                'punc_model': 'models/funasr/ct-punc',              # 本地标点恢复模型路径
                'device': 'auto',
                'sample_rate': self.sample_rate,
                'max_audio_duration': 30.0,
                'min_audio_duration': 1.0,
                'auto_recognition': False,
                # 智能VAD配置
                'use_smart_vad': True,          # 启用智能VAD
                'waiting_timeout': 10.0,        # 唤醒后等待语音10秒超时
                'speech_end_timeout': 1.2       # 语音结束后1.2秒静音确认
            }

            self.asr_service = ASRService(asr_config, self.event_bus)
            if not await self.asr_service.initialize():
                raise RuntimeError("语音识别服务初始化失败")
            if not await self.asr_service.start():
                raise RuntimeError("语音识别服务启动失败")

            # 4. 初始化LLM服务
            llm_config = {
                "api_url": "http://11.20.60.13/v1",
                "api_key": "app-NIfC6KMjycOJyp2p85LGzR0u",
                "timeout": 30,
                "retry_attempts": 3
            }

            self.llm_service = DifyLLMService()
            if not await self.llm_service.initialize(llm_config):
                raise RuntimeError("LLM服务初始化失败")

            # 5. 初始化音频管理器（用于音频播放）
            audio_manager_config = {
                'sample_rate': 16000,
                'channels': 1,
                'chunk_size': 1024,
                'input_device_id': None,  # 将在后面设置
                'output_device_id': None  # 使用默认输出设备
            }

            audio_config = AudioConfig(
                sample_rate=16000,
                channels=1,
                chunk_size=1024,
                input_device_id=None,
                output_device_id=None
            )

            self.audio_manager = AudioManager(audio_manager_config)
            if not await self.audio_manager.initialize(audio_config):
                print("⚠️  音频管理器初始化失败")
                self.audio_manager = None
            else:
                print("✅ 音频管理器初始化成功")

            # 6. 初始化TTS服务
            tts_config = {
                "model_name": "CosyVoice-300M",
                "device": "cpu",
                "sample_rate": 22050,
                "streaming": True,
                "voice": "default",  # 使用默认voice，让系统自动选择
                "speed": 1.0,
                "volume": 1.0
            }

            self.tts_service = CosyVoiceTTSService(tts_config, self.event_bus)
            # 将音频管理器传递给TTS服务
            if self.audio_manager:
                self.tts_service.set_audio_manager(self.audio_manager)

            model_name = tts_config["model_name"]

            print("🔊 正在初始化TTS服务...")
            if not await self.tts_service.initialize(model_name, tts_config):
                print("⚠️  TTS服务初始化失败，将跳过语音合成功能")
                self.tts_service = None
            else:
                if not await self.tts_service.start():
                    print("⚠️  TTS服务启动失败，将跳过语音合成功能")
                    self.tts_service = None
                else:
                    print("✅ TTS服务初始化成功")

            # 7. 初始化状态机（传入ASR服务引用）
            state_machine_config = {
                "conversation_timeout": 15.0,
                "speech_timeout": 30.0,  # 备用超时，主要依赖静音检测
                "error_recovery_timeout": 5.0
            }
            self.state_machine = StateMachine(state_machine_config, self.event_bus, self.asr_service)
            if not await self.state_machine.initialize():
                raise RuntimeError("状态机初始化失败")
            if not await self.state_machine.start():
                raise RuntimeError("状态机启动失败")

            # 8. 初始化对话管理服务
            conversation_config = {
                "max_context_length": 10,
                "session_timeout": 300.0,
                "max_turn_count": 50,
                "enable_interruption": True,
                "interruption_threshold": 0.5
            }
            self.conversation_service = ConversationService(conversation_config, self.event_bus, self.llm_service)
            if not await self.conversation_service.initialize():
                raise RuntimeError("对话管理服务初始化失败")
            if not await self.conversation_service.start():
                raise RuntimeError("对话管理服务启动失败")

            # 9. 订阅事件（现在由状态机和对话管理服务管理）
            # await self.event_bus.subscribe("wake_word_detected", self.on_wake_word_detected)
            await self.event_bus.subscribe("speech_recognized", self.on_speech_recognized)
            await self.event_bus.subscribe("speech_collection_complete", self.on_speech_collection_complete)
            await self.event_bus.subscribe("state_changed", self.on_state_changed)
            
            return True
            
        except Exception as e:
            print(f"❌ 服务初始化失败: {e}")
            return False
    
    def setup_microphone(self):
        """设置麦克风设备"""
        try:
            print("🎤 设置音频设备...")

            # 使用音频设备管理器
            selected_device = self.audio_device_manager.setup_audio_device()

            if selected_device:
                self.device_id = selected_device.device_id
                print(f"✅ 选择音频设备: [{selected_device.device_id}] {selected_device.name}")
                return selected_device, selected_device.name
            else:
                print("❌ 未能设置音频设备")
                return None, None

        except Exception as e:
            print(f"❌ 设置音频设备失败: {e}")
            return None, None
    
    def audio_callback(self, indata, frames, time, status):
        """音频回调函数"""
        if status:
            print(f"音频状态警告: {status}")
            return

        try:
            if self.is_running and self.event_bus and self.main_loop:
                # 减少调试计数器的开销
                if hasattr(self, '_debug_counter'):
                    self._debug_counter += 1
                else:
                    self._debug_counter = 1

                audio_data = indata[:, 0] if self.channels == 1 else indata.flatten()

                # 使用线程安全的方式将事件发布任务调度到主事件循环
                # 不等待结果，避免阻塞音频线程
                try:
                    future = asyncio.run_coroutine_threadsafe(
                        self.event_bus.emit("audio_chunk", {"audio_data": audio_data}),
                        self.main_loop
                    )
                    # 立即释放future引用，减少内存占用
                    del future
                except RuntimeError:
                    # 事件循环可能已关闭，静默忽略
                    pass

        except Exception as e:
            # 记录错误但不影响音频流
            print(f"音频回调错误: {e}")
            pass
    
    def start_audio_capture(self, device_info):
        """启动音频采集"""
        try:
            print(f"🎤 启动PipeWire音频采集: {device_info.name}")

            # 使用统一音频捕获
            self.audio_stream = UnifiedAudioCapture(
                device_info=device_info,
                samplerate=self.sample_rate,
                channels=self.channels,
                blocksize=self.chunk_size,
                callback=self.audio_callback,
                dtype=np.float32
            )

            self.audio_stream.start()

            # 获取实际使用的捕获状态
            status = self.audio_stream.get_status()
            print(f"✅ PipeWire音频采集启动成功，设备: {status['device']['description']}")
            return True

        except Exception as e:
            print(f"❌ 启动音频采集失败: {e}")
            return False
    
    def stop_audio_capture(self):
        """停止音频采集"""
        if self.audio_stream:
            self.audio_stream.stop()
            self.audio_stream.close()
            self.audio_stream = None
    
    async def on_wake_word_detected(self, event_data):
        """处理唤醒词检测事件"""
        try:
            keyword = event_data.get('keyword', '')
            confidence = event_data.get('confidence', 0.0)

            print(f"🎉 [{self.get_timestamp()}] 检测到唤醒词 '{keyword}'! (置信度: {confidence:.2f})")
            print("🎤 请说出您的指令...")

            # 开始等待语音输入
            self.is_listening_for_speech = True

            # 启动超时任务
            if self.speech_timeout_task:
                self.speech_timeout_task.cancel()
            self.speech_timeout_task = asyncio.create_task(self.speech_timeout_handler())

        except Exception as e:
            print(f"❌ 处理唤醒词事件失败: {e}")
    
    async def on_speech_recognized(self, event_data):
        """处理语音识别事件"""
        try:
            if not self.is_listening_for_speech:
                return

            # 取消超时任务
            if self.speech_timeout_task:
                self.speech_timeout_task.cancel()
                self.speech_timeout_task = None

            text = event_data.get('text', '').strip()
            confidence = event_data.get('confidence', 0.0)
            error = event_data.get('error')

            if error:
                print(f"❌ [{self.get_timestamp()}] 识别失败: {error}")
            elif text:
                # 清理FunASR的特殊标记
                clean_text = self.clean_funasr_text(text)
                if clean_text:
                    print(f"🎯 [{self.get_timestamp()}] 识别结果: \"{clean_text}\" (置信度: {confidence:.2f})")
                else:
                    print(f"❌ [{self.get_timestamp()}] 识别失败: 无有效语音内容")
            else:
                print(f"❌ [{self.get_timestamp()}] 识别失败: 无语音输入")

            print("👂 继续监听唤醒词...")
            self.is_listening_for_speech = False

        except Exception as e:
            print(f"❌ 处理语音识别事件失败: {e}")
            self.is_listening_for_speech = False
    
    def clean_funasr_text(self, text):
        """清理FunASR输出的特殊标记"""
        import re
        # 移除语言标记、情感标记等
        text = re.sub(r'<\|[^|]*\|>', '', text)
        # 移除多余的标点和空格
        text = re.sub(r'[。，、]+$', '', text.strip())
        return text.strip()

    async def on_speech_collection_complete(self, event_data):
        """处理语音收集完成事件"""
        try:
            print(f"📝 [{self.get_timestamp()}] 语音收集完成，开始转写...")
        except Exception as e:
            print(f"❌ 处理语音收集完成事件失败: {e}")

    async def on_state_changed(self, event_data):
        """处理状态变化事件"""
        try:
            current_state = event_data.get('current_state')
            previous_state = event_data.get('previous_state')
            if current_state and previous_state:
                print(f"🔄 [{self.get_timestamp()}] 状态变化: {previous_state} → {current_state}")
        except Exception as e:
            print(f"❌ 处理状态变化事件失败: {e}")


    
    async def speech_timeout_handler(self):
        """语音输入超时处理"""
        try:
            await asyncio.sleep(self.speech_timeout)
            if self.is_listening_for_speech:
                print(f"⏰ [{self.get_timestamp()}] 语音输入超时")
                print("👂 继续监听唤醒词...")
                self.is_listening_for_speech = False
        except asyncio.CancelledError:
            pass

    async def run(self):
        """主运行循环"""
        print("🚀 正在启动语音助手...")

        # 保存主事件循环引用
        self.main_loop = asyncio.get_running_loop()

        # 1. 初始化服务
        if not await self.initialize_services():
            return False

        # 2. 设置麦克风设备
        device_info, device_name = self.setup_microphone()
        if device_info is None:
            print("❌ 未能设置音频设备")
            return False

        # 3. 启动音频采集
        if not self.start_audio_capture(device_info):
            return False

        # 4. 开始运行
        self.is_running = True
        print(f"🎤 语音助手已启动，正在监听唤醒词 \"hey aibi\"...")
        print(f"📱 使用设备: {device_name}")
        print("⏹️  按 Ctrl+C 退出")
        print("-" * 50)

        try:
            # 静默运行，等待事件
            while self.is_running:
                await asyncio.sleep(1)

        except KeyboardInterrupt:
            print(f"\n⏹️  [{self.get_timestamp()}] 收到退出信号")
        except Exception as e:
            print(f"\n❌ [{self.get_timestamp()}] 运行异常: {e}")
        finally:
            await self.cleanup()

        return True

    async def cleanup(self):
        """清理资源"""
        print("🧹 正在清理资源...")

        self.is_running = False

        # 停止音频采集
        self.stop_audio_capture()

        # 取消超时任务
        if self.speech_timeout_task:
            self.speech_timeout_task.cancel()

        # 停止服务
        if self.conversation_service:
            await self.conversation_service.stop()
        if self.state_machine:
            await self.state_machine.stop()
        if self.tts_service:
            await self.tts_service.stop()
        if self.audio_manager:
            await self.audio_manager.cleanup()
        if self.llm_service:
            await self.llm_service.cleanup()
        if self.asr_service:
            await self.asr_service.stop()

        if self.wake_word_service:
            await self.wake_word_service.stop()
        if self.event_bus:
            await self.event_bus.shutdown()

        print("✅ 资源清理完成")


def setup_signal_handlers(test_instance):
    """设置信号处理器"""
    def signal_handler(sig, frame):
        test_instance.is_running = False

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='语音助手集成测试')
    parser.add_argument('--select-device', action='store_true',
                       help='重新选择音频设备')
    parser.add_argument('--list-devices', action='store_true',
                       help='列出所有音频设备')


    args = parser.parse_args()

    # 如果只是列出设备
    if args.list_devices:
        manager = AudioDeviceManager()
        devices = manager.get_available_input_devices()
        print("\n🎤 所有音频输入设备:")
        print("=" * 60)
        for device in devices:
            print(device)
        print("=" * 60)
        return 0

    # 创建测试实例
    test = VoiceAssistantTest()

    # 如果需要重新选择设备
    if args.select_device:
        device_info, device_name = test.setup_microphone()
        if device_info is None:
            print("❌ 设备选择失败")
            return 1
        print(f"✅ 设备选择完成: {device_name}")
        return 0



    # 设置信号处理器
    setup_signal_handlers(test)

    try:
        # 运行测试
        success = await test.run()
        return 0 if success else 1

    except Exception as e:
        print(f"❌ 程序异常: {e}")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 再见!")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
