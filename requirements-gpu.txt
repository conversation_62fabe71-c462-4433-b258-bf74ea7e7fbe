# 智能语音助手系统GPU环境依赖包
# 基于三层架构设计的端到端语音交互系统
# 针对Ubuntu系统 + 24GB GPU显存环境优化
# CUDA 12.1 + PyTorch 2.3.1 + RTX 4090 D

# ===== 核心框架 =====
asyncio-mqtt>=0.13.0
pydantic>=2.0.0
pyyaml>=6.0
python-dotenv>=1.0.0

# ===== GPU加速深度学习框架 =====
# PyTorch GPU版本 (CUDA 12.1)
torch==2.3.1+cu121
torchaudio==2.3.1+cu121
torchvision==0.18.1+cu121
# PyTorch Lightning for training
pytorch-lightning>=2.5.0
# 额外的PyTorch工具
torch-complex>=0.4.4
torchmetrics>=1.7.0

# ===== NVIDIA CUDA支持库 =====
# CUDA运行时库 (已在环境中安装，这里标记版本)
# nvidia-cuda-runtime-cu12==12.9.79
# nvidia-cublas-cu12==12.1.3.1
# nvidia-cudnn-cu12==8.9.2.26
# nvidia-cufft-cu12==11.0.2.54
# nvidia-curand-cu12==10.3.2.106
# nvidia-cusolver-cu12==11.4.5.107
# nvidia-cusparse-cu12==**********
# nvidia-nccl-cu12==2.20.5

# ===== 语音处理核心库 =====
# FunASR - 语音识别和VAD (已安装 v1.2.6)
funasr>=1.2.6
# 音频处理
soundfile>=0.12.1
librosa>=0.10.2
pyaudio>=0.2.11
# 跨平台音频支持
sounddevice>=0.4.6
# WebRTC VAD
webrtcvad>=2.0.10
# 音频读取
audioread>=3.0.1

# ===== 机器学习推理 =====
# ONNX Runtime GPU版本 (替换CPU版本)
onnxruntime-gpu>=1.16.0
# 模型量化工具
onnx>=1.18.0
# NumPy数组处理
numpy>=1.26.4
# 科学计算
scipy>=1.11.0

# ===== 语音合成 =====
# CosyVoice相关依赖 (已有torch/torchaudio)
# 文本处理
jieba>=0.42.1
# 日语处理
jaconv>=0.4.0
jamo>=0.4.1
# 语音合成工具
edge-tts>=7.0.0
pyworld>=0.3.5

# ===== Transformers和模型库 =====
# HuggingFace Transformers
transformers>=4.53.0
# HuggingFace Hub
huggingface-hub>=0.33.0
# Tokenizers
tokenizers>=0.21.0
# ModelScope
modelscope>=1.20.0
# Diffusers for advanced models
diffusers>=0.29.0

# ===== 大语言模型集成 =====
# HTTP客户端
httpx>=0.28.0
aiohttp>=3.12.0
# Dify API客户端
requests>=2.31.0
# 异步支持
anyio>=4.9.0
httpcore>=1.0.9

# ===== 性能优化库 =====
# 深度学习加速
einops>=0.8.0
# 数值计算优化
numba>=0.61.0
# GPU监控
gputil>=1.4.0
# 内存管理
psutil>=5.9.0

# ===== 开发和测试 =====
# 测试框架
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
# 代码质量
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0
# 性能分析
memory-profiler>=0.61.0

# ===== 日志和监控 =====
# 结构化日志
structlog>=23.1.0
# 性能监控
prometheus-client>=0.17.0
# 彩色日志
coloredlogs>=15.0.1

# ===== 配置管理 =====
# 配置验证
cerberus>=1.3.4
# 环境变量管理
python-decouple>=3.8
# 文件监控
watchdog>=3.0.0
# Hydra配置管理
hydra-core>=1.3.2
omegaconf>=2.3.0

# ===== 工具库 =====
# 数据类
dataclasses-json>=0.6.0
# 类型提示
typing-extensions>=4.14.0
# 路径处理
pathlib2>=2.3.7
# 进度条
tqdm>=4.67.0
# 文件下载
gdown>=5.2.0
wget>=3.2

# ===== 音频信号处理 =====
# 音频特征提取
python-speech-features>=0.6
# 音频增强
noisereduce>=3.0.0
# 音频格式转换
pydub>=0.25.1

# ===== 可选高级功能 =====
# DeepSpeed for large model training (已安装)
deepspeed>=0.17.0
# Lightning utilities
lightning-utilities>=0.14.0
# 机器学习工具
scikit-learn>=1.3.0
joblib>=1.5.0

# ===== 开发工具 =====
# Jupyter支持
jupyter>=1.0.0
ipython>=8.0.0
# 文档生成
sphinx>=7.0.0
# 代码格式化
autopep8>=2.0.0
# 依赖管理
pip-tools>=7.0.0

# ===== 网络和安全 =====
# 加密支持
cryptography>=45.0.0
# SSL证书
certifi>=2025.7.14
# URL解析
urllib3>=2.5.0
# 字符编码
charset-normalizer>=3.4.0

# ===== 数据处理 =====
# 数据分析
pandas>=2.0.0
# 可视化
matplotlib>=3.10.0
seaborn>=0.12.0
# 图像处理
pillow>=10.0.0

# ===== 特殊说明 =====
# 1. PyTorch GPU版本需要从官方源安装：
#    pip install torch==2.3.1+cu121 torchaudio==2.3.1+cu121 --index-url https://download.pytorch.org/whl/cu121
# 2. NVIDIA CUDA库已通过conda/pip自动安装
# 3. 确保CUDA 12.1驱动程序已正确安装
# 4. 建议GPU显存至少8GB，当前24GB显存充足
# 5. 某些包可能需要系统级依赖，请参考各包的安装文档
