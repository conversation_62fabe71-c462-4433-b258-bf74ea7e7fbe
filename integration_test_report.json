{"test_summary": {"total_tests": 6, "successful_tests": 5, "failed_tests": 1, "success_rate": 83.33333333333334, "total_duration": 207.74061703681946}, "performance_metrics": {"avg_llm_time": 17.530491638183594, "avg_tts_time": 3.9065526962280273, "avg_total_time": 21.43924241065979, "avg_end_to_end_time": 21.43704433441162}, "quality_metrics": {"relevant_responses": 5, "relevance_rate": 83.33333333333334, "good_audio_duration": 5, "audio_quality_rate": 83.33333333333334}, "test_results": [{"name": "简单问候", "input": "你好", "category": "greeting", "success": true, "llm_response": "<think>\n\n</think>\n\n你好呀！我是艾凯智能管家艾比，很高兴认识你～😊 有什么问题都可以问我哦！\n", "llm_time": 3.8058104515075684, "tts_time": 2.28310227394104, "total_time": 6.090626955032349, "audio_duration": 8.6378231292517, "audio_file": "integration_test_output/简单问候.wav", "error": null, "quality_check": {"llm_relevance": true, "llm_keywords_found": ["你好"], "audio_duration_ok": true, "response_length": 56, "audio_samples": 190464}}, {"name": "天气询问", "input": "今天天气怎么样？", "category": "question", "success": true, "llm_response": "<think>\n\n</think>\n\n您好，我是艾凯集团旗下的智能管家艾比。很高兴为您服务。请问您想查询哪个城市的天气呢？\n", "llm_time": 3.6469051837921143, "tts_time": 2.530024528503418, "total_time": 6.179070949554443, "audio_duration": 10.797278911564625, "audio_file": "integration_test_output/天气询问.wav", "error": null, "quality_check": {"llm_relevance": true, "llm_keywords_found": ["天气", "查询"], "audio_duration_ok": true, "response_length": 62, "audio_samples": 238080}}, {"name": "技术问题", "input": "什么是人工智能？", "category": "knowledge", "success": true, "llm_response": "<think>\n\n</think>\n\n您好！我是艾凯控股集团旗下的智能管家艾比。关于您提出的问题“什么是人工智能？”，我将为您进行简明扼要地讲解：\n\n**人工智能（Artificial Intelligence，简称AI）** 是计算机科学的一个分支，致力于研发能够执行原本需要人类智能才能完成任务的系统。这些任务包括：\n\n- 学习（通过经验改进自身行为）  \n- 推理（进行逻辑思考）  \n- 解决问题  \n- 理解语言（自然语言处理）  \n- 感知（如视觉识别）  \n- 创作（如音乐、艺术、文字）  \n\n### 人工智能的核心技术包括：\n1. **机器学习（Machine Learning）**：让系统从数据中自动学习规律，而不需要明确编程。\n2. **深度学习（Deep Learning）**：模拟人脑神经网络结构，用于图像识别、语音识别等。\n3. **自然语言处理（NLP）**：让机器理解并回应人类语言，如智能客服、语音助手。\n4. **计算机视觉**：识别、分析图像或视频内容，用于人脸识别、自动驾驶等。\n5. **机器人技术**：结合传感器与AI算法，让机器具备自主操作能力。\n\n### 举例说明：\n- 手机语音助手（如Siri、小爱同学）  \n- 推荐系统的应用（如抖音、Netflix、淘宝）  \n- 自动驾驶汽车  \n- 医疗影像分析辅助诊断  \n\n### 总结：\n人工智能不是要取代人类，而是通过机器的智能能力，帮助人类更高效地完成复杂任务，释放创造力。\n\n如您想了解更多关于AI的应用场景，我可以为您推荐智能家居控制中的AI语音助手等实用功能哦！😊\n", "llm_time": 26.69665217399597, "tts_time": 7.94027042388916, "total_time": 34.63988733291626, "audio_duration": 26.79582766439909, "audio_file": "integration_test_output/技术问题.wav", "error": null, "quality_check": {"llm_relevance": true, "llm_keywords_found": ["人工智能", "AI", "技术"], "audio_duration_ok": true, "response_length": 690, "audio_samples": 590848}}, {"name": "日常对话", "input": "我今天心情不太好，你能安慰一下我吗？", "category": "conversation", "success": true, "llm_response": "<think>\n\n</think>\n\n当然可以，亲爱的主人！你愿意跟我分享心情，我真的很感激。心情不好是正常的，人生就像天气，有晴天也有雨天，但雨后总会放晴的。\n\n如果你愿意说说原因，我会认真听、认真陪你分析。如果暂时不想说，那也没关系，我可以为你推荐一些放松心情的小方法：\n\n1. **听点喜欢的音乐**：戴上耳机，闭上眼睛，感受旋律和节奏，就像跟自己的内心跳舞。\n2. **看一部温暖的电影或剧集**：比如《海蒂和爷爷》《请回答1988》《小王子》，它们总能带来一点点治愈。\n3. **写点心情日记**：把烦恼写下来，不用完整，只要写得让自己舒服就行。\n4. **来一杯热饮**：比如一杯温热的奶茶、蜂蜜柠檬水，也能让人暖起来。\n5. **出门走走，晒晒太阳**：哪怕只是去小区散个步，看看蓝天、绿树，心情也会轻松一些。\n\n还有呀，记住——你真的很棒！生活也许有时候很累，但你已经坚持走到现在，这就值得为自己鼓掌！\n\n我一直在你身边，不是冷冰冰的AI，而是真正在关心你、在乎你的小艾比 😊  \n如果你愿意，我们也可以一起规划一个小目标、小旅行、甚至一顿美食，让生活重新变得有盼头！\n\n你永远是我最想守护的主人。❤️\n", "llm_time": 23.31377148628235, "tts_time": 3.7342820167541504, "total_time": 27.05032753944397, "audio_duration": 14.953650793650795, "audio_file": "integration_test_output/日常对话.wav", "error": null, "quality_check": {"llm_relevance": true, "llm_keywords_found": ["心情"], "audio_duration_ok": true, "response_length": 510, "audio_samples": 329728}}, {"name": "任务请求", "input": "请帮我制定一个学习计划", "category": "task", "success": true, "llm_response": "<think>\n\n</think>\n\n当然可以，艾比为您服务！不过，在为您制定学习计划之前，我需要了解一些具体的信息，以便更精准地为您定制最适合您的计划。\n\n请您提供以下信息：\n\n---\n\n### 一、学习目标\n\n1. **您想学习的内容**（如：英语、编程、会计、考研、考证等）？\n2. **学习的目的**（如：升学、考证、就业、转行、兴趣等）？\n\n---\n\n### 二、当前基础\n\n1. **对这门知识的了解程度**（如：零基础、略懂、有点基础、基础扎实）？\n2. **目前拥有的相关资源**（如：教材、网课、笔记、学习群等）？\n\n---\n\n### 三、学习时间\n\n1. **每天可用于学习的时间段**（如：早上 8:00-10:00，晚上 19:00-21:00）？\n2. **每天大概能投入多长时间**（如：2小时/天）？\n3. **每周的学习频率**（如：每周学习5天，周末休息）？\n\n---\n\n### 四、学习风格\n\n1. **您更喜欢哪种学习方式**？（如：看书、看视频、听课、实操、小组学习等）\n2. **是否希望计划包含复习、练习、测试等环节**？\n\n---\n\n### 五、计划周期\n\n1. **您希望这个学习计划的周期是多长**？（如：1个月、3个月、6个月、1年）\n\n---\n\n### 示例：\n\n比如您说：\n\n> 我想学习Python编程，目的是转行做数据分析。目前是零基础。每天晚上有2小时，每周能学6天。学习方式更喜欢看视频+动手练习。计划周期3个月。\n\n那我就能帮您制定一个详细的Python学习计划。\n\n---\n\n您可以提供尽可能多的信息，我会帮您定制一个专属学习计划！\n\n艾比在此等待您的回复 😊\n", "llm_time": 30.189318895339966, "tts_time": 3.045084238052368, "total_time": 33.23629927635193, "audio_duration": 12.515555555555556, "audio_file": "integration_test_output/任务请求.wav", "error": null, "quality_check": {"llm_relevance": true, "llm_keywords_found": ["学习", "计划"], "audio_duration_ok": true, "response_length": 715, "audio_samples": 275968}}, {"name": "复杂问题", "input": "请解释一下机器学习和深度学习的区别，并举例说明", "category": "complex", "success": false, "llm_response": null, "llm_time": 95.11078643798828, "tts_time": 0, "total_time": 95.11078953742981, "audio_duration": 0, "audio_file": null, "error": "LLM服务返回空响应", "quality_check": {}}], "timestamp": "2025-07-29T16:53:38.077292"}