#!/bin/bash
# GPU环境依赖安装脚本
# 针对Ubuntu系统 + CUDA 12.1 + RTX 4090 D环境

set -e  # 遇到错误立即退出

echo "=== 智能语音助手GPU环境依赖安装 ==="
echo "目标环境: conda aibi"
echo "CUDA版本: 12.1"
echo "GPU: RTX 4090 D (24GB)"
echo ""

# 检查conda环境
if ! conda info --envs | grep -q "aibi"; then
    echo "错误: conda环境 'aibi' 不存在"
    echo "请先创建环境: conda create -n aibi python=3.10"
    exit 1
fi

# 激活环境
echo "激活conda环境: aibi"
source $(conda info --base)/etc/profile.d/conda.sh
conda activate aibi

# 检查CUDA是否可用
echo "检查CUDA环境..."
python -c "import torch; print(f'CUDA可用: {torch.cuda.is_available()}'); print(f'CUDA版本: {torch.version.cuda}'); print(f'GPU数量: {torch.cuda.device_count()}')" || {
    echo "警告: CUDA检查失败，将继续安装但可能需要重新安装PyTorch"
}

echo ""
echo "=== 第1步: 安装PyTorch GPU版本 ==="
# 确保安装正确的PyTorch GPU版本
pip install torch==2.3.1+cu121 torchaudio==2.3.1+cu121 torchvision==0.18.1+cu121 --index-url https://download.pytorch.org/whl/cu121

echo ""
echo "=== 第2步: 安装ONNX Runtime GPU版本 ==="
# 卸载CPU版本并安装GPU版本
pip uninstall -y onnxruntime onnxruntime-cpu 2>/dev/null || true
pip install onnxruntime-gpu>=1.16.0

echo ""
echo "=== 第3步: 安装核心语音处理库 ==="
# 安装已验证兼容的版本
pip install funasr>=1.2.6
pip install soundfile>=0.12.1
pip install librosa>=0.10.2
pip install webrtcvad>=2.0.10

echo ""
echo "=== 第4步: 安装系统级音频依赖 ==="
# 检查系统音频库
echo "检查系统音频库..."
python -c "import sounddevice; print('sounddevice: OK')" 2>/dev/null || {
    echo "安装sounddevice..."
    pip install sounddevice>=0.4.6
}

python -c "import pyaudio; print('pyaudio: OK')" 2>/dev/null || {
    echo "安装pyaudio (可能需要系统依赖)..."
    pip install pyaudio>=0.2.11 || {
        echo "警告: pyaudio安装失败，可能需要安装系统依赖:"
        echo "sudo apt-get install portaudio19-dev python3-pyaudio"
    }
}

echo ""
echo "=== 第5步: 安装其余依赖 ==="
# 安装其他依赖包
pip install -r requirements-gpu.txt

echo ""
echo "=== 第6步: 验证安装 ==="
echo "验证关键组件..."

# 验证PyTorch GPU
python -c "
import torch
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'CUDA版本: {torch.version.cuda}')
    print(f'GPU设备: {torch.cuda.get_device_name(0)}')
    print(f'GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB')
"

# 验证FunASR
python -c "
try:
    import funasr
    print(f'FunASR版本: {funasr.__version__}')
    print('FunASR: OK')
except Exception as e:
    print(f'FunASR错误: {e}')
"

# 验证音频库
python -c "
try:
    import soundfile
    import librosa
    import sounddevice
    print('音频库: OK')
except Exception as e:
    print(f'音频库错误: {e}')
"

# 验证ONNX Runtime GPU
python -c "
try:
    import onnxruntime as ort
    providers = ort.get_available_providers()
    print(f'ONNX Runtime提供者: {providers}')
    if 'CUDAExecutionProvider' in providers:
        print('ONNX Runtime GPU: OK')
    else:
        print('警告: ONNX Runtime GPU不可用')
except Exception as e:
    print(f'ONNX Runtime错误: {e}')
"

echo ""
echo "=== 安装完成 ==="
echo "如果所有验证都通过，您的GPU环境已准备就绪！"
echo ""
echo "下一步建议:"
echo "1. 运行项目测试: python -m pytest tests/"
echo "2. 测试音频功能: python verify_audio.py"
echo "3. 检查GPU使用: nvidia-smi"
echo ""
echo "如果遇到问题，请检查:"
echo "- CUDA驱动程序版本是否匹配"
echo "- 系统音频库是否正确安装"
echo "- 虚拟环境是否正确激活"
