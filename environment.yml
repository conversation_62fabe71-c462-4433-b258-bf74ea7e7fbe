# Conda环境配置文件
# 用于创建hey-aibi开发环境

name: aibi

channels:
  - conda-forge
  - pytorch
  - defaults

dependencies:
  # Python基础
  - python=3.10
  - pip
  
  # 科学计算基础
  - numpy>=1.24.0
  - scipy>=1.10.0
  
  # 音频处理
  - librosa>=0.10.0
  - soundfile>=0.12.1
  - ffmpeg
  
  # 机器学习框架
  - pytorch>=2.0.0
  - torchaudio>=2.0.0
  - cpuonly  # CPU版本，如需GPU请移除此行并添加cudatoolkit
  
  # 数据处理
  - pandas>=2.0.0
  - pyyaml>=6.0
  
  # 网络和异步
  - aiohttp>=3.9.0
  - requests>=2.31.0
  
  # 开发工具
  - jupyter
  - ipython
  - notebook
  
  # 系统工具
  - psutil>=5.9.0
  - watchdog>=3.0.0
  
  # 通过pip安装的包
  - pip:
    # 语音处理专用库
    - funasr>=1.0.0
    - pyaudio>=0.2.11
    - sounddevice>=0.4.6
    
    # ONNX推理
    - onnxruntime>=1.16.0
    - onnx>=1.15.0
    
    # 异步和事件处理
    - asyncio-mqtt>=0.13.0
    
    # 数据验证和配置
    - pydantic>=2.0.0
    - python-dotenv>=1.0.0
    - cerberus>=1.3.4
    - python-decouple>=3.8
    
    # 日志和监控
    - structlog>=23.1.0
    - prometheus-client>=0.17.0
    
    # 文本处理
    - jieba>=0.42.1
    
    # HTTP客户端
    - httpx>=0.25.0
    
    # 数据类和类型
    - dataclasses-json>=0.6.0
    - typing-extensions>=4.7.0
    - pathlib2>=2.3.7
    
    # 开发和测试工具
    - pytest>=7.4.0
    - pytest-asyncio>=0.21.0
    - pytest-cov>=4.1.0
    - pytest-mock>=3.11.0
    - pytest-benchmark>=4.0.0
    
    # 代码质量工具
    - black>=23.0.0
    - flake8>=6.0.0
    - mypy>=1.5.0
    - isort>=5.12.0
    - bandit>=1.7.5
    
    # 性能分析
    - memory-profiler>=0.61.0
    - line-profiler>=4.0.0
    
    # 文档生成
    - sphinx>=7.0.0
    - sphinx-rtd-theme>=1.3.0
    - myst-parser>=2.0.0
    
    # 构建工具
    - build>=0.10.0
    - twine>=4.0.0
    
    # 代码格式化和检查
    - autopep8>=2.0.0
    - flake8-docstrings>=1.7.0
    - flake8-import-order>=0.18.0
    
    # 安全检查
    - safety>=2.3.0
    
    # 项目管理
    - pre-commit>=3.3.0
