#!/bin/bash
"""
语音助手集成测试启动脚本
"""

echo "🚀 启动语音助手集成测试..."

# 检查conda是否可用
if ! command -v conda &> /dev/null; then
    echo "❌ 未找到conda，请先安装Miniconda或Anaconda"
    exit 1
fi

# 激活conda环境
echo "🔧 激活conda环境 aibi..."
source ~/miniconda3/etc/profile.d/conda.sh
conda activate aibi

# 检查环境是否激活成功
if [[ "$CONDA_DEFAULT_ENV" != "aibi" ]]; then
    echo "❌ 无法激活aibi环境，请检查环境是否存在"
    exit 1
fi

# 进入项目目录
cd /home/<USER>/project/hey-aibi

# 运行测试
echo "▶️  启动集成测试..."
python scripts/test_main.py
