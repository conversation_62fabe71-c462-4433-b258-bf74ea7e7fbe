[tool:pytest]
# pytest配置文件

# 测试目录
testpaths = tests

# 测试文件模式
python_files = test_*.py *_test.py

# 测试类模式
python_classes = Test*

# 测试函数模式
python_functions = test_*

# 最小版本要求
minversion = 7.0

# 添加选项
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=src
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=80
    --asyncio-mode=auto

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    slow: 慢速测试
    audio: 音频相关测试
    model: 模型相关测试
    network: 网络相关测试
    requires_audio: 需要音频设备的测试
    requires_network: 需要网络连接的测试

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:torch.*

# 异步测试配置
asyncio_mode = auto

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 测试发现
norecursedirs = .git .tox dist build *.egg .venv venv

# 覆盖率配置
[coverage:run]
source = src
omit = 
    */tests/*
    */test_*
    */__init__.py
    */setup.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
    if __name__ == .__main__.:
    if TYPE_CHECKING:
