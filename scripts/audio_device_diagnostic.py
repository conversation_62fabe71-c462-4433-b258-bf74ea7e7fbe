#!/usr/bin/env python3
"""
音频设备诊断工具
全面检查和修复音频设备问题，特别是USB Gadget设备
"""

import subprocess
import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    import sounddevice as sd
    from src.core.audio_device_manager import AudioDeviceManager
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)


class AudioDeviceDiagnostic:
    """音频设备诊断工具"""
    
    def __init__(self):
        self.audio_manager = AudioDeviceManager()
    
    def run_command(self, command, timeout=10):
        """安全执行系统命令"""
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                capture_output=True, 
                text=True, 
                timeout=timeout
            )
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", "命令超时"
        except Exception as e:
            return False, "", str(e)
    
    def check_alsa_devices(self):
        """检查ALSA音频设备"""
        print("\n🔍 检查ALSA音频设备...")
        print("=" * 60)
        
        # 检查录音设备
        success, stdout, stderr = self.run_command("arecord -l")
        if success:
            print("📋 ALSA录音设备:")
            print(stdout)
        else:
            print(f"❌ 获取ALSA录音设备失败: {stderr}")
        
        # 检查声卡信息
        success, stdout, stderr = self.run_command("cat /proc/asound/cards")
        if success:
            print("🎵 声卡信息:")
            print(stdout)
        else:
            print(f"❌ 获取声卡信息失败: {stderr}")
    
    def check_usb_devices(self):
        """检查USB设备"""
        print("\n🔍 检查USB设备...")
        print("=" * 60)
        
        # 检查USB设备列表
        success, stdout, stderr = self.run_command("lsusb")
        if success:
            print("🔌 USB设备列表:")
            usb_lines = stdout.strip().split('\n')
            for line in usb_lines:
                if 'audio' in line.lower() or 'gadget' in line.lower() or 'sound' in line.lower():
                    print(f"🎤 {line}")
                else:
                    print(f"   {line}")
        else:
            print(f"❌ 获取USB设备列表失败: {stderr}")
        
        # 检查USB音频设备详细信息
        success, stdout, stderr = self.run_command("lsusb -v | grep -A 10 -B 5 -i audio")
        if success and stdout.strip():
            print("\n🎵 USB音频设备详细信息:")
            print(stdout)
    
    def check_pulseaudio_devices(self):
        """检查PulseAudio设备"""
        print("\n🔍 检查PulseAudio设备...")
        print("=" * 60)
        
        # 检查PulseAudio源设备
        success, stdout, stderr = self.run_command("pactl list sources short")
        if success:
            print("🎤 PulseAudio输入源:")
            print(stdout)
        else:
            print(f"❌ 获取PulseAudio源失败: {stderr}")
        
        # 检查PulseAudio卡设备
        success, stdout, stderr = self.run_command("pactl list cards short")
        if success:
            print("🎵 PulseAudio声卡:")
            print(stdout)
        else:
            print(f"❌ 获取PulseAudio声卡失败: {stderr}")
    
    def check_device_occupation(self):
        """检查设备占用情况"""
        print("\n🔍 检查音频设备占用情况...")
        print("=" * 60)
        
        # 使用lsof检查
        success, stdout, stderr = self.run_command("lsof /dev/snd/* 2>/dev/null")
        if success and stdout.strip():
            print("📋 占用音频设备的进程 (lsof):")
            print(stdout)
        else:
            print("✅ lsof未发现设备占用")
        
        # 使用fuser检查
        success, stdout, stderr = self.run_command("fuser -v /dev/snd/* 2>/dev/null")
        if success and stdout.strip():
            print("\n📋 占用音频设备的进程 (fuser):")
            print(stdout)
        else:
            print("✅ fuser未发现设备占用")
    
    def check_permissions(self):
        """检查权限"""
        print("\n🔍 检查音频设备权限...")
        print("=" * 60)
        
        # 检查用户组
        success, stdout, stderr = self.run_command("groups")
        if success:
            groups = stdout.strip().split()
            print(f"👤 当前用户组: {' '.join(groups)}")
            if 'audio' in groups:
                print("✅ 用户在audio组中")
            else:
                print("❌ 用户不在audio组中，可能影响音频设备访问")
        
        # 检查/dev/snd权限
        success, stdout, stderr = self.run_command("ls -la /dev/snd/")
        if success:
            print("\n📁 /dev/snd/ 设备权限:")
            print(stdout)
        else:
            print(f"❌ 检查/dev/snd/权限失败: {stderr}")
    
    def check_sounddevice_detection(self):
        """检查sounddevice库检测到的设备"""
        print("\n🔍 检查sounddevice库检测到的设备...")
        print("=" * 60)
        
        try:
            devices = self.audio_manager.get_available_input_devices()
            print("🎤 sounddevice检测到的输入设备:")
            for device in devices:
                print(f"   {device}")
            
            if not any('gadget' in device.name.lower() for device in devices):
                print("\n⚠️ 未检测到USB Gadget设备")
            else:
                print("\n✅ 检测到USB Gadget设备")
                
        except Exception as e:
            print(f"❌ sounddevice检测失败: {e}")
    
    def fix_audio_issues(self):
        """尝试修复音频问题"""
        print("\n🔧 尝试修复音频问题...")
        print("=" * 60)
        
        fixes_applied = []
        
        # 1. 重启PulseAudio
        print("🔄 重启PulseAudio服务...")
        success, stdout, stderr = self.run_command("pulseaudio --kill && sleep 2 && pulseaudio --start")
        if success:
            print("✅ PulseAudio重启成功")
            fixes_applied.append("PulseAudio重启")
        else:
            print(f"❌ PulseAudio重启失败: {stderr}")
        
        # 2. 重新加载ALSA
        print("🔄 重新加载ALSA...")
        success, stdout, stderr = self.run_command("sudo alsa force-reload")
        if success:
            print("✅ ALSA重新加载成功")
            fixes_applied.append("ALSA重新加载")
        else:
            print(f"⚠️ ALSA重新加载需要sudo权限")
        
        # 3. 重新扫描USB设备
        print("🔄 重新扫描USB设备...")
        success, stdout, stderr = self.run_command("sudo udevadm trigger --subsystem-match=usb")
        if success:
            print("✅ USB设备重新扫描成功")
            fixes_applied.append("USB设备重新扫描")
        else:
            print(f"⚠️ USB设备重新扫描需要sudo权限")
        
        # 4. 等待设备重新识别
        if fixes_applied:
            print("⏳ 等待设备重新识别...")
            time.sleep(3)
        
        return fixes_applied
    
    def kill_audio_processes(self):
        """终止占用音频设备的进程"""
        print("\n🔧 终止占用音频设备的进程...")
        print("=" * 60)
        
        # 获取占用进程
        success, stdout, stderr = self.run_command("lsof -t /dev/snd/* 2>/dev/null")
        if success and stdout.strip():
            pids = stdout.strip().split('\n')
            print(f"发现 {len(pids)} 个占用音频设备的进程")
            
            for pid in pids:
                if pid.strip():
                    # 获取进程信息
                    success2, proc_info, _ = self.run_command(f"ps -p {pid} -o comm=")
                    proc_name = proc_info.strip() if success2 else "未知进程"
                    
                    print(f"终止进程 {pid} ({proc_name})...")
                    success3, _, stderr3 = self.run_command(f"kill {pid}")
                    if success3:
                        print(f"✅ 进程 {pid} 已终止")
                    else:
                        print(f"❌ 终止进程 {pid} 失败: {stderr3}")
        else:
            print("✅ 没有发现占用音频设备的进程")
    
    def run_full_diagnostic(self):
        """运行完整诊断"""
        print("🩺 音频设备全面诊断")
        print("=" * 60)
        
        # 基础检查
        self.check_alsa_devices()
        self.check_usb_devices()
        self.check_pulseaudio_devices()
        self.check_device_occupation()
        self.check_permissions()
        self.check_sounddevice_detection()
        
        # 询问是否修复
        print("\n" + "=" * 60)
        choice = input("是否尝试自动修复音频问题? (y/N): ").strip().lower()
        
        if choice in ['y', 'yes']:
            # 先终止占用进程
            self.kill_audio_processes()
            
            # 然后修复问题
            fixes = self.fix_audio_issues()
            
            if fixes:
                print(f"\n✅ 已应用修复: {', '.join(fixes)}")
                print("🔄 重新检查设备...")
                time.sleep(2)
                self.check_sounddevice_detection()
            else:
                print("\n⚠️ 未能应用任何修复")
        
        print("\n" + "=" * 60)
        print("🏁 诊断完成")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='音频设备诊断工具')
    parser.add_argument('--fix', action='store_true', help='自动修复问题')
    parser.add_argument('--kill-processes', action='store_true', help='终止占用音频设备的进程')
    
    args = parser.parse_args()
    
    diagnostic = AudioDeviceDiagnostic()
    
    if args.kill_processes:
        diagnostic.kill_audio_processes()
    elif args.fix:
        diagnostic.fix_audio_issues()
        diagnostic.check_sounddevice_detection()
    else:
        diagnostic.run_full_diagnostic()


if __name__ == "__main__":
    main()
