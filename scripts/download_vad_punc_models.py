#!/usr/bin/env python3
"""
下载VAD和PUNC模型到本地
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def download_models():
    """下载VAD和PUNC模型"""
    try:
        from funasr import AutoModel
        
        # 创建模型目录
        models_dir = project_root / "models" / "funasr"
        models_dir.mkdir(parents=True, exist_ok=True)
        
        print("🔄 开始下载VAD和PUNC模型...")
        
        # 下载VAD模型
        print("📥 下载fsmn-vad模型...")
        vad_model = AutoModel(model="fsmn-vad")
        vad_target_dir = models_dir / "fsmn-vad"
        if vad_target_dir.exists():
            print(f"✅ VAD模型已存在: {vad_target_dir}")
        else:
            # 复制模型文件到目标目录
            import shutil
            shutil.copytree(vad_model.model_path, vad_target_dir)
            print(f"✅ VAD模型下载完成: {vad_target_dir}")
        
        # 下载PUNC模型
        print("📥 下载ct-punc模型...")
        punc_model = AutoModel(model="ct-punc")
        punc_target_dir = models_dir / "ct-punc"
        if punc_target_dir.exists():
            print(f"✅ PUNC模型已存在: {punc_target_dir}")
        else:
            # 复制模型文件到目标目录
            import shutil
            shutil.copytree(punc_model.model_path, punc_target_dir)
            print(f"✅ PUNC模型下载完成: {punc_target_dir}")
        
        print("🎉 所有模型下载完成！")
        
        # 显示目录结构
        print("\n📁 模型目录结构:")
        for item in sorted(models_dir.rglob("*")):
            if item.is_dir():
                level = len(item.relative_to(models_dir).parts)
                indent = "  " * level
                print(f"{indent}{item.name}/")
        
        return True
        
    except Exception as e:
        print(f"❌ 下载模型失败: {e}")
        return False

if __name__ == "__main__":
    success = download_models()
    sys.exit(0 if success else 1)
