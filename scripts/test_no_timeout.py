#!/usr/bin/env python3
"""
测试语音助手（不使用timeout）
用于排查自动退出问题
"""

import asyncio
import sys
import os
import signal
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.application.voice_assistant import VoiceAssistant


async def main():
    """主函数"""
    print("🔍 测试语音助手（无timeout限制）")
    print("=" * 50)
    
    assistant = VoiceAssistant()
    
    try:
        print("🚀 启动语音助手...")
        start_time = time.time()
        
        # 运行语音助手
        success = await assistant.run()
        
        end_time = time.time()
        runtime = end_time - start_time
        
        if success:
            print(f"✅ 语音助手正常退出，运行时间: {runtime:.1f}秒")
        else:
            print(f"❌ 语音助手异常退出，运行时间: {runtime:.1f}秒")
            
    except KeyboardInterrupt:
        print("\n🛑 用户中断")
    except Exception as e:
        print(f"❌ 运行异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("🧹 清理完成")


if __name__ == "__main__":
    # 设置信号处理
    def signal_handler(signum, frame):
        signal_names = {2: "SIGINT", 15: "SIGTERM"}
        signal_name = signal_names.get(signum, f"信号{signum}")
        print(f"\n📡 脚本接收到{signal_name}")
        # 不做任何处理，让语音助手自己处理
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 运行主函数
    asyncio.run(main())
