#!/usr/bin/env python3
"""
CosyVoice安装脚本
自动安装CosyVoice及其依赖
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_command(cmd, cwd=None, check=True):
    """运行命令"""
    logger.info(f"执行命令: {cmd}")
    if isinstance(cmd, str):
        cmd = cmd.split()
    
    try:
        result = subprocess.run(cmd, cwd=cwd, check=check, capture_output=True, text=True)
        if result.stdout:
            logger.info(f"输出: {result.stdout}")
        return result
    except subprocess.CalledProcessError as e:
        logger.error(f"命令执行失败: {e}")
        if e.stderr:
            logger.error(f"错误输出: {e.stderr}")
        if check:
            raise
        return e


def check_conda():
    """检查conda是否可用"""
    try:
        result = subprocess.run(['conda', '--version'], capture_output=True, text=True)
        logger.info(f"Conda版本: {result.stdout.strip()}")
        return True
    except FileNotFoundError:
        logger.error("Conda未找到，请先安装Anaconda或Miniconda")
        return False


def check_git():
    """检查git是否可用"""
    try:
        result = subprocess.run(['git', '--version'], capture_output=True, text=True)
        logger.info(f"Git版本: {result.stdout.strip()}")
        return True
    except FileNotFoundError:
        logger.error("Git未找到，请先安装Git")
        return False


def install_cosyvoice():
    """安装CosyVoice"""
    project_root = Path(__file__).parent.parent
    plugins_dir = project_root / "src" / "plugins"
    cosyvoice_dir = plugins_dir / "CosyVoice"
    
    logger.info("开始安装CosyVoice...")
    
    # 检查依赖
    if not check_conda():
        return False
    
    if not check_git():
        return False
    
    # 创建plugins目录
    plugins_dir.mkdir(parents=True, exist_ok=True)
    logger.info(f"创建目录: {plugins_dir}")

    # 克隆CosyVoice仓库
    if cosyvoice_dir.exists():
        logger.info("CosyVoice目录已存在，跳过克隆")
    else:
        logger.info("克隆CosyVoice仓库...")
        run_command([
            "git", "clone", "--recursive",
            "https://github.com/FunAudioLLM/CosyVoice.git"
        ], cwd=plugins_dir)
        
        # 更新子模块
        logger.info("更新子模块...")
        run_command([
            "git", "submodule", "update", "--init", "--recursive"
        ], cwd=cosyvoice_dir)
    
    # 安装conda依赖
    logger.info("安装conda依赖...")
    try:
        run_command([
            "conda", "install", "-y", "-c", "conda-forge", "pynini==2.1.5"
        ])
    except subprocess.CalledProcessError:
        logger.warning("pynini安装失败，可能需要手动安装")
    
    # 安装pip依赖
    requirements_file = cosyvoice_dir / "requirements.txt"
    if requirements_file.exists():
        logger.info("安装pip依赖...")
        run_command([
            "pip", "install", "-r", str(requirements_file)
        ])
    else:
        logger.warning("requirements.txt文件不存在")
    
    # 检查模型文件
    model_dir = project_root / "models" / "tts" / "CosyVoice-300M"
    if not model_dir.exists():
        logger.warning(f"模型目录不存在: {model_dir}")
        logger.info("请使用以下命令下载模型:")
        logger.info("python -c \"from modelscope import snapshot_download; snapshot_download('iic/CosyVoice-300M', local_dir='models/tts/CosyVoice-300M')\"")
    else:
        logger.info(f"模型目录已存在: {model_dir}")
    
    logger.info("CosyVoice安装完成！")
    return True


def download_model():
    """下载CosyVoice模型"""
    logger.info("开始下载CosyVoice-300M模型...")
    
    project_root = Path(__file__).parent.parent
    model_dir = project_root / "models" / "tts" / "CosyVoice-300M"
    
    if model_dir.exists() and any(model_dir.iterdir()):
        logger.info("模型已存在，跳过下载")
        return True
    
    try:
        # 安装modelscope
        logger.info("安装modelscope...")
        run_command(["pip", "install", "modelscope"])
        
        # 下载模型
        logger.info("下载模型文件...")
        model_dir.parent.mkdir(parents=True, exist_ok=True)
        
        import_cmd = [
            "python", "-c",
            f"from modelscope import snapshot_download; snapshot_download('iic/CosyVoice-300M', local_dir='{model_dir}')"
        ]
        run_command(import_cmd)
        
        logger.info("模型下载完成！")
        return True
        
    except Exception as e:
        logger.error(f"模型下载失败: {e}")
        return False


def verify_installation():
    """验证安装"""
    logger.info("验证CosyVoice安装...")

    project_root = Path(__file__).parent.parent
    cosyvoice_dir = project_root / "src" / "plugins" / "CosyVoice"
    model_dir = project_root / "models" / "tts" / "CosyVoice-300M"
    
    # 检查源码
    if not cosyvoice_dir.exists():
        logger.error("CosyVoice源码目录不存在")
        return False
    
    # 检查模型
    if not model_dir.exists():
        logger.error("模型目录不存在")
        return False
    
    # 尝试导入
    try:
        sys.path.insert(0, str(cosyvoice_dir))
        matcha_path = project_root / "src" / "plugins" / "third_party" / "Matcha-TTS"
        sys.path.insert(0, str(matcha_path))
        
        from cosyvoice.cli.cosyvoice import CosyVoice
        logger.info("CosyVoice模块导入成功")
        
        # 尝试加载模型
        logger.info("测试模型加载...")
        model = CosyVoice(str(model_dir), load_jit=False, load_trt=False, fp16=False)
        logger.info("模型加载成功")
        
        # 获取说话人列表
        try:
            speakers = model.list_available_spks()
            logger.info(f"可用说话人: {speakers}")
        except:
            logger.warning("无法获取说话人列表")
        
        logger.info("✅ CosyVoice安装验证成功！")
        return True
        
    except Exception as e:
        logger.error(f"验证失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="CosyVoice安装脚本")
    parser.add_argument("--install", action="store_true", help="安装CosyVoice")
    parser.add_argument("--download-model", action="store_true", help="下载模型")
    parser.add_argument("--verify", action="store_true", help="验证安装")
    parser.add_argument("--all", action="store_true", help="执行完整安装流程")
    
    args = parser.parse_args()
    
    if args.all:
        # 完整安装流程
        logger.info("开始完整安装流程...")
        
        if not install_cosyvoice():
            logger.error("CosyVoice安装失败")
            return 1
        
        if not download_model():
            logger.error("模型下载失败")
            return 1
        
        if not verify_installation():
            logger.error("安装验证失败")
            return 1
        
        logger.info("🎉 CosyVoice完整安装成功！")
        return 0
    
    if args.install:
        if install_cosyvoice():
            logger.info("✅ CosyVoice安装成功")
        else:
            logger.error("❌ CosyVoice安装失败")
            return 1
    
    if args.download_model:
        if download_model():
            logger.info("✅ 模型下载成功")
        else:
            logger.error("❌ 模型下载失败")
            return 1
    
    if args.verify:
        if verify_installation():
            logger.info("✅ 安装验证成功")
        else:
            logger.error("❌ 安装验证失败")
            return 1
    
    if not any([args.install, args.download_model, args.verify, args.all]):
        parser.print_help()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
