# 状态机配置
state_machine:
  # 对话超时时间（秒）
  conversation_timeout: 15.0
  
  # 语音输入超时时间（秒）
  speech_timeout: 10.0
  
  # 错误恢复超时时间（秒）
  error_recovery_timeout: 5.0
  
  # 语音活动检测配置
  speech_activity_detection:
    # 是否启用语音活动检测
    enabled: true
    
    # 音频能量阈值
    energy_threshold: 0.01
    
    # 静默检测时间（秒）
    silence_duration: 2.0
    
    # 最小语音持续时间（秒）
    min_speech_duration: 0.5
  
  # 状态转换日志
  logging:
    # 是否记录状态转换
    log_transitions: true
    
    # 是否记录状态历史
    log_history: true
    
    # 历史记录最大条数
    max_history_size: 100
  
  # 调试模式
  debug:
    # 是否启用调试模式
    enabled: false
    
    # 是否显示详细状态信息
    verbose_state_info: false
    
    # 是否启用状态转换验证
    validate_transitions: true
