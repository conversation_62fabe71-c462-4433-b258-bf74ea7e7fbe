# 生产环境配置文件
# 覆盖主配置文件中的生产环境特定设置

# ===== 系统配置覆盖 =====
system:
  environment: "production"
  debug: false
  log_level: "INFO"

# ===== 生产环境音频配置 =====
audio:
  # 生产环境使用较大的缓冲区保证稳定性
  buffer_size: 48000  # 3秒缓冲

# ===== 生产环境模型配置 =====
wake_word:
  # 生产环境使用较高的阈值减少误触发
  confidence_threshold: 0.8

speech_recognition:
  # 生产环境使用稳定的VAD设置
  vad:
    max_end_silence: 1500    # 1.5秒
    max_start_silence: 4000  # 4秒

# ===== 生产环境LLM配置 =====
llm:
  # 生产环境使用较长的超时保证稳定性
  timeout: 45
  max_tokens: 1000
  retry_attempts: 5

# ===== 生产环境TTS配置 =====
tts:
  # 生产环境优化音质
  speed: 1.0
  volume: 0.8

# ===== 生产环境性能配置 =====
performance:
  # 生产环境严格的延迟目标
  target_latency:
    wake_word: 100
    speech_recognition: 600
    llm_response: 500
    tts_synthesis: 500
    total_end_to_end: 1500
  
  # 生产环境并发配置
  max_concurrent_requests: 3
  thread_pool_size: 8
  
  # 生产环境缓存配置
  enable_model_cache: true
  cache_size: 200  # MB

# ===== 生产环境监控配置 =====
monitoring:
  enable_metrics: true
  metrics_port: 8080
  health_check_interval: 60  # 1分钟
  performance_logging: true
  
  # 生产环境告警阈值
  alerts:
    high_latency_threshold: 1800  # 1.8秒
    error_rate_threshold: 0.02    # 2%
    memory_usage_threshold: 0.7   # 70%

# ===== 生产环境日志配置 =====
logging:
  level: "INFO"
  format: "json"
  file_path: "logs/hey-aibi.log"
  max_file_size: "50MB"
  backup_count: 10
  enable_console: false  # 生产环境不输出到控制台
  
  # 生产环境日志级别
  loggers:
    "src.services": "INFO"
    "src.infrastructure": "INFO"
    "src.application": "INFO"
    "root": "WARNING"

# ===== 生产环境安全配置 =====
security:
  enable_audio_encryption: true
  max_audio_duration: 30  # 30秒限制
  rate_limiting:
    enabled: true
    max_requests_per_minute: 30  # 更严格的限制
  
  # 生产环境数据保护
  data_retention_days: 3
  auto_cleanup: true

# ===== 生产环境禁用开发功能 =====
development:
  hot_reload: false
  debug_audio: false
  mock_services: false
  test_mode: false
  enable_profiling: false
  enable_debugging: false
