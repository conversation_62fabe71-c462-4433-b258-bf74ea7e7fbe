# 对话管理服务配置
conversation_service:
  # 对话上下文管理
  max_context_length: 10          # 最大上下文长度（消息数）
  max_turn_count: 50              # 最大对话轮数
  session_timeout: 300.0          # 会话超时时间（秒）
  
  # 打断检测
  enable_interruption: true       # 是否启用打断检测
  interruption_threshold: 0.5     # 打断检测阈值
  
  # 文本处理
  text_cleaning:
    # 是否启用文本清理
    enabled: true
    
    # 最小文本长度
    min_text_length: 2
    
    # 需要移除的噪音模式
    noise_patterns:
      - "<|startoftranscript|>"
      - "<|endoftranscript|>"
      - "<|nospeech|>"
      - "<|silence|>"
      - "uh"
      - "um"
      - "er"
      - "ah"
  
  # 会话管理
  session_management:
    # 最大历史会话数
    max_history_sessions: 100
    
    # 清理检查间隔（秒）
    cleanup_interval: 60
    
    # 是否自动保存会话
    auto_save: true
  
  # 统计和监控
  statistics:
    # 是否启用统计
    enabled: true
    
    # 统计更新间隔（秒）
    update_interval: 30
    
    # 是否记录详细统计
    detailed_stats: false
  
  # 调试模式
  debug:
    # 是否启用调试模式
    enabled: false
    
    # 是否记录所有消息
    log_all_messages: false
    
    # 是否显示详细状态信息
    verbose_state_info: false
