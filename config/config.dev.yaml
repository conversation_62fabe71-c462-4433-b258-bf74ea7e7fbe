# 开发环境配置文件
# 覆盖主配置文件中的开发环境特定设置

# ===== 系统配置覆盖 =====
system:
  environment: "development"
  debug: true
  log_level: "DEBUG"

# ===== 开发环境音频配置 =====
audio:
  # 开发环境使用较小的缓冲区以便调试
  buffer_size: 16000  # 1秒缓冲

# ===== 开发环境模型配置 =====
wake_word:
  # 开发环境使用较低的阈值便于测试
  confidence_threshold: 0.5

speech_recognition:
  # 开发环境使用更快的VAD设置
  vad:
    max_end_silence: 800     # 0.8秒
    max_start_silence: 2000  # 2秒

# ===== 开发环境LLM配置 =====
llm:
  # 开发环境使用较短的超时
  timeout: 15
  max_tokens: 500
  # 可以使用mock服务进行开发
  mock_response: false

# ===== 开发环境TTS配置 =====
tts:
  # 开发环境可以禁用TTS以加快测试
  enabled: true
  # 使用较快的合成设置
  speed: 1.2

# ===== 开发环境性能配置 =====
performance:
  # 开发环境的宽松延迟目标
  target_latency:
    total_end_to_end: 2000  # 2秒
  
  # 开发环境缓存设置
  enable_model_cache: true
  cache_size: 50  # MB

# ===== 开发环境监控配置 =====
monitoring:
  enable_metrics: true
  performance_logging: true
  health_check_interval: 10  # 更频繁的健康检查
  
  # 开发环境告警阈值
  alerts:
    high_latency_threshold: 3000  # 3秒
    error_rate_threshold: 0.1     # 10%

# ===== 开发环境日志配置 =====
logging:
  level: "DEBUG"
  enable_console: true
  # 开发环境详细日志
  loggers:
    "src.services": "DEBUG"
    "src.infrastructure": "DEBUG"
    "src.application": "DEBUG"
    "tests": "DEBUG"

# ===== 开发环境安全配置 =====
security:
  # 开发环境宽松的安全设置
  rate_limiting:
    enabled: false
  data_retention_days: 1
  auto_cleanup: true

# ===== 开发工具配置 =====
development:
  hot_reload: true
  debug_audio: true
  mock_services: false
  test_mode: false
  enable_profiling: true
  enable_debugging: true
  
  # 开发环境特定设置
  auto_restart: true
  watch_files: true
  debug_port: 5678
