# 智能VAD管理器配置
# 解决唤醒后语音收集问题的完整配置

# ASR服务配置
asr_service:
  enabled: true
  model_name: "SenseVoiceSmall"
  device: "cpu"
  sample_rate: 16000
  vad_model: "fsmn-vad"
  punc_model: "ct-punc"
  
  # 启用智能VAD管理器
  use_smart_vad: true
  
  # 智能VAD配置
  waiting_timeout: 10.0          # 唤醒后等待用户说话的超时时间（秒）
  speech_end_timeout: 1.2        # 语音结束后的静音确认时间（秒）
  vad_chunk_size: 200           # VAD分析块大小（毫秒）
  min_speech_duration: 0.3      # 最小语音持续时间（秒）
  max_silence_in_speech: 0.5    # 语音中允许的最大静音时间（秒）
  enable_vad_debug: true        # 启用VAD调试输出
  
  # 传统静音检测配置（作为备用）
  silence_duration_threshold: 2.0
  speech_duration_threshold: 0.5
  energy_threshold: 0.01
  zero_crossing_threshold: 50

# 唤醒词检测配置
wake_word_detection:
  enabled: true
  model_path: "models/wake_word_model.onnx"
  threshold: 0.85
  sample_rate: 16000
  
# 状态机配置
state_machine:
  # 启用智能VAD状态管理
  use_smart_vad_states: true
  
  # 超时配置
  wake_listening_timeout: 300.0    # 待唤醒状态超时（秒）
  speech_collecting_timeout: 15.0  # 语音收集超时（秒，作为最大保护）
  speech_processing_timeout: 30.0  # 语音处理超时（秒）
  
# 事件总线配置
event_bus:
  max_workers: 4
  queue_size: 1000
  
  # 智能VAD相关事件优先级
  event_priorities:
    wake_word_detected: "HIGH"
    speech_collection_complete: "HIGH"
    speech_timeout: "HIGH"
    vad_state_change: "NORMAL"
    speech_activity: "LOW"

# 音频管理器配置
audio_manager:
  sample_rate: 16000
  channels: 1
  chunk_size: 1024
  input_device_id: null
  output_device_id: null
  
  # 音频缓冲配置
  max_buffer_duration: 30.0      # 最大缓冲时长（秒）
  min_buffer_duration: 0.5       # 最小缓冲时长（秒）

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # 智能VAD相关日志
  loggers:
    "src.core.smart_vad_manager":
      level: "DEBUG"
    "src.services.enhanced_asr_service":
      level: "DEBUG"

# 性能监控配置
monitoring:
  enable_performance_stats: true
  stats_interval: 10.0           # 统计信息输出间隔（秒）
  
  # 智能VAD性能指标
  vad_metrics:
    track_state_transitions: true
    track_speech_activity: true
    track_timeout_events: true
    track_processing_latency: true

# 调试配置
debug:
  enable_audio_dump: false       # 是否保存音频文件用于调试
  audio_dump_path: "debug/audio"
  enable_state_logging: true     # 是否记录状态变化
  enable_timing_analysis: true   # 是否分析时序性能
