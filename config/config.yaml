# 智能语音助手系统主配置文件
# 基于三层架构设计的配置管理

# ===== 系统基础配置 =====
system:
  name: "hey-aibi"
  version: "1.0.0"
  environment: "development"
  debug: true
  log_level: "INFO"

# ===== 音频配置 =====
audio:
  sample_rate: 16000
  channels: 1
  chunk_size: 1024
  buffer_size: 32000  # 2秒缓冲
  input_device: null  # 自动选择
  output_device: null  # 自动选择

# ===== 唤醒词检测配置 =====
wake_word:
  model_path: "models/wake_word/hey_aibi.onnx"
  keyword: "hey aibi"
  confidence_threshold: 0.65  # 根据模型信息调整
  window_size: 1.5  # 秒
  hop_size: 0.5     # 秒
  enabled: true

# ===== 语音识别配置 =====
speech_recognition:
  model_name: "models/funasr/iic/SenseVoiceSmall"
  device: "cpu"
  num_threads: 4
  batch_size: 1
  # VAD配置
  vad:
    max_end_silence: 1200    # 1.2秒静音结束
    max_start_silence: 3000  # 3秒开始超时
    speech_noise_thres: 0.6  # 语音噪声阈值

# ===== 大语言模型配置 =====
llm:
  provider: "dify"
  api_url: "http://11.20.60.13/v1"            # API服务地址
  api_key: "app-NIfC6KMjycOJyp2p85LGzR0u"     # API密钥
  model: "gpt-3.5-turbo"
  max_tokens: 1000
  temperature: 0.7
  timeout: 30  # 秒
  retry_attempts: 3
  retry_delay: 1.0
  max_retry_delay: 10.0

# ===== 语音合成配置 =====
tts:
  model_name: "CosyVoice-300M"
  device: "cpu"
  num_threads: 4
  streaming: true
  voice: "default"
  speed: 1.0
  volume: 1.0

# ===== 对话管理配置 =====
conversation:
  max_history: 10
  session_timeout: 300  # 5分钟
  context_window: 4000  # token数
  enable_interrupt: true
  interrupt_sensitivity: 0.3

# ===== 性能配置 =====
performance:
  # 延迟目标（毫秒）
  target_latency:
    wake_word: 100
    speech_recognition: 600
    llm_response: 500
    tts_synthesis: 500
    total_end_to_end: 1500
  
  # 并发配置
  max_concurrent_requests: 1
  thread_pool_size: 4
  
  # 缓存配置
  enable_model_cache: true
  cache_size: 100  # MB

# ===== 监控配置 =====
monitoring:
  enable_metrics: true
  metrics_port: 8080
  health_check_interval: 30  # 秒
  performance_logging: true
  
  # 告警阈值
  alerts:
    high_latency_threshold: 2000  # 毫秒
    error_rate_threshold: 0.05    # 5%
    memory_usage_threshold: 0.8   # 80%

# ===== 日志配置 =====
logging:
  level: "INFO"
  format: "json"
  file_path: "logs/hey-aibi.log"
  max_file_size: "10MB"
  backup_count: 5
  enable_console: true
  
  # 日志级别配置
  loggers:
    "src.services": "DEBUG"
    "src.infrastructure": "INFO"
    "src.application": "INFO"

# ===== 安全配置 =====
security:
  enable_audio_encryption: false
  max_audio_duration: 60  # 秒
  rate_limiting:
    enabled: true
    max_requests_per_minute: 60
  
  # 数据保护
  data_retention_days: 7
  auto_cleanup: true

# ===== 开发配置 =====
development:
  hot_reload: true
  debug_audio: false
  mock_services: false
  test_mode: false
  
  # 开发工具
  enable_profiling: false
  enable_debugging: true
