#!/usr/bin/env python3
"""
唤醒词检测服务演示
展示如何使用唤醒词检测服务进行实时音频检测
"""

import asyncio
import numpy as np
import sounddevice as sd
import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.services.wake_word_service import WakeWordService
from src.services.event_bus import EventBus


class AudioCapture:
    """音频采集器"""
    
    def __init__(self, 
                 sample_rate: int = 16000,
                 chunk_size: int = 1024,
                 channels: int = 1):
        self.sample_rate = sample_rate
        self.chunk_size = chunk_size
        self.channels = channels
        
        self.is_recording = False
        self.event_bus = None
        
        self.logger = logging.getLogger(__name__)
    
    def set_event_bus(self, event_bus: EventBus):
        """设置事件总线"""
        self.event_bus = event_bus
    
    def audio_callback(self, indata, frames, time, status):
        """音频回调函数"""
        if status:
            self.logger.warning(f"音频状态: {status}")
        
        if self.event_bus and self.is_recording:
            # 发送音频数据到事件总线
            audio_data = indata[:, 0] if self.channels == 1 else indata
            asyncio.create_task(
                self.event_bus.emit("audio_chunk", {"audio_data": audio_data})
            )
    
    def start_recording(self):
        """开始录音"""
        self.is_recording = True
        self.stream = sd.InputStream(
            samplerate=self.sample_rate,
            channels=self.channels,
            blocksize=self.chunk_size,
            callback=self.audio_callback,
            dtype=np.float32
        )
        self.stream.start()
        self.logger.info("开始音频采集")
    
    def stop_recording(self):
        """停止录音"""
        self.is_recording = False
        if hasattr(self, 'stream'):
            self.stream.stop()
            self.stream.close()
        self.logger.info("停止音频采集")


class WakeWordDemo:
    """唤醒词检测演示"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 配置
        self.config = {
            'model_path': 'models/wake_word/hey_aibi.onnx',
            'keyword': 'hey aibi',
            'confidence_threshold': 0.85,
            'window_size': 1.5,
            'hop_size': 0.5,
            'enabled': True,
            'sample_rate': 16000,
            'chunk_size': 1024,
            'cooldown_period': 2.0
        }
        
        # 组件
        self.event_bus = None
        self.wake_word_service = None
        self.audio_capture = None
        
        # 统计
        self.detection_count = 0
    
    async def initialize(self):
        """初始化演示"""
        self.logger.info("初始化唤醒词检测演示...")
        
        # 创建事件总线
        self.event_bus = EventBus()
        await self.event_bus.initialize()
        
        # 创建唤醒词服务
        self.wake_word_service = WakeWordService(self.config, self.event_bus)
        
        # 订阅唤醒词检测事件
        await self.event_bus.subscribe(
            "wake_word_detected",
            self.on_wake_word_detected
        )
        
        # 创建音频采集器
        self.audio_capture = AudioCapture(
            sample_rate=self.config['sample_rate'],
            chunk_size=self.config['chunk_size']
        )
        self.audio_capture.set_event_bus(self.event_bus)
        
        # 初始化服务
        success = await self.wake_word_service.initialize()
        if not success:
            self.logger.error("唤醒词服务初始化失败")
            return False
        
        self.logger.info("演示初始化完成")
        return True
    
    async def start(self):
        """启动演示"""
        self.logger.info("启动唤醒词检测演示...")
        
        # 启动唤醒词服务
        success = await self.wake_word_service.start()
        if not success:
            self.logger.error("唤醒词服务启动失败")
            return False
        
        # 开始音频采集
        self.audio_capture.start_recording()
        
        self.logger.info("演示已启动，请说出 'hey aibi' 来测试唤醒词检测")
        self.logger.info("按 Ctrl+C 退出")
        
        return True
    
    async def stop(self):
        """停止演示"""
        self.logger.info("停止唤醒词检测演示...")
        
        # 停止音频采集
        if self.audio_capture:
            self.audio_capture.stop_recording()
        
        # 停止唤醒词服务
        if self.wake_word_service:
            await self.wake_word_service.stop()
        
        # 关闭事件总线
        if self.event_bus:
            await self.event_bus.shutdown()
        
        self.logger.info("演示已停止")
    
    async def on_wake_word_detected(self, data):
        """唤醒词检测事件处理"""
        self.detection_count += 1
        
        keyword = data.get('keyword', 'unknown')
        confidence = data.get('confidence', 0.0)
        timestamp = data.get('timestamp', 0.0)
        
        self.logger.info(f"🎉 检测到唤醒词: '{keyword}' (置信度: {confidence:.3f})")
        self.logger.info(f"   检测次数: {self.detection_count}")
        
        # 可以在这里添加更多的响应逻辑
        # 例如：播放提示音、启动语音识别等
    
    async def run(self):
        """运行演示"""
        try:
            # 初始化
            if not await self.initialize():
                return
            
            # 启动
            if not await self.start():
                return
            
            # 运行主循环
            while True:
                await asyncio.sleep(1)
                
                # 定期打印统计信息
                if self.detection_count > 0 and self.detection_count % 5 == 0:
                    stats = self.wake_word_service.get_detection_stats()
                    self.logger.info(f"统计信息: {stats}")
        
        except KeyboardInterrupt:
            self.logger.info("收到中断信号")
        
        except Exception as e:
            self.logger.error(f"演示运行异常: {e}")
        
        finally:
            await self.stop()


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
        ]
    )


async def main():
    """主函数"""
    setup_logging()
    
    demo = WakeWordDemo()
    await demo.run()


if __name__ == "__main__":
    asyncio.run(main())
