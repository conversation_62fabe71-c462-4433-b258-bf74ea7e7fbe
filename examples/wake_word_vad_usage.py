#!/usr/bin/env python3
"""
唤醒词VAD使用示例
展示如何在实际系统中集成智能VAD处理器
"""

import asyncio
import sys
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.wake_word_vad_handler import WakeWordVADHandler, WakeVADState


class VoiceAssistantExample:
    """语音助手示例"""
    
    def __init__(self):
        # 创建VAD处理器
        self.vad_handler = WakeWordVADHandler(
            waiting_timeout=10.0,      # 10秒等待超时
            speech_end_timeout=1.2,    # 1.2秒语音结束确认
            sample_rate=16000
        )
        
        # 设置回调
        self.vad_handler.on_speech_complete = self.on_speech_complete
        self.vad_handler.on_timeout = self.on_timeout
        
        self.is_running = False
    
    async def initialize(self):
        """初始化"""
        print("🤖 初始化语音助手...")
        
        if not await self.vad_handler.initialize():
            print("❌ VAD处理器初始化失败")
            return False
        
        print("✅ 语音助手初始化成功")
        return True
    
    async def on_wake_word_detected(self, confidence: float):
        """唤醒词检测回调"""
        print(f"🎯 检测到唤醒词! 置信度: {confidence:.4f}")
        
        # 启动智能VAD处理
        await self.vad_handler.start_after_wake_word()
    
    async def on_speech_complete(self):
        """语音收集完成回调"""
        print("✅ 语音收集完成，开始处理...")
        
        # 这里可以调用ASR进行语音识别
        # 然后调用LLM进行对话处理
        # 最后调用TTS进行语音合成
        
        print("🤖 正在处理您的请求...")
        await asyncio.sleep(1.0)  # 模拟处理时间
        print("🎵 语音助手：您好，我已经收到您的指令了！")
        
        # 处理完成后，可以继续监听唤醒词
        print("👂 继续监听唤醒词...")
    
    async def on_timeout(self):
        """超时回调"""
        print("⏰ 未检测到语音输入，返回待唤醒状态")
        
        # 返回待唤醒状态，继续监听唤醒词
        print("👂 继续监听唤醒词...")
    
    async def add_audio_data(self, audio_data: np.ndarray):
        """添加音频数据"""
        # 将音频数据发送给VAD处理器
        await self.vad_handler.add_audio_chunk(audio_data)
    
    async def simulate_conversation(self):
        """模拟对话场景"""
        print("\n🎭 模拟语音助手对话场景")
        print("=" * 50)
        
        # 场景1：正常对话
        print("\n📋 场景1：正常对话流程")
        print("👂 监听唤醒词...")
        
        # 模拟唤醒词检测
        await self.on_wake_word_detected(0.95)
        
        # 模拟用户说话：静音(0.5s) -> 语音(2s) -> 静音(2s)
        sample_rate = 16000
        chunk_size = 1024
        
        # 短暂静音
        for i in range(int(0.5 * sample_rate / chunk_size)):
            silence = np.zeros(chunk_size, dtype=np.float32)
            await self.add_audio_data(silence)
            await asyncio.sleep(0.05)
        
        # 语音输入
        frequency = 440
        for i in range(int(2.0 * sample_rate / chunk_size)):
            t = np.linspace(i * chunk_size / sample_rate, 
                           (i + 1) * chunk_size / sample_rate, 
                           chunk_size, False)
            speech = 0.3 * np.sin(2 * np.pi * frequency * t).astype(np.float32)
            await self.add_audio_data(speech)
            await asyncio.sleep(0.05)
        
        # 静音（触发语音结束）
        for i in range(int(2.0 * sample_rate / chunk_size)):
            silence = np.zeros(chunk_size, dtype=np.float32)
            await self.add_audio_data(silence)
            await asyncio.sleep(0.05)
            
            if self.vad_handler.get_state() == WakeVADState.SPEECH_COMPLETE:
                break
        
        await asyncio.sleep(2.0)
        
        # 场景2：唤醒后无语音
        print("\n📋 场景2：唤醒后无语音超时")
        print("👂 监听唤醒词...")
        
        # 模拟唤醒词检测
        await self.on_wake_word_detected(0.92)
        
        # 模拟长时间静音（触发超时）
        for i in range(int(11.0 * sample_rate / chunk_size)):
            silence = np.zeros(chunk_size, dtype=np.float32)
            await self.add_audio_data(silence)
            await asyncio.sleep(0.05)
            
            if self.vad_handler.get_state() == WakeVADState.TIMEOUT:
                break
        
        await asyncio.sleep(1.0)
        
        print("\n🎊 对话场景模拟完成！")
        print("✨ 智能VAD完美解决了唤醒后的语音收集问题！")


async def main():
    """主函数"""
    print("🚀 唤醒词VAD使用示例")
    print("🎯 展示如何解决唤醒后的语音收集问题\n")
    
    # 创建语音助手
    assistant = VoiceAssistantExample()
    
    # 初始化
    if not await assistant.initialize():
        print("❌ 语音助手初始化失败")
        return
    
    # 运行对话模拟
    await assistant.simulate_conversation()
    
    print("\n" + "="*60)
    print("📚 使用说明:")
    print("1. 在实际系统中，将 WakeWordVADHandler 集成到 ASR 服务")
    print("2. 在唤醒词检测后调用 start_after_wake_word()")
    print("3. 持续调用 add_audio_chunk() 发送音频数据")
    print("4. 通过回调函数处理语音完成和超时事件")
    print("5. 完美解决用户反馈的两种场景问题")
    
    print("\n🎉 问题解决方案已完全验证！")


if __name__ == "__main__":
    asyncio.run(main())
