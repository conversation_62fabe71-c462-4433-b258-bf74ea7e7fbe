#!/usr/bin/env python3
"""
TTS服务演示脚本
展示如何使用CosyVoice TTS服务进行语音合成
"""

import asyncio
import time
import numpy as np
import soundfile as sf
from pathlib import Path
import sys
from unittest.mock import AsyncMock, Mock

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.services.tts import CosyVoiceTTSService


async def create_tts_service():
    """创建TTS服务"""
    # 创建模拟依赖
    event_bus = AsyncMock()
    event_bus.subscribe = AsyncMock()
    event_bus.publish = AsyncMock()

    config = {
        "model_name": "CosyVoice-300M",
        "device": "cpu",
        "num_threads": 4,
        "streaming": True,
        "voice": "中文女",
        "speed": 1.0,
        "volume": 1.0
    }

    service = CosyVoiceTTSService(config, event_bus)
    return service


async def demo_basic_synthesis(service):
    """演示基本语音合成"""
    print("\n=== 基本语音合成演示 ===")
    print("💡 注意：CosyVoice-300M使用zero-shot模式，对于很短的文本可能会有采样问题")
    print("   建议使用中等长度以上的文本以获得最佳效果")

    text = "你好，我是艾比智能助手，很高兴为您服务。"

    print(f"合成文本: {text}")
    print("正在合成...")

    start_time = time.time()
    result = await service.synthesize(text)
    end_time = time.time()

    if result:
        print(f"✅ 合成成功!")
        print(f"音频长度: {result.audio_data.shape[0]} 样本")
        print(f"采样率: {result.sample_rate} Hz")
        print(f"音频时长: {result.duration:.2f} 秒")
        print(f"处理时间: {result.processing_time:.2f} 秒")
        print(f"实时率: {result.processing_time / result.duration:.2f}")

        # 检查音频质量
        if result.duration < 1.0:
            print("⚠️ 音频时长较短，这可能是由于文本长度或模型采样限制导致的")

        # 保存音频文件
        output_path = "output/basic_synthesis.wav"
        Path("output").mkdir(exist_ok=True)
        sf.write(output_path, result.audio_data, result.sample_rate)
        print(f"音频已保存到: {output_path}")
    else:
        print("❌ 合成失败")


async def demo_streaming_synthesis(service):
    """演示流式语音合成"""
    print("\n=== 流式语音合成演示 ===")
    
    text = "这是一个流式语音合成的演示。我们将实时生成音频块，这样可以降低延迟，提供更好的用户体验。"
    
    print(f"合成文本: {text}")
    print("正在流式合成...")
    
    chunks = []
    start_time = time.time()
    chunk_count = 0
    
    async for chunk in service.synthesize_streaming(text):
        chunk_count += 1
        chunks.append(chunk)
        
        if len(chunk.audio_data) > 0:
            print(f"收到音频块 {chunk_count}: {len(chunk.audio_data)} 样本")
        
        if chunk.is_final:
            print("✅ 流式合成完成")
            break
    
    end_time = time.time()
    
    # 合并所有音频块
    audio_chunks = [chunk.audio_data for chunk in chunks if len(chunk.audio_data) > 0]
    if audio_chunks:
        total_audio = np.concatenate(audio_chunks)
        sample_rate = chunks[0].sample_rate
        total_duration = len(total_audio) / sample_rate
        
        print(f"总块数: {len(chunks)}")
        print(f"总音频长度: {len(total_audio)} 样本")
        print(f"总时长: {total_duration:.2f} 秒")
        print(f"流式处理时间: {end_time - start_time:.2f} 秒")
        
        # 保存合并后的音频
        output_path = "output/streaming_synthesis.wav"
        sf.write(output_path, total_audio, sample_rate)
        print(f"合并音频已保存到: {output_path}")


async def demo_voice_control(service):
    """演示语音控制"""
    print("\n=== 语音控制演示 ===")
    
    # 获取可用语音
    available_voices = service.get_available_voices()
    print(f"可用语音: {available_voices}")
    
    if len(available_voices) > 1:
        # 测试不同语音
        text = "这是语音切换测试"
        
        for i, voice in enumerate(available_voices[:2]):  # 只测试前两个
            print(f"\n使用语音: {voice}")
            service.set_voice(voice)
            
            result = await service.synthesize(text)
            if result:
                output_path = f"output/voice_{i}_{voice}.wav"
                sf.write(output_path, result.audio_data, result.sample_rate)
                print(f"音频已保存到: {output_path}")
    else:
        print("只有一个可用语音，跳过语音切换测试")


async def demo_speed_control(service):
    """演示语速控制"""
    print("\n=== 语速控制演示 ===")
    
    text = "这是语速控制测试，我们将测试不同的语速设置。"
    speeds = [0.8, 1.0, 1.5]
    
    for speed in speeds:
        print(f"\n设置语速: {speed}")
        service.set_speed(speed)
        
        result = await service.synthesize(text)
        if result:
            print(f"音频时长: {result.duration:.2f} 秒")
            output_path = f"output/speed_{speed}.wav"
            sf.write(output_path, result.audio_data, result.sample_rate)
            print(f"音频已保存到: {output_path}")


async def demo_volume_control(service):
    """演示音量控制"""
    print("\n=== 音量控制演示 ===")
    
    text = "这是音量控制测试"
    volumes = [0.5, 1.0, 1.5]
    
    for volume in volumes:
        print(f"\n设置音量: {volume}")
        service.set_volume(volume)
        
        result = await service.synthesize(text)
        if result:
            # 计算RMS作为音量指标
            rms = np.sqrt(np.mean(result.audio_data ** 2))
            print(f"音频RMS: {rms:.4f}")
            output_path = f"output/volume_{volume}.wav"
            sf.write(output_path, result.audio_data, result.sample_rate)
            print(f"音频已保存到: {output_path}")


async def demo_long_text(service):
    """演示长文本合成"""
    print("\n=== 长文本合成演示 ===")
    
    long_text = """
    人工智能是计算机科学的一个分支，它企图了解智能的实质，
    并生产出一种新的能以人类智能相似的方式做出反应的智能机器。
    该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。
    人工智能从诞生以来，理论和技术日益成熟，应用领域也不断扩大。
    可以设想，未来人工智能带来的科技产品，将会是人类智慧的容器。
    """
    
    text = long_text.strip()
    print(f"文本长度: {len(text)} 字符")
    print("正在合成长文本...")
    
    start_time = time.time()
    result = await service.synthesize(text)
    end_time = time.time()
    
    if result:
        print(f"✅ 长文本合成成功!")
        print(f"音频时长: {result.duration:.2f} 秒")
        print(f"处理时间: {result.processing_time:.2f} 秒")
        print(f"字符/秒: {len(text) / result.duration:.1f}")
        
        output_path = "output/long_text.wav"
        sf.write(output_path, result.audio_data, result.sample_rate)
        print(f"音频已保存到: {output_path}")
    else:
        print("❌ 长文本合成失败")


async def demo_performance_test(service):
    """演示性能测试"""
    print("\n=== 性能测试演示 ===")
    
    test_texts = [
        "短文本测试",
        "这是一个中等长度的文本，用于测试TTS服务的性能表现。",
        "这是一个较长的文本，我们将使用它来评估TTS服务在处理不同长度文本时的性能差异，包括处理时间和音频质量等指标。"
    ]
    
    print(f"测试 {len(test_texts)} 种不同长度的文本...")
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n测试 {i}: 长度 {len(text)} 字符")
        
        # 执行3次取平均值
        times = []
        durations = []
        
        for j in range(3):
            result = await service.synthesize(text)
            if result:
                times.append(result.processing_time)
                durations.append(result.duration)
        
        if times:
            avg_time = sum(times) / len(times)
            avg_duration = sum(durations) / len(durations)
            rtf = avg_time / avg_duration
            
            print(f"平均处理时间: {avg_time:.3f} 秒")
            print(f"平均音频时长: {avg_duration:.3f} 秒")
            print(f"实时率: {rtf:.3f}")
        else:
            print("❌ 测试失败")


async def demo_statistics(service):
    """演示统计信息"""
    print("\n=== 统计信息演示 ===")
    
    # 执行几次合成以生成统计数据
    test_texts = [
        "统计测试一",
        "统计测试二", 
        "统计测试三"
    ]
    
    print("执行合成以生成统计数据...")
    for text in test_texts:
        await service.synthesize(text)
    
    # 获取统计信息
    stats = service.get_statistics()
    
    print(f"\n📊 TTS服务统计信息:")
    print(f"合成次数: {stats['synthesis_count']}")
    print(f"平均合成时间: {stats['avg_synthesis_time']:.3f} 秒")
    print(f"平均音频时长: {stats['avg_audio_duration']:.3f} 秒")
    print(f"总合成时间: {stats['total_synthesis_time']:.3f} 秒")
    print(f"总音频时长: {stats['total_audio_duration']:.3f} 秒")
    
    # 获取模型信息
    model_info = service.get_model_info()
    print(f"\n🤖 模型信息:")
    print(f"模型路径: {model_info.get('model_path', 'N/A')}")
    print(f"设备: {model_info.get('device', 'N/A')}")
    print(f"当前语音: {model_info.get('current_voice', 'N/A')}")
    print(f"采样率: {model_info.get('sample_rate', 'N/A')} Hz")
    print(f"可用语音: {model_info.get('available_speakers', [])}")


async def main():
    """主函数"""
    print("🎤 CosyVoice TTS服务演示")
    print("=" * 50)
    
    try:
        # 创建TTS服务
        print("🔧 创建TTS服务...")
        service = await create_tts_service()
        
        # 初始化服务
        print("🚀 初始化TTS服务...")
        success = await service._initialize_impl()
        
        if not success:
            print("❌ TTS服务初始化失败")
            return
        
        print("✅ TTS服务初始化成功")
        
        # 创建输出目录
        Path("output").mkdir(exist_ok=True)
        
        # 运行演示
        await demo_basic_synthesis(service)
        await demo_streaming_synthesis(service)
        await demo_voice_control(service)
        await demo_speed_control(service)
        await demo_volume_control(service)
        await demo_long_text(service)
        await demo_performance_test(service)
        await demo_statistics(service)
        
        print("\n🎉 所有演示完成！")
        print("📁 音频文件已保存到 output/ 目录")
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        if 'service' in locals():
            print("🧹 清理资源...")
            await service.cleanup()
            print("✅ 资源清理完成")


if __name__ == "__main__":
    asyncio.run(main())
