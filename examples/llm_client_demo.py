#!/usr/bin/env python3
"""
LLM客户端演示脚本
展示如何使用Dify LLM客户端进行对话
"""

import asyncio
import time
import yaml
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.services.llm import DifyLLMService
from src.interfaces.llm_interface import LLMRequest, LLMMessage


async def load_config():
    """加载配置"""
    config_path = project_root / "config" / "config.yaml"
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config['llm']


async def demo_simple_chat(client):
    """演示简单对话"""
    print("\n=== 简单对话演示 ===")
    
    request = LLMRequest(
        messages=[
            LLMMessage(role="user", content="你好，请简单介绍一下你自己", timestamp=time.time())
        ],
        max_tokens=200
    )
    
    print("用户: 你好，请简单介绍一下你自己")
    print("正在等待响应...")
    
    start_time = time.time()
    response = await client.chat(request)
    end_time = time.time()
    
    if response:
        print(f"助手: {response.content}")
        print(f"响应时间: {end_time - start_time:.2f}秒")
        print(f"Token使用: {response.usage}")
    else:
        print("❌ 请求失败")


async def demo_stream_chat(client):
    """演示流式对话"""
    print("\n=== 流式对话演示 ===")
    
    request = LLMRequest(
        messages=[
            LLMMessage(role="user", content="请详细解释一下人工智能的发展历程", timestamp=time.time())
        ],
        max_tokens=300,
        stream=True
    )
    
    print("用户: 请详细解释一下人工智能的发展历程")
    print("助手: ", end="", flush=True)
    
    start_time = time.time()
    full_content = ""
    
    try:
        async for chunk in client.chat_stream(request):
            if not chunk.is_final and chunk.content:
                print(chunk.content, end="", flush=True)
                full_content += chunk.content
            elif chunk.is_final:
                break
    except Exception as e:
        print(f"\n❌ 流式请求失败: {e}")
        return
    
    end_time = time.time()
    print(f"\n\n响应时间: {end_time - start_time:.2f}秒")
    print(f"总字符数: {len(full_content)}")


async def demo_conversation_flow(client):
    """演示完整对话流程"""
    print("\n=== 对话流程演示 ===")
    
    # 开始对话
    conversation_id = await client.start_conversation("demo_user")
    print(f"开始对话: {conversation_id}")
    
    # 对话轮次
    conversations = [
        "我的名字是张三，我是一名软件工程师",
        "你还记得我的名字和职业吗？",
        "请给我一些编程学习的建议"
    ]
    
    for i, user_input in enumerate(conversations, 1):
        print(f"\n--- 第 {i} 轮对话 ---")
        
        # 添加用户消息
        user_msg = LLMMessage(role="user", content=user_input, timestamp=time.time())
        await client.add_message(conversation_id, user_msg)
        
        # 获取对话历史
        history = await client.get_history(conversation_id)
        
        # 发送请求
        request = LLMRequest(
            messages=history,
            metadata={
                "conversation_id": conversation_id,
                "user_id": "demo_user"
            },
            max_tokens=150
        )
        
        print(f"用户: {user_input}")
        print("正在等待响应...")
        
        response = await client.chat(request)
        
        if response:
            print(f"助手: {response.content}")
            
            # 添加助手消息到历史
            assistant_msg = LLMMessage(role="assistant", content=response.content, timestamp=time.time())
            await client.add_message(conversation_id, assistant_msg)
        else:
            print("❌ 请求失败")
    
    # 显示完整对话历史
    print(f"\n--- 完整对话历史 ---")
    history = await client.get_history(conversation_id)
    for msg in history:
        role_name = "用户" if msg.role == "user" else "助手"
        print(f"{role_name}: {msg.content}")
    
    # 结束对话
    await client.end_conversation(conversation_id)
    print(f"\n对话已结束: {conversation_id}")


async def demo_error_handling(client):
    """演示错误处理"""
    print("\n=== 错误处理演示 ===")
    
    # 测试无效请求
    print("测试无效请求（没有用户消息）...")
    request = LLMRequest(
        messages=[
            LLMMessage(role="assistant", content="只有助手消息", timestamp=time.time())
        ]
    )
    
    response = await client.chat(request)
    if response is None:
        print("✅ 正确处理了无效请求")
    else:
        print("❌ 应该返回None")
    
    # 测试空内容
    print("\n测试空内容...")
    request = LLMRequest(
        messages=[
            LLMMessage(role="user", content="", timestamp=time.time())
        ]
    )
    
    response = await client.chat(request)
    if response:
        print(f"响应: {response.content}")
    else:
        print("❌ 空内容请求失败")


async def demo_performance_test(client):
    """演示性能测试"""
    print("\n=== 性能测试演示 ===")
    
    test_queries = [
        "你好",
        "今天天气怎么样？",
        "什么是机器学习？",
        "推荐一本书",
        "谢谢"
    ]
    
    print(f"测试 {len(test_queries)} 个查询的性能...")
    
    total_time = 0
    successful_requests = 0
    
    for i, query in enumerate(test_queries, 1):
        request = LLMRequest(
            messages=[LLMMessage(role="user", content=query, timestamp=time.time())],
            max_tokens=50
        )
        
        print(f"[{i}/{len(test_queries)}] 查询: '{query}'")
        
        start_time = time.time()
        response = await client.chat(request)
        end_time = time.time()
        
        if response:
            request_time = end_time - start_time
            total_time += request_time
            successful_requests += 1
            print(f"  ✅ 响应时间: {request_time:.2f}秒")
            print(f"  📝 响应: {response.content[:50]}...")
        else:
            print("  ❌ 请求失败")
        
        # 添加小延迟避免过于频繁的请求
        await asyncio.sleep(0.5)
    
    if successful_requests > 0:
        avg_time = total_time / successful_requests
        print(f"\n📊 性能统计:")
        print(f"成功请求: {successful_requests}/{len(test_queries)}")
        print(f"平均响应时间: {avg_time:.2f}秒")
        print(f"总时间: {total_time:.2f}秒")
    else:
        print("❌ 所有请求都失败了")


async def main():
    """主函数"""
    print("🤖 Dify LLM客户端演示")
    print("=" * 50)
    
    try:
        # 加载配置
        print("📋 加载配置...")
        config = await load_config()
        print(f"API地址: {config['api_url']}")
        
        # 创建并初始化服务
        print("🔧 初始化LLM服务...")
        client = DifyLLMService()
        
        success = await client.initialize(config)
        if not success:
            print("❌ LLM服务初始化失败")
            return

        print("✅ LLM服务初始化成功")
        
        # 健康检查
        print("🏥 执行健康检查...")
        health_ok = await client.health_check()
        if health_ok:
            print("✅ 健康检查通过")
        else:
            print("⚠️ 健康检查失败，但继续演示")
        
        # 运行演示
        await demo_simple_chat(client)
        await demo_stream_chat(client)
        await demo_conversation_flow(client)
        await demo_error_handling(client)
        await demo_performance_test(client)
        
        print("\n🎉 演示完成！")
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        if 'client' in locals():
            print("🧹 清理资源...")
            await client.cleanup()
            print("✅ 资源清理完成")


if __name__ == "__main__":
    asyncio.run(main())
