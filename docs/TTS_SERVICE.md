# TTS服务使用文档

## 概述

TTS服务是语音助手系统服务层中负责文本到语音转换的核心组件，基于CosyVoice-300M模型，支持流式语音合成、多种语音模式、异步处理等功能。

## 功能特性

- ✅ **CosyVoice-300M集成**: 支持高质量的中文语音合成
- ✅ **流式合成**: 支持实时流式音频输出，降低延迟
- ✅ **多语音支持**: 支持多种说话人语音
- ✅ **语音控制**: 支持语速、音量、语音切换
- ✅ **异步处理**: 基于asyncio的异步架构
- ✅ **智能安装指导**: 当CosyVoice不可用时提供详细的安装指导
- ✅ **事件驱动**: 支持事件总线集成
- ✅ **统计监控**: 提供详细的性能统计信息
- ✅ **自动化安装**: 提供一键安装脚本

## 架构位置

```
应用层 (Application Layer)
├── VoiceAssistant (主控制器)
└── StateMachine (状态管理)

服务层 (Service Layer)  ← TTS服务位于此层
├── WakeWordService (唤醒词检测)
├── SpeechService (语音识别)
├── ConversationService (对话管理)
├── LLMService (大语言模型服务)
├── TTSService (语音合成服务) ← 这里
└── EventBus (事件总线)

基础设施层 (Infrastructure Layer)
├── AudioManager (音频管理)
├── ConfigManager (配置管理)
└── PerformanceMonitor (性能监控)
```

## 快速开始

### 1. 安装CosyVoice

首先安装CosyVoice及其依赖：

```bash
# 一键安装（推荐）
python scripts/install_cosyvoice.py --all

# 或分步安装
python scripts/install_cosyvoice.py --install      # 安装CosyVoice源码
python scripts/install_cosyvoice.py --download-model  # 下载模型
python scripts/install_cosyvoice.py --verify       # 验证安装
```

### 2. 基本配置

```yaml
# config/config.yaml
tts:
  model_name: "CosyVoice-300M"
  device: "cpu"
  num_threads: 4
  streaming: true
  voice: "中文女"
  speed: 1.0
  volume: 1.0
```

### 3. 简单使用示例

```python
import asyncio
from unittest.mock import AsyncMock
from src.services.tts import CosyVoiceTTSService

async def simple_tts():
    # 配置
    config = {
        "model_name": "CosyVoice-300M",
        "device": "cpu",
        "num_threads": 4,
        "streaming": True,
        "voice": "中文女",
        "speed": 1.0,
        "volume": 1.0
    }
    
    # 创建事件总线
    event_bus = AsyncMock()
    
    # 创建服务
    service = CosyVoiceTTSService(config, event_bus)
    
    try:
        # 初始化
        await service._initialize_impl()
        
        # 合成语音
        result = await service.synthesize("你好，世界")
        
        if result:
            print(f"音频时长: {result.duration:.2f}秒")
            print(f"处理时间: {result.processing_time:.2f}秒")
        
    finally:
        await service.cleanup()

# 运行
asyncio.run(simple_tts())
```

### 4. 流式合成示例

```python
async def stream_tts():
    service = CosyVoiceTTSService(config, event_bus)
    await service._initialize_impl()
    
    try:
        text = "这是一个流式语音合成的演示"
        
        async for chunk in service.synthesize_streaming(text):
            if not chunk.is_final:
                # 处理音频块
                print(f"收到音频块: {len(chunk.audio_data)} 样本")
            else:
                print("合成完成")
                break
                
    finally:
        await service.cleanup()
```

### 5. 语音控制示例

```python
async def voice_control():
    service = CosyVoiceTTSService(config, event_bus)
    await service._initialize_impl()
    
    try:
        # 获取可用语音
        voices = service.get_available_voices()
        print(f"可用语音: {voices}")
        
        # 切换语音
        if len(voices) > 1:
            service.set_voice(voices[1])
        
        # 调整语速
        service.set_speed(1.2)  # 加快20%
        
        # 调整音量
        service.set_volume(0.8)  # 降低到80%
        
        # 合成测试
        result = await service.synthesize("语音参数已调整")
        
    finally:
        await service.cleanup()
```

## API参考

### CosyVoiceTTSService

主要的TTS服务类，继承自`BaseService`并实现`TTSInterface`接口。

#### 构造函数

```python
def __init__(self, config: Dict[str, Any], event_bus)
```

**参数:**
- `config`: 配置字典
- `event_bus`: 事件总线实例

#### 核心方法

```python
async def synthesize(self, text: str) -> Optional[TTSResult]
```

**参数:**
- `text`: 要合成的文本

**返回:** TTSResult对象或None（失败时）

```python
async def synthesize_streaming(self, text: str) -> AsyncGenerator[TTSStreamChunk, None]
```

**参数:**
- `text`: 要合成的文本

**返回:** 流式音频块的异步生成器

#### 语音控制方法

```python
def set_voice(self, voice: str) -> None
def set_speed(self, speed: float) -> None  # 范围: 0.5-2.0
def set_volume(self, volume: float) -> None  # 范围: 0.0-2.0
def get_available_voices(self) -> List[str]
```

#### 信息获取方法

```python
def get_model_info(self) -> Dict[str, Any]
def get_statistics(self) -> Dict[str, Any]
async def test_synthesis(self, text: str = "你好，这是一个测试") -> Optional[TTSResult]
```

## 数据结构

### TTSConfig

```python
@dataclass
class TTSConfig:
    model_path: str
    device: str = "cpu"
    num_threads: int = 4
    streaming: bool = True
    voice: str = "default"
    speed: float = 1.0
    volume: float = 1.0
    sample_rate: int = 22050
```

### TTSResult

```python
@dataclass
class TTSResult:
    audio_data: np.ndarray
    sample_rate: int
    duration: float
    processing_time: float
```

### TTSStreamChunk

```python
@dataclass
class TTSStreamChunk:
    audio_data: np.ndarray
    sample_rate: int
    is_final: bool
    chunk_id: str
```

## 事件系统

TTS服务支持以下事件：

### 订阅事件

- `text_to_synthesize`: 接收文本合成请求
- `tts_interrupt`: 接收TTS中断信号

### 发布事件

- `tts_started`: TTS开始合成
- `tts_finished`: TTS完成合成
- `tts_audio_chunk`: 流式音频块
- `tts_audio_ready`: 音频准备就绪

## 安装指南

### 自动安装（推荐）

使用提供的安装脚本进行一键安装：

```bash
# 完整安装流程
python scripts/install_cosyvoice.py --all

# 查看帮助
python scripts/install_cosyvoice.py --help
```

### 手动安装

如果自动安装失败，可以手动安装：

1. **安装CosyVoice源码**：
```bash
cd src/plugins
git clone --recursive https://github.com/FunAudioLLM/CosyVoice.git
cd CosyVoice
git submodule update --init --recursive
```

2. **安装依赖**：
```bash
conda install -y -c conda-forge pynini==2.1.5
pip install -r requirements.txt
```

3. **下载模型**：
```python
from modelscope import snapshot_download
snapshot_download('iic/CosyVoice-300M', local_dir='models/tts/CosyVoice-300M')
```

### 验证安装

```bash
# 验证安装
python scripts/install_cosyvoice.py --verify

# 运行集成测试
python test_cosyvoice_integration.py
```

## 模型集成

### CosyVoice-300M

TTS服务使用CosyVoice-300M模型：

1. **模型路径**: `models/tts/CosyVoice-300M/`
2. **支持语言**: 中文
3. **音频质量**: 22.05kHz采样率
4. **说话人**: 支持多种中文说话人
5. **推理模式**: SFT（Supervised Fine-Tuning）模式

### 模型要求

- **源码路径**: `src/plugins/CosyVoice/`
- **模型文件**: 完整的CosyVoice-300M模型文件
- **依赖库**: PyTorch、torchaudio、transformers等
- **系统要求**: Python 3.8+，建议使用conda环境

## 性能优化

1. **模型预热**: 启动时进行模型预热
2. **异步处理**: 支持并发合成请求
3. **内存管理**: 高效的音频数据处理
4. **流式输出**: 降低首字节延迟

## 测试

### 运行单元测试

```bash
conda run -n aibi python -m pytest tests/unit/test_tts_service.py -v
```

### 运行集成测试

```bash
conda run -n aibi python -m pytest tests/integration/test_tts_integration.py -v -m integration
```

### 运行演示脚本

```bash
conda run -n aibi python examples/tts_demo.py
```

## 故障排查

### 常见问题

1. **"无法导入CosyVoice模块"**:
   - 检查CosyVoice源码是否存在：`src/plugins/CosyVoice/`
   - 运行安装脚本：`python scripts/install_cosyvoice.py --install`

2. **"模型路径不存在"**:
   - 检查模型文件：`models/tts/CosyVoice-300M/`
   - 下载模型：`python scripts/install_cosyvoice.py --download-model`

3. **"模型加载失败"**:
   - 检查模型文件完整性
   - 确认Python环境和依赖版本
   - 查看详细错误日志

4. **"音频输出异常"**:
   - 检查采样率设置（应为22050Hz）
   - 验证音频数据格式
   - 检查说话人设置

5. **"内存不足"**:
   - 使用CPU模式：`device: "cpu"`
   - 减少线程数：`num_threads: 2`
   - 关闭JIT和TensorRT：`load_jit: false, load_trt: false`

### 调试方法

1. **启用详细日志**：
```python
import logging
logging.getLogger('src.services.tts').setLevel(logging.DEBUG)
```

2. **运行诊断脚本**：
```bash
python test_cosyvoice_integration.py
```

3. **检查安装状态**：
```bash
python scripts/install_cosyvoice.py --verify
```

4. **手动测试模型加载**：
```python
import sys
sys.path.append('src/plugins/CosyVoice')
sys.path.append('src/plugins/third_party/Matcha-TTS')
from cosyvoice.cli.cosyvoice import CosyVoice
model = CosyVoice('models/tts/CosyVoice-300M')
```

### 安装指导

当TTS服务检测到CosyVoice不可用时，会自动显示详细的安装指导：

```
CosyVoice TTS模型加载失败！
请按照以下步骤安装CosyVoice:

1. 克隆CosyVoice仓库到third_party目录
2. 安装依赖
3. 确保模型文件存在
4. 重启服务
```

## 配置说明

### 基本配置

```yaml
tts:
  model_name: "CosyVoice-300M"    # 模型名称
  device: "cpu"                   # 设备类型 (cpu/cuda)
  num_threads: 4                  # 线程数
  streaming: true                 # 是否启用流式合成
  voice: "中文女"                 # 默认语音
  speed: 1.0                      # 默认语速
  volume: 1.0                     # 默认音量
```

### 高级配置

```yaml
tts:
  model_name: "CosyVoice-300M"
  device: "cpu"
  num_threads: 4
  streaming: true
  voice: "中文女"
  speed: 1.0
  volume: 1.0
  # 高级选项
  sample_rate: 22050              # 采样率
  chunk_size: 1024                # 流式块大小
  max_text_length: 1000           # 最大文本长度
  timeout: 30                     # 合成超时时间
```

## 统计信息

TTS服务提供详细的统计信息：

```python
stats = service.get_statistics()
print(f"合成次数: {stats['synthesis_count']}")
print(f"平均合成时间: {stats['avg_synthesis_time']:.3f}秒")
print(f"平均音频时长: {stats['avg_audio_duration']:.3f}秒")
print(f"总合成时间: {stats['total_synthesis_time']:.3f}秒")
print(f"实时率: {stats['avg_synthesis_time'] / stats['avg_audio_duration']:.2f}")
```

## 最佳实践

1. **预热模型**: 在服务启动时进行模型预热
2. **批量处理**: 对于大量文本，考虑批量合成
3. **缓存策略**: 对常用文本进行音频缓存
4. **错误处理**: 实现完善的错误处理和重试机制
5. **资源管理**: 及时清理不再使用的音频数据

## 扩展开发

### 添加新的语音模型

1. 实现模型加载接口
2. 添加配置支持
3. 更新可用语音列表
4. 编写相应测试

### 自定义音频处理

1. 继承TTSStreamChunk类
2. 实现自定义音频处理逻辑
3. 集成到合成流程中

TTS服务为语音助手系统提供了高质量、低延迟的语音合成能力，支持多种语音模式和实时流式输出，是构建自然语音交互体验的重要组件。
