# Hey-AIBI 开发指南

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Conda (推荐) 或 Python venv
- Git
- 16GB+ 内存
- 4核8线程+ CPU

### 开发环境设置

1. **克隆项目**
```bash
git clone https://github.com/hey-aibi/hey-aibi.git
cd hey-aibi
```

2. **创建Conda环境**
```bash
# 使用environment.yml创建环境
conda env create -f environment.yml
conda activate aibi
```

3. **安装开发依赖**
```bash
make install-dev
# 或者
pip install -e ".[dev,test,docs]"
```

4. **设置Pre-commit钩子**
```bash
pre-commit install
```

5. **验证安装**
```bash
make dev-check
```

## 📁 项目结构

```
hey-aibi/
├── config/                 # 配置文件
│   ├── config.yaml         # 主配置
│   ├── config.dev.yaml     # 开发环境配置
│   └── config.prod.yaml    # 生产环境配置
├── src/                    # 源代码
│   ├── application/        # 应用层
│   ├── services/          # 服务层
│   ├── infrastructure/    # 基础设施层
│   └── interfaces/        # 接口定义
├── tests/                 # 测试代码
│   ├── unit/              # 单元测试
│   ├── integration/       # 集成测试
│   └── performance/       # 性能测试
├── models/                # 模型文件
├── docs/                  # 文档
├── scripts/               # 脚本工具
└── logs/                  # 日志文件
```

## 🏗️ 架构设计

### 三层架构

1. **应用层 (Application Layer)**
   - `VoiceAssistant`: 主控制器
   - `StateMachine`: 状态管理

2. **服务层 (Service Layer)**
   - `WakeWordService`: 唤醒词检测
   - `SpeechService`: 语音识别
   - `ConversationService`: 对话管理
   - `TTSService`: 语音合成
   - `EventBus`: 事件总线

3. **基础设施层 (Infrastructure Layer)**
   - `AudioManager`: 音频管理
   - `ConfigManager`: 配置管理
   - `LLMClient`: 大模型客户端
   - `PerformanceMonitor`: 性能监控

### 设计原则

- **单一职责原则**: 每个类只负责一个功能
- **依赖倒置原则**: 依赖抽象而非具体实现
- **开闭原则**: 对扩展开放，对修改关闭
- **接口隔离原则**: 使用小而专一的接口

## 🧪 测试策略

### 测试类型

1. **单元测试** (`tests/unit/`)
   - 测试单个函数或类
   - 使用mock隔离依赖
   - 快速执行，高覆盖率

2. **集成测试** (`tests/integration/`)
   - 测试模块间交互
   - 使用真实依赖
   - 验证端到端流程

3. **性能测试** (`tests/performance/`)
   - 测试响应时间
   - 测试资源使用
   - 压力测试

### 运行测试

```bash
# 运行所有测试
make test

# 运行单元测试
make test-unit

# 运行集成测试
make test-integration

# 运行性能测试
make test-performance

# 生成覆盖率报告
make test-coverage
```

### 测试标记

使用pytest标记来分类测试：

```python
@pytest.mark.unit
def test_config_loading():
    pass

@pytest.mark.integration
def test_speech_pipeline():
    pass

@pytest.mark.performance
def test_response_time():
    pass

@pytest.mark.slow
def test_model_loading():
    pass
```

## 🎨 代码规范

### 代码风格

- 使用 **Black** 进行代码格式化
- 使用 **isort** 进行导入排序
- 使用 **Flake8** 进行代码检查
- 使用 **MyPy** 进行类型检查

### 格式化代码

```bash
# 格式化所有代码
make format

# 检查代码质量
make lint

# 类型检查
make type-check

# 安全检查
make security-check

# 运行所有质量检查
make quality-check
```

### 命名约定

- **类名**: PascalCase (`VoiceAssistant`)
- **函数名**: snake_case (`process_audio`)
- **变量名**: snake_case (`sample_rate`)
- **常量名**: UPPER_SNAKE_CASE (`MAX_RETRIES`)
- **私有方法**: 前缀下划线 (`_internal_method`)

### 文档字符串

使用Google风格的文档字符串：

```python
def process_audio(audio_data: np.ndarray, sample_rate: int) -> Dict[str, Any]:
    """处理音频数据进行语音识别。
    
    Args:
        audio_data: 音频数据数组
        sample_rate: 采样率
        
    Returns:
        包含识别结果的字典
        
    Raises:
        ValueError: 当音频数据无效时
    """
    pass
```

## 🔧 开发工作流

### 功能开发流程

1. **创建功能分支**
```bash
git checkout -b feature/new-feature
```

2. **编写代码**
   - 遵循架构设计原则
   - 编写单元测试
   - 添加文档字符串

3. **运行质量检查**
```bash
make dev-check
```

4. **提交代码**
```bash
git add .
git commit -m "feat: add new feature"
```

5. **推送并创建PR**
```bash
git push origin feature/new-feature
```

### 提交信息规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

- `feat:` 新功能
- `fix:` 修复bug
- `docs:` 文档更新
- `style:` 代码格式化
- `refactor:` 代码重构
- `test:` 测试相关
- `chore:` 构建工具或辅助工具的变动

示例：
```
feat(speech): add real-time speech recognition
fix(audio): resolve audio device detection issue
docs(api): update API documentation
```

## 🐛 调试指南

### 日志配置

系统使用结构化日志，支持多种输出格式：

```python
import logging
from src.infrastructure.logging_config import get_contextual_logger

# 获取上下文日志器
logger = get_contextual_logger(__name__, service="speech", version="1.0")

# 记录日志
logger.info("Processing audio", audio_length=1024, sample_rate=16000)
logger.error("Recognition failed", error_code="ASR_001")
```

### 调试模式

```bash
# 调试模式运行
make debug

# 开发模式运行（详细日志）
make run-dev

# 性能分析
python -m cProfile -o profile.stats -m src.main
```

### 常见问题

1. **音频设备问题**
   - 检查音频设备权限
   - 验证设备兼容性
   - 查看音频配置

2. **模型加载失败**
   - 检查模型文件路径
   - 验证模型格式
   - 查看内存使用情况

3. **网络连接问题**
   - 检查API密钥配置
   - 验证网络连接
   - 查看代理设置

## 📊 性能优化

### 性能监控

系统内置性能监控，可以实时查看关键指标：

```python
from src.infrastructure.performance_monitor import PerformanceMonitor, TimerContext

monitor = PerformanceMonitor()

# 使用计时器上下文
with TimerContext(monitor, "speech_recognition"):
    result = recognize_speech(audio_data)

# 记录指标
monitor.record_gauge("queue_size", len(audio_queue))
monitor.record_counter("requests_total", 1)
```

### 优化建议

1. **模型优化**
   - 使用ONNX量化模型
   - 启用模型缓存
   - 批处理推理

2. **内存优化**
   - 及时释放音频缓冲区
   - 使用对象池
   - 监控内存泄漏

3. **并发优化**
   - 异步处理音频流
   - 并行模型推理
   - 事件驱动架构

## 🚀 部署指南

### 开发环境部署

```bash
# 启动开发服务器
make run-dev

# 启动监控服务
python -m src.infrastructure.performance_monitor --port 8080
```

### 生产环境部署

```bash
# 构建项目
make build

# 运行生产模式
python -m src.main --config config/config.prod.yaml
```

### Docker部署

```bash
# 构建Docker镜像
make docker-build

# 运行Docker容器
make docker-run
```

## 📚 参考资料

- [架构设计文档](../架构设计文档.md)
- [API文档](api.md)
- [配置指南](configuration.md)
- [故障排查](troubleshooting.md)

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 编写代码和测试
4. 运行质量检查
5. 提交Pull Request

### 代码审查清单

- [ ] 代码遵循项目规范
- [ ] 包含充分的单元测试
- [ ] 文档字符串完整
- [ ] 性能影响可接受
- [ ] 安全检查通过
- [ ] 向后兼容性考虑

## 📞 获取帮助

- 提交Issue: [GitHub Issues](https://github.com/hey-aibi/hey-aibi/issues)
- 讨论区: [GitHub Discussions](https://github.com/hey-aibi/hey-aibi/discussions)
- 邮件: <EMAIL>
