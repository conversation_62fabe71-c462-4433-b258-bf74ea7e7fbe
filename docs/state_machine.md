# 语音助手状态机管理系统

## 概述

状态机管理系统是语音助手的核心组件，负责管理系统的各种状态转换和对话流程控制，实现了连续对话功能，让用户可以在一次唤醒后进行多轮交互。

## 系统状态

### 状态定义

```python
class SystemState(Enum):
    IDLE = "idle"                           # 空闲状态
    WAKE_WORD_LISTENING = "wake_listening"  # 监听唤醒词
    SPEECH_COLLECTING = "speech_collecting" # 收集语音
    SPEECH_PROCESSING = "speech_processing" # 处理语音
    ACTIVE_CONVERSATION = "active_conv"     # 活跃对话模式
    RESPONDING = "responding"               # 系统响应中
    ERROR = "error"                         # 错误状态
```

### 状态说明

- **IDLE**: 系统空闲状态，服务未启动
- **WAKE_WORD_LISTENING**: 监听唤醒词状态，等待用户说"hey aibi"
- **SPEECH_COLLECTING**: 收集用户语音状态，ASR服务正在收集音频
- **SPEECH_PROCESSING**: 处理语音状态，ASR服务正在识别语音
- **ACTIVE_CONVERSATION**: 活跃对话模式，系统保持激活状态等待后续指令
- **RESPONDING**: 系统响应状态，TTS正在播放回复（预留）
- **ERROR**: 错误状态，系统出现异常

## 状态转换流程

### 基本对话流程

```
IDLE → WAKE_WORD_LISTENING → SPEECH_COLLECTING → SPEECH_PROCESSING → ACTIVE_CONVERSATION
```

### 连续对话流程

```
ACTIVE_CONVERSATION → SPEECH_COLLECTING → SPEECH_PROCESSING → ACTIVE_CONVERSATION
```

### 超时和错误处理

```
SPEECH_COLLECTING --timeout--> WAKE_WORD_LISTENING
SPEECH_PROCESSING --error--> WAKE_WORD_LISTENING
ACTIVE_CONVERSATION --timeout--> WAKE_WORD_LISTENING
任何状态 --error--> ERROR → WAKE_WORD_LISTENING
```

## 关键特性

### 1. 连续对话模式

- 用户说出唤醒词后，系统进入活跃对话模式
- 在对话模式下，用户可以继续说话而无需重复唤醒词
- 默认15秒超时后自动回到监听唤醒词状态

### 2. 智能状态管理

- 自动处理状态转换和验证
- 支持状态监听器和回调
- 完整的状态历史记录

### 3. 超时机制

- **对话超时**: 15秒（可配置）
- **语音收集超时**: 10秒（可配置）
- **错误恢复超时**: 5秒（可配置）

### 4. 错误处理

- 自动错误检测和恢复
- 熔断器模式防止级联失败
- 优雅降级机制

## 配置参数

```yaml
state_machine:
  conversation_timeout: 15.0    # 对话超时时间（秒）
  speech_timeout: 10.0          # 语音输入超时时间（秒）
  error_recovery_timeout: 5.0   # 错误恢复超时时间（秒）
```

## API接口

### 核心方法

```python
# 状态查询
get_current_state() -> SystemState
get_state_context() -> StateContext
get_statistics() -> Dict[str, Any]

# 状态控制
transition_to(new_state, event, data=None) -> bool
force_transition(new_state, reason="manual")
reset_state_machine()

# 事件触发
trigger_speech_collection_complete()
trigger_speech_activity()
trigger_response_required(data=None)
trigger_response_complete()

# 监听器管理
add_state_listener(state, listener)
remove_state_listener(state, listener)
```

### 事件处理

状态机自动订阅以下事件：
- `wake_word_detected`: 唤醒词检测事件
- `speech_recognized`: 语音识别完成事件
- `speech_activity_detected`: 语音活动检测事件
- `system_error`: 系统错误事件

## 使用示例

### 基本使用

```python
# 初始化
state_machine = StateMachine(config, event_bus, asr_service)
await state_machine.initialize()
await state_machine.start()

# 查询状态
current_state = state_machine.get_current_state()
stats = state_machine.get_statistics()

# 添加状态监听器
def on_conversation_active(context):
    print("进入对话模式")

state_machine.add_state_listener(
    SystemState.ACTIVE_CONVERSATION, 
    on_conversation_active
)
```

### 手动控制

```python
# 手动触发状态转换
await state_machine.trigger_speech_activity()
await state_machine.trigger_speech_collection_complete()

# 强制状态转换（调试用）
await state_machine.force_transition(SystemState.ERROR, "test")

# 重置状态机
await state_machine.reset_state_machine()
```

## 集成说明

### 与ASR服务集成

状态机直接控制ASR服务的语音收集：
- 进入`SPEECH_COLLECTING`状态时自动启动ASR收集
- 退出`SPEECH_COLLECTING`状态时自动停止ASR收集
- 通过`asr_service`参数传递ASR服务实例

### 与事件总线集成

状态机通过事件总线进行通信：
- 订阅相关事件进行状态转换
- 发布状态变化事件供其他组件监听
- 支持事件优先级和错误处理

## 监控和调试

### 统计信息

```python
stats = state_machine.get_statistics()
# 返回：
# {
#     "current_state": "active_conv",
#     "transition_count": 15,
#     "conversation_duration": 45.2,
#     "state_duration": 3.1,
#     "last_interaction_time": **********.0,
#     "history_count": 20
# }
```

### 状态历史

```python
history = state_machine.get_state_history()
# 返回状态转换历史记录列表
```

### 健康检查

```python
is_healthy = await state_machine._health_check_impl()
# 检查状态机是否正常工作
```

## 最佳实践

1. **合理设置超时时间**: 根据实际使用场景调整超时参数
2. **监听状态变化**: 使用状态监听器实现业务逻辑
3. **错误处理**: 实现适当的错误恢复机制
4. **性能监控**: 定期检查状态转换统计信息
5. **测试覆盖**: 编写完整的状态转换测试用例

## 故障排除

### 常见问题

1. **状态卡住**: 检查是否有未处理的异常或死锁
2. **转换失败**: 验证状态转换规则和事件名称
3. **超时问题**: 调整超时参数或检查处理逻辑
4. **内存泄漏**: 检查状态监听器和定时器的清理

### 调试工具

- 启用详细日志记录
- 使用状态历史分析问题
- 利用强制转换进行故障恢复
- 监控统计信息发现异常模式
