# 基础对话管理服务

## 概述

基础对话管理服务是语音助手的核心组件之一，负责管理对话上下文、会话状态跟踪、消息历史记录和简单的打断检测。它与状态机服务协同工作，提供完整的对话管理功能。

## 核心功能

### 1. 对话上下文管理
- **会话跟踪**: 为每个对话创建唯一的会话ID
- **消息历史**: 记录完整的对话历史，包括用户输入和助手回复
- **上下文数据**: 存储对话相关的元数据和状态信息
- **自动清理**: 定期清理过期的会话数据

### 2. 消息类型管理
- **USER**: 用户消息（语音识别结果）
- **ASSISTANT**: 助手消息（系统回复）
- **SYSTEM**: 系统消息（状态变化、事件通知）
- **ERROR**: 错误消息（识别失败、系统错误）

### 3. 智能文本处理
- **噪音过滤**: 自动移除ASR输出中的特殊标记和噪音
- **文本清理**: 处理标点符号、语气词等
- **长度验证**: 过滤过短或无效的输入

### 4. 打断检测
- **实时监控**: 监听语音活动事件
- **智能判断**: 基于活动级别判断是否为有效打断
- **回调机制**: 支持自定义打断处理逻辑

## 数据结构

### ConversationMessage
```python
@dataclass
class ConversationMessage:
    id: str                    # 消息唯一ID
    type: MessageType          # 消息类型
    content: str               # 消息内容
    timestamp: float           # 时间戳
    confidence: float          # 置信度（用于语音识别）
    metadata: Dict[str, Any]   # 元数据
```

### ConversationContext
```python
@dataclass
class ConversationContext:
    session_id: str                        # 会话ID
    user_id: Optional[str]                 # 用户ID
    start_time: float                      # 开始时间
    last_activity_time: float              # 最后活动时间
    state: ConversationState               # 对话状态
    messages: List[ConversationMessage]    # 消息列表
    context_data: Dict[str, Any]           # 上下文数据
    turn_count: int                        # 对话轮数
```

## 配置参数

```yaml
conversation_service:
  max_context_length: 10          # 最大上下文长度（消息数）
  max_turn_count: 50              # 最大对话轮数
  session_timeout: 300.0          # 会话超时时间（秒）
  enable_interruption: true       # 是否启用打断检测
  interruption_threshold: 0.5     # 打断检测阈值
```

## 事件处理

### 订阅的事件
- `speech_recognized`: 语音识别完成
- `wake_word_detected`: 唤醒词检测
- `state_changed`: 状态机状态变化
- `speech_activity_detected`: 语音活动检测
- `tts_started`: TTS开始播放
- `tts_finished`: TTS播放完成

### 发布的事件
- `conversation_started`: 对话开始
- `conversation_ended`: 对话结束
- `user_input_received`: 用户输入接收
- `user_interruption`: 用户打断检测
- `conversation_error`: 对话错误
- `empty_input_detected`: 空输入检测
- `max_turns_reached`: 达到最大轮数

## API接口

### 核心方法

```python
# 对话管理
async def start_new_conversation(user_id: Optional[str] = None) -> str
async def force_end_conversation(reason: str = "manual")

# 消息管理
def add_assistant_message(content: str, metadata: Optional[Dict] = None) -> str
def get_recent_messages(count: int = 5) -> List[ConversationMessage]

# 上下文数据
async def set_conversation_context_data(key: str, value: Any)
def get_conversation_context_data(key: str, default: Any = None) -> Any

# 状态查询
def get_current_context() -> Optional[ConversationContext]
def get_conversation_history(session_id: str) -> Optional[ConversationContext]
def get_conversation_statistics() -> Dict[str, Any]

# 打断检测
def add_interruption_callback(callback: Callable)
def remove_interruption_callback(callback: Callable)
```

### 统计信息

```python
stats = conversation_service.get_conversation_statistics()
# 返回：
# {
#     "total_conversations": 15,
#     "total_messages": 120,
#     "average_conversation_length": 45.2,
#     "active_sessions": 3,
#     "current_session_id": "abc123...",
#     "current_conversation_duration": 23.5,
#     "current_turn_count": 5,
#     "is_processing": false,
#     "is_responding": false
# }
```

## 使用示例

### 基本使用

```python
# 初始化
conversation_service = ConversationService(config, event_bus)
await conversation_service.initialize()
await conversation_service.start()

# 开始新对话
session_id = await conversation_service.start_new_conversation("user123")

# 添加助手消息
conversation_service.add_assistant_message("你好！我是AI助手。")

# 设置上下文数据
await conversation_service.set_conversation_context_data("user_mood", "开心")

# 获取对话统计
stats = conversation_service.get_conversation_statistics()
```

### 打断检测

```python
def handle_interruption(context, event_data):
    print(f"用户打断了对话，活动级别: {event_data['activity_level']}")
    # 停止当前TTS播放
    # 准备接收新的用户输入

# 添加打断回调
conversation_service.add_interruption_callback(handle_interruption)
```

### 自定义消息处理

```python
# 监听用户输入事件
async def on_user_input(event_data):
    session_id = event_data['session_id']
    text = event_data['text']
    confidence = event_data['confidence']
    
    # 处理用户输入
    response = await process_user_input(text)
    
    # 添加助手回复
    conversation_service.add_assistant_message(response)

await event_bus.subscribe("user_input_received", on_user_input)
```

## 与状态机集成

对话管理服务与状态机服务紧密集成：

1. **状态同步**: 根据状态机状态更新对话状态
2. **事件协调**: 共同处理语音识别和唤醒词事件
3. **生命周期管理**: 状态机控制对话的开始和结束

```python
# 状态机状态 -> 对话状态映射
state_mapping = {
    'speech_collecting': ConversationState.LISTENING,
    'speech_processing': ConversationState.PROCESSING,
    'active_conv': ConversationState.WAITING_FOLLOWUP,
    'responding': ConversationState.RESPONDING
}
```

## 文本清理功能

### 自动清理规则

```python
# 移除的噪音模式
noise_patterns = [
    '<|startoftranscript|>', '<|endoftranscript|>',
    '<|nospeech|>', '<|silence|>',
    '。', '，', '、', '？', '！',  # 单独的标点符号
    'uh', 'um', 'er', 'ah',      # 语气词
]

# 示例
input_text = "<|startoftranscript|>你好，，，世界！！！<|endoftranscript|>"
cleaned_text = "你好世界"  # 清理后的结果
```

## 性能优化

### 内存管理
- **历史限制**: 限制消息历史长度，自动修剪旧消息
- **会话清理**: 定期清理过期会话
- **缓存策略**: 智能缓存常用数据

### 并发处理
- **异步设计**: 所有IO操作都是异步的
- **事件驱动**: 基于事件总线的松耦合架构
- **线程安全**: 支持多线程环境

## 监控和调试

### 日志记录
- **结构化日志**: 使用标准的日志格式
- **事件追踪**: 记录所有重要事件
- **性能指标**: 监控响应时间和资源使用

### 调试工具
- **状态检查**: 实时查看对话状态
- **消息历史**: 完整的消息记录
- **统计分析**: 对话质量分析

## 最佳实践

1. **合理设置参数**: 根据使用场景调整超时和限制参数
2. **监控资源使用**: 定期检查内存和性能指标
3. **错误处理**: 实现完善的错误处理和恢复机制
4. **用户体验**: 提供清晰的状态反馈和错误提示
5. **数据保护**: 注意用户隐私和数据安全

## 故障排除

### 常见问题
1. **内存泄漏**: 检查会话清理和消息修剪
2. **响应延迟**: 优化事件处理和数据库操作
3. **状态不一致**: 检查与状态机的同步
4. **打断失效**: 验证语音活动检测配置

### 调试步骤
1. 检查服务状态和健康检查
2. 查看事件总线的消息流
3. 分析对话统计信息
4. 检查日志中的错误信息
