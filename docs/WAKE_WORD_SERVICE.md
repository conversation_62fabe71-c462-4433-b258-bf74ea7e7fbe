# 唤醒词检测服务文档

## 概述

唤醒词检测服务（WakeWordService）是智能语音助手系统的核心组件之一，专门负责检测"hey aibi"唤醒词。该服务基于PyTorch实现的轻量级CNN模型，支持实时音频流处理和低延迟检测。

## 特性

- **实时检测**: 支持连续音频流的实时唤醒词检测
- **低延迟**: 目标检测延迟 < 100ms
- **预训练模型**: 使用预训练的"hey aibi"唤醒词检测模型
- **GPU加速**: 支持CUDA加速推理（可选）
- **事件驱动**: 基于事件总线的异步通信
- **性能监控**: 内置检测统计和性能指标
- **容错机制**: 完善的错误处理和恢复机制

## 架构设计

### 核心组件

1. **WakeWordCNN**: 基于1D卷积神经网络的检测模型
2. **AudioFeatureExtractor**: 音频特征提取器（MFCC）
3. **WakeWordDetector**: 唤醒词检测器
4. **AudioBuffer**: 音频缓冲区管理
5. **WakeWordService**: 主服务类

### 数据流

```
音频输入 → 音频缓冲区 → 滑动窗口 → 特征提取 → 模型推理 → 检测结果 → 事件发送
```

## 配置参数

### 基本配置

```yaml
wake_word:
  model_path: "models/wake_word/hey_aibi.onnx"  # 模型文件路径
  keyword: "hey aibi"                           # 唤醒词
  confidence_threshold: 0.85                    # 置信度阈值（推荐0.85）
  window_size: 1.5                             # 检测窗口大小（秒）
  hop_size: 0.5                               # 滑动步长（秒）
  enabled: true                               # 是否启用
  cooldown_period: 2.0                        # 冷却期（秒）
```

### 音频配置

```yaml
audio:
  sample_rate: 16000    # 采样率
  chunk_size: 1024      # 音频块大小
  channels: 1           # 声道数
```

## 使用方法

### 1. 基本使用

```python
import asyncio
from src.services.wake_word_service import WakeWordService
from src.services.event_bus import EventBus

async def main():
    # 创建事件总线
    event_bus = EventBus()
    await event_bus.initialize()
    
    # 配置
    config = {
        'model_path': 'models/wake_word/hey_aibi.onnx',
        'keyword': 'hey aibi',
        'confidence_threshold': 0.85,
        'enabled': True
    }
    
    # 创建服务
    service = WakeWordService(config, event_bus)
    
    # 订阅唤醒事件
    async def on_wake_word(data):
        print(f"检测到唤醒词: {data['keyword']}")
    
    await event_bus.subscribe("wake_word_detected", on_wake_word)
    
    # 启动服务
    await service.initialize()
    await service.start()
    
    # 发送音频数据
    await event_bus.emit("audio_chunk", {"audio_data": audio_data})

asyncio.run(main())
```

### 2. 运行演示

```bash
python examples/wake_word_demo.py
```

## 模型信息

当前使用的预训练模型具有以下特性：

### 模型规格
- **模型格式**: ONNX / PyTorch
- **输入特征**: MFCC (40维)
- **采样率**: 16kHz
- **窗长**: 25ms
- **帧移**: 10ms
- **输入形状**: [1, 1, 100, 40]
- **输出形状**: [1, 1]
- **推荐阈值**: 0.85

### 性能指标
- **检测延迟**: < 100ms
- **准确率**: 高精度唤醒词检测
- **误触率**: 低误触发率
- **适用场景**: 安静和轻度噪声环境

## API 参考

### WakeWordService

#### 方法

- `initialize()`: 初始化服务
- `start()`: 启动服务
- `stop()`: 停止服务
- `health_check()`: 健康检查
- `set_confidence_threshold(threshold)`: 设置置信度阈值
- `add_detection_callback(callback)`: 添加检测回调
- `get_detection_stats()`: 获取检测统计

#### 事件

- **订阅事件**:
  - `audio_chunk`: 音频数据块

- **发送事件**:
  - `wake_word_detected`: 唤醒词检测事件

### 事件数据格式

#### wake_word_detected

```json
{
  "keyword": "hey aibi",
  "confidence": 0.85,
  "timestamp": **********.123,
  "audio_length": 24000,
  "service": "WakeWordService"
}
```

## 性能优化

### 1. 模型优化

- 使用ONNX格式提高推理速度
- 启用GPU加速（如果可用）
- 模型量化减少内存占用

### 2. 音频处理优化

- 调整窗口大小和步长平衡延迟和准确性
- 使用合适的音频缓冲区大小
- 优化特征提取参数

### 3. 系统优化

- 使用多线程处理音频数据
- 合理设置冷却期避免重复检测
- 监控内存使用和CPU占用

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型文件路径
   - 确认模型格式正确
   - 检查依赖库版本

2. **检测准确率低**
   - 调整置信度阈值
   - 检查音频质量
   - 确认环境噪声水平

3. **检测延迟高**
   - 减小窗口大小
   - 优化模型结构
   - 启用GPU加速

4. **内存占用高**
   - 调整缓冲区大小
   - 使用模型量化
   - 优化音频处理

### 调试方法

1. **启用详细日志**
   ```python
   logging.getLogger('src.services.wake_word_service').setLevel(logging.DEBUG)
   ```

2. **检查服务状态**
   ```python
   status = service.get_status()
   metrics = service.get_metrics()
   stats = service.get_detection_stats()
   ```

3. **监控性能**
   ```python
   # 检测统计
   print(f"检测次数: {stats['detection_count']}")
   print(f"唤醒次数: {stats['wake_word_count']}")
   print(f"平均检测时间: {stats['avg_detection_time']:.3f}s")
   ```

## 最佳实践

1. **模型使用**
   - 使用推荐的置信度阈值(0.85)
   - 确保音频质量符合要求
   - 定期监控检测性能

2. **部署配置**
   - 根据硬件配置调整参数
   - 设置合适的置信度阈值
   - 启用性能监控

3. **系统集成**
   - 使用事件驱动架构
   - 实现优雅的错误处理
   - 提供健康检查接口

## 扩展功能

### 多唤醒词支持

可以扩展服务支持多个唤醒词：

```python
# 配置多个唤醒词
config = {
    'keywords': ['hey aibi', 'hello assistant'],
    'models': {
        'hey aibi': 'models/wake_word/hey_aibi.onnx',
        'hello assistant': 'models/wake_word/hello_assistant.onnx'
    }
}
```

### 自适应阈值

根据环境噪声自动调整置信度阈值：

```python
def adaptive_threshold(noise_level):
    base_threshold = 0.7
    if noise_level > 0.5:
        return min(base_threshold + 0.1, 0.9)
    return base_threshold
```

## 相关文档

- [音频管理器文档](AUDIO_MANAGER.md)
- [事件总线文档](EVENT_BUS.md)
- [系统架构文档](../架构设计文档.md)
