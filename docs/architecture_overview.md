# 语音助手系统架构概览

## 📁 项目结构

```
hey-aibi/
├── src/                          # 源代码
│   ├── application/              # 应用层
│   │   └── voice_assistant.py    # 主应用入口 ⭐
│   ├── core/                     # 核心组件
│   │   └── service_manager.py    # 服务管理器
│   └── services/                 # 服务层
│       ├── event_bus.py          # 事件总线
│       ├── wake_word.py          # 唤醒词检测
│       ├── asr_service.py        # 语音识别
│       ├── state_machine.py      # 状态机管理
│       └── conversation_service.py # 对话管理
├── scripts/                      # 脚本
│   └── test_main.py              # 手动集成测试 🧪
├── tests/                        # 自动化测试
│   └── test_end_to_end.py        # 自动化集成测试 🤖
└── docs/                         # 文档
```

## 🎯 主要入口点

### 1. 主应用 - `src/application/voice_assistant.py` ⭐

**用途**: 生产环境的主应用入口
**特点**:
- 集成ServiceManager进行统一服务管理
- 完整的音频流处理和信号处理
- 优雅的启动和关闭流程
- 遵循分层架构设计

**使用方法**:
```bash
conda activate aibi
python src/application/voice_assistant.py
```

### 2. 手动集成测试 - `scripts/test_main.py` 🧪

**用途**: 手动集成测试和演示
**特点**:
- 需要真实音频设备
- 用于验证完整功能
- 适合开发和调试
- 包含详细的状态输出

**使用方法**:
```bash
conda activate aibi
python scripts/test_main.py
```

### 3. 自动化测试 - `tests/test_end_to_end.py` 🤖

**用途**: 自动化集成测试
**特点**:
- 使用mock，不需要真实音频设备
- 适合CI/CD流水线
- pytest框架，标准化测试
- 快速验证功能完整性

**使用方法**:
```bash
conda activate aibi
python tests/test_end_to_end.py
# 或
pytest tests/test_end_to_end.py -v
```

## 🏗️ 架构层次

### 应用层 (Application Layer)
- **VoiceAssistant**: 主应用控制器
- 负责整体流程控制和用户交互

### 核心层 (Core Layer)
- **ServiceManager**: 服务生命周期管理
- 统一的服务初始化、启动、停止和健康检查

### 服务层 (Service Layer)
- **EventBus**: 事件驱动通信
- **WakeWordService**: 唤醒词检测
- **ASRService**: 语音识别
- **StateMachine**: 状态管理
- **ConversationService**: 对话管理

## 🔄 数据流

```
音频输入 → EventBus → WakeWordService → StateMachine → ASRService → ConversationService
    ↑                                        ↓
    └─────────── 音频回调 ←─────────── 状态变化事件
```

## 📋 测试策略

### 测试类型对比

| 特性 | scripts/test_main.py | tests/test_end_to_end.py |
|------|---------------------|--------------------------|
| **用途** | 手动集成测试 | 自动化测试 |
| **音频设备** | 需要真实设备 | 使用mock |
| **运行环境** | 开发环境 | CI/CD |
| **测试深度** | 完整功能验证 | 单元和集成测试 |
| **执行方式** | 手动运行 | 自动化执行 |
| **适用场景** | 演示、调试、验证 | 回归测试、质量保证 |

### 测试覆盖范围

1. **服务管理器测试**
   - 服务初始化和启动
   - 服务健康检查
   - 服务重启和恢复

2. **完整对话流程测试**
   - 唤醒词检测 → 语音收集 → 语音识别 → 对话管理
   - 连续对话功能
   - 超时和错误处理

3. **状态机测试**
   - 状态转换逻辑
   - 事件处理
   - 异常恢复

4. **对话管理测试**
   - 上下文管理
   - 消息历史
   - 打断检测

## 🚀 部署和运行

### 开发环境
```bash
# 激活环境
conda activate aibi

# 运行主应用
python src/application/voice_assistant.py

# 或运行手动测试
python scripts/test_main.py
```

### 测试环境
```bash
# 运行自动化测试
python tests/test_end_to_end.py

# 或使用pytest
pytest tests/ -v
```

## 🔧 配置管理

配置通过ServiceManager统一管理，包括：
- 事件总线配置
- 唤醒词服务配置
- ASR服务配置
- 状态机配置
- 对话管理配置

## 📊 监控和调试

### 系统状态监控
- 服务健康检查
- 性能统计
- 错误计数和恢复

### 调试工具
- 详细的状态输出
- 事件流追踪
- 错误日志记录

## 🎯 最佳实践

1. **开发阶段**: 使用 `scripts/test_main.py` 进行功能验证
2. **测试阶段**: 使用 `tests/test_end_to_end.py` 进行自动化测试
3. **生产部署**: 使用 `src/application/voice_assistant.py` 作为主入口
4. **问题调试**: 查看详细日志和状态输出
5. **性能优化**: 监控系统统计信息

## 🔄 持续集成

建议的CI/CD流程：
1. 代码提交触发自动化测试
2. 运行 `pytest tests/test_end_to_end.py`
3. 验证所有服务正常工作
4. 检查代码覆盖率
5. 部署到测试环境

这种架构设计确保了：
- **清晰的职责分离**: 每个组件都有明确的职责
- **灵活的测试策略**: 支持手动和自动化测试
- **统一的服务管理**: ServiceManager提供一致的服务生命周期管理
- **可扩展性**: 易于添加新的服务和功能
