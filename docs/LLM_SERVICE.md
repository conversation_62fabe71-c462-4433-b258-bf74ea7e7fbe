# LLM服务使用文档

## 概述

LLM服务是语音助手系统服务层中负责与大语言模型交互的核心组件，支持Dify平台集成，提供同步和流式对话功能。

## 功能特性

- ✅ **Dify平台集成**: 支持Dify API调用
- ✅ **异步处理**: 基于asyncio的异步架构
- ✅ **流式对话**: 支持实时流式响应
- ✅ **错误处理**: 完善的重试机制和错误处理
- ✅ **对话管理**: 支持多轮对话和上下文管理
- ✅ **配置灵活**: 支持多种配置选项

## 架构位置

```
应用层 (Application Layer)
├── VoiceAssistant (主控制器)
└── StateMachine (状态管理)

服务层 (Service Layer)  ← LLM服务位于此层
├── WakeWordService (唤醒词检测)
├── SpeechService (语音识别)
├── ConversationService (对话管理)
├── LLMService (大语言模型服务) ← 这里
├── TTSService (语音合成)
└── EventBus (事件总线)

基础设施层 (Infrastructure Layer)
├── AudioManager (音频管理)
├── ConfigManager (配置管理)
└── PerformanceMonitor (性能监控)
```

## 快速开始

### 1. 基本配置

```yaml
# config/config.yaml
llm:
  provider: "dify"
  api_url: "http://11.20.60.13/v1"
  api_key: "app-NIfC6KMjycOJyp2p85LGzR0u"
  timeout: 30
  retry_attempts: 3
  retry_delay: 1.0
  max_retry_delay: 10.0
```

### 2. 简单使用示例

```python
import asyncio
from src.services.llm import DifyLLMService
from src.interfaces.llm_interface import LLMRequest, LLMMessage

async def simple_chat():
    # 创建服务
    service = DifyLLMService()
    
    # 配置
    config = {
        "api_url": "http://11.20.60.13/v1",
        "api_key": "app-NIfC6KMjycOJyp2p85LGzR0u",
        "timeout": 30,
        "retry_attempts": 3
    }
    
    try:
        # 初始化
        await service.initialize(config)
        
        # 创建请求
        request = LLMRequest(
            messages=[
                LLMMessage(role="user", content="你好", timestamp=time.time())
            ],
            max_tokens=100
        )
        
        # 发送请求
        response = await service.chat(request)
        
        if response:
            print(f"响应: {response.content}")
            print(f"处理时间: {response.processing_time:.2f}秒")
        
    finally:
        await service.cleanup()

# 运行
asyncio.run(simple_chat())
```

### 3. 流式对话示例

```python
async def stream_chat():
    service = DifyLLMService()
    await service.initialize(config)
    
    try:
        request = LLMRequest(
            messages=[
                LLMMessage(role="user", content="介绍一下人工智能", timestamp=time.time())
            ],
            stream=True
        )
        
        print("助手: ", end="", flush=True)
        async for chunk in service.chat_stream(request):
            if not chunk.is_final and chunk.content:
                print(chunk.content, end="", flush=True)
            elif chunk.is_final:
                print("\n完成")
                break
                
    finally:
        await service.cleanup()
```

### 4. 对话管理示例

```python
async def conversation_example():
    service = DifyLLMService()
    await service.initialize(config)
    
    try:
        # 开始对话
        conversation_id = await service.start_conversation("user123")
        
        # 第一轮对话
        user_msg = LLMMessage(role="user", content="我叫张三", timestamp=time.time())
        await service.add_message(conversation_id, user_msg)
        
        request = LLMRequest(
            messages=[user_msg],
            metadata={"conversation_id": conversation_id, "user_id": "user123"}
        )
        
        response = await service.chat(request)
        if response:
            assistant_msg = LLMMessage(role="assistant", content=response.content, timestamp=time.time())
            await service.add_message(conversation_id, assistant_msg)
        
        # 第二轮对话 - 测试记忆
        user_msg2 = LLMMessage(role="user", content="你还记得我的名字吗？", timestamp=time.time())
        await service.add_message(conversation_id, user_msg2)
        
        history = await service.get_history(conversation_id)
        request2 = LLMRequest(
            messages=history,
            metadata={"conversation_id": conversation_id, "user_id": "user123"}
        )
        
        response2 = await service.chat(request2)
        print(f"记忆测试响应: {response2.content}")
        
        # 结束对话
        await service.end_conversation(conversation_id)
        
    finally:
        await service.cleanup()
```

## API参考

### DifyLLMService

主要的LLM服务类，实现了`LLMInterface`和`ConversationInterface`接口。

#### 初始化方法

```python
async def initialize(self, config: Dict[str, Any]) -> bool
```

**参数:**
- `config`: 配置字典，包含以下字段：
  - `api_url`: Dify API地址
  - `api_key`: API密钥
  - `timeout`: 请求超时时间（秒）
  - `retry_attempts`: 重试次数
  - `retry_delay`: 重试延迟（秒）
  - `max_retry_delay`: 最大重试延迟（秒）

**返回:** 初始化是否成功

#### 对话方法

```python
async def chat(self, request: LLMRequest) -> Optional[LLMResponse]
```

**参数:**
- `request`: LLM请求对象

**返回:** LLM响应对象或None（失败时）

```python
async def chat_stream(self, request: LLMRequest) -> AsyncGenerator[LLMStreamChunk, None]
```

**参数:**
- `request`: LLM请求对象

**返回:** 流式响应块的异步生成器

#### 对话管理方法

```python
async def start_conversation(self, user_id: str) -> str
async def add_message(self, conversation_id: str, message: LLMMessage) -> None
async def get_history(self, conversation_id: str, limit: int = 10) -> List[LLMMessage]
async def clear_history(self, conversation_id: str) -> None
async def end_conversation(self, conversation_id: str) -> None
def is_conversation_active(self, conversation_id: str) -> bool
```

#### 其他方法

```python
async def health_check(self) -> bool
def get_provider(self) -> LLMProvider
def get_model_info(self) -> Dict[str, Any]
async def cleanup() -> None
```

## 数据结构

### LLMRequest

```python
@dataclass
class LLMRequest:
    messages: List[LLMMessage]
    max_tokens: int = 1000
    temperature: float = 0.7
    stream: bool = False
    metadata: Dict[str, Any] = None
```

### LLMResponse

```python
@dataclass
class LLMResponse:
    content: str
    usage: Dict[str, int]
    model: str
    finish_reason: str
    processing_time: float
```

### LLMMessage

```python
@dataclass
class LLMMessage:
    role: str  # "user", "assistant", "system"
    content: str
    timestamp: float
```

## 错误处理

服务提供了完善的错误处理机制：

1. **网络错误**: 自动重试，支持指数退避
2. **API错误**: 记录详细错误信息
3. **超时处理**: 可配置的超时时间
4. **参数验证**: 请求参数验证

## 性能优化

1. **连接复用**: 使用aiohttp会话复用连接
2. **异步处理**: 基于asyncio的异步架构
3. **流式响应**: 支持实时流式处理
4. **重试机制**: 智能重试避免不必要的失败

## 测试

### 运行单元测试

```bash
conda run -n aibi python -m pytest tests/unit/test_llm_service.py -v
```

### 运行集成测试

```bash
conda run -n aibi python -m pytest tests/integration/test_llm_integration.py -v -m integration
```

### 运行演示脚本

```bash
conda run -n aibi python examples/llm_client_demo.py
```

## 注意事项

1. **API密钥安全**: 确保API密钥的安全存储
2. **网络连接**: 确保能够访问Dify API服务
3. **资源清理**: 使用完毕后调用`cleanup()`方法
4. **错误处理**: 适当处理网络错误和API错误
5. **参数配置**: 根据具体应用调整Dify应用的输入参数

## 故障排查

### 常见问题

1. **"k is required in input form"**: 确保Dify应用配置了正确的输入参数
2. **"Conversation Not Exists"**: 检查对话ID是否有效
3. **网络超时**: 检查网络连接和超时配置
4. **API密钥错误**: 验证API密钥是否正确

### 调试方法

1. 启用详细日志记录
2. 检查网络连接
3. 验证API配置
4. 使用简单测试脚本验证功能

## 架构优势

将LLM功能放在服务层的优势：

1. **职责清晰**: 服务层专注于业务逻辑处理
2. **易于扩展**: 可以轻松添加其他LLM提供商
3. **解耦合**: 与基础设施层分离，降低耦合度
4. **可测试**: 更容易进行单元测试和集成测试
5. **可维护**: 符合三层架构的设计原则
