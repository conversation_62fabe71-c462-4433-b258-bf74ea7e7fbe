# 音频管理器使用指南

## 概述

音频管理器 (`AudioManager`) 是Hey-AIBI语音助手系统的核心组件之一，负责跨平台音频输入输出管理，支持实时音频流处理。

## 主要功能

### 🎤 音频输入管理
- 跨平台音频设备检测
- 实时音频录制
- 音频数据缓冲和管理
- 支持多种音频格式和采样率

### 🔊 音频输出管理
- 音频播放功能
- 支持实时音频流输出
- 音频设备选择和配置

### 📊 音频监控
- 实时音频统计
- 设备健康检查
- 性能监控和告警

### 🔧 设备管理
- 自动设备检测
- 设备兼容性测试
- 推荐设备选择

## 快速开始

### 1. 基本使用

```python
import asyncio
from src.infrastructure.audio_manager import AudioManager
from src.interfaces.audio_interface import AudioConfig

async def main():
    # 创建音频配置
    config = AudioConfig(
        sample_rate=16000,
        channels=1,
        chunk_size=1024,
        buffer_size=32000
    )
    
    # 创建音频管理器
    manager_config = {
        'sample_rate': 16000,
        'channels': 1,
        'chunk_size': 1024,
        'buffer_size': 32000
    }
    
    manager = AudioManager(manager_config)
    
    # 初始化
    await manager.initialize(config)
    await manager.start()
    
    # 开始录音
    await manager.start_recording()
    
    # 录音3秒
    await asyncio.sleep(3.0)
    
    # 停止录音
    await manager.stop_recording()
    
    # 清理资源
    await manager.cleanup()

# 运行
asyncio.run(main())
```

### 2. 音频回调处理

```python
def audio_callback(audio_data):
    """音频数据回调函数"""
    print(f"接收到音频数据: {len(audio_data)} 帧")
    # 处理音频数据...

# 注册回调
manager.register_audio_callback(audio_callback)
```

### 3. 异步音频回调

```python
from src.interfaces.audio_interface import AudioCallbackInterface

class MyAudioCallback(AudioCallbackInterface):
    async def on_audio_data(self, audio_data):
        """处理音频数据"""
        print(f"异步处理音频: {len(audio_data)} 帧")
    
    async def on_audio_error(self, error):
        """处理音频错误"""
        print(f"音频错误: {error}")

# 注册异步回调
callback = MyAudioCallback()
manager.register_callback_interface(callback)
```

## 设备检测和测试

### 使用设备检测器

```python
from src.infrastructure.audio_device_detector import AudioDeviceDetector

detector = AudioDeviceDetector()

# 检测所有设备
devices = detector.detect_all_devices()
for device in devices:
    print(f"{device.name} ({'输入' if device.is_input else '输出'})")

# 获取默认设备
default_input, default_output = detector.get_default_devices()

# 测试设备功能
config = AudioConfig(sample_rate=16000, channels=1)
test_results = detector.test_all_devices(config)

# 生成设备报告
report = detector.generate_device_report(config)
print(report)
```

### 获取推荐设备

```python
# 获取推荐的输入和输出设备（基于延迟测试）
rec_input, rec_output = detector.get_recommended_devices(config)

if rec_input:
    print(f"推荐输入设备: {rec_input.name}")
if rec_output:
    print(f"推荐输出设备: {rec_output.name}")
```

## 配置选项

### AudioConfig 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `sample_rate` | int | 16000 | 采样率 (Hz) |
| `channels` | int | 1 | 声道数 |
| `chunk_size` | int | 1024 | 音频块大小 |
| `buffer_size` | int | 32000 | 缓冲区大小 |
| `input_device_id` | int | None | 输入设备ID (None=默认) |
| `output_device_id` | int | None | 输出设备ID (None=默认) |

### AudioManager 配置

```python
manager_config = {
    'sample_rate': 16000,        # 采样率
    'channels': 1,               # 声道数
    'chunk_size': 1024,          # 音频块大小
    'buffer_size': 32000,        # 缓冲区大小
    'input_device': None,        # 输入设备ID
    'output_device': None,       # 输出设备ID
    'health_check_interval': 60, # 健康检查间隔(秒)
    'max_retries': 3,           # 最大重试次数
    'retry_delay': 1.0          # 重试延迟(秒)
}
```

## 音频数据处理

### 获取最近音频数据

```python
# 获取最近1秒的音频数据
recent_audio = manager.get_recent_audio(1000)  # 1000ms

if len(recent_audio) > 0:
    print(f"获取到 {len(recent_audio)} 帧音频数据")
```

### 播放音频

```python
import numpy as np

# 生成测试音频（440Hz正弦波）
duration = 1.0
sample_rate = 16000
t = np.linspace(0, duration, int(sample_rate * duration), False)
audio_data = 0.3 * np.sin(2 * np.pi * 440 * t).astype(np.float32)

# 播放音频
await manager.play_audio(audio_data)
```

## 监控和统计

### 获取音频统计

```python
stats = manager.get_audio_stats()
print(f"处理帧数: {stats['input_frames_processed']}")
print(f"缓冲区大小: {stats['buffer_size']}")
print(f"缓冲区溢出: {stats['buffer_overruns']}")
print(f"回调错误: {stats['callback_errors']}")
```

### 服务状态监控

```python
# 获取服务状态
status = manager.get_status()
print(f"服务状态: {status.value}")

# 健康检查
health = await manager.health_check()
print(f"健康状态: {'正常' if health else '异常'}")

# 获取服务指标
metrics = manager.get_metrics()
print(f"成功率: {metrics['metrics']['success_rate']:.2%}")
print(f"平均响应时间: {metrics['metrics']['avg_response_time']:.2f}ms")
```

## 测试工具

### 使用测试脚本

```bash
# 运行音频系统测试
python scripts/test_audio.py

# 指定录音时长
python scripts/test_audio.py --duration 5

# 详细输出
python scripts/test_audio.py --verbose
```

### 验证安装

```bash
# 验证音频管理器基本功能
python verify_audio.py
```

## 故障排查

### 常见问题

1. **没有检测到音频设备**
   - 检查音频驱动是否正确安装
   - 确认音频设备已连接并启用
   - 检查系统音频权限

2. **录音无声音**
   - 检查麦克风权限
   - 确认输入设备选择正确
   - 检查麦克风音量设置

3. **播放无声音**
   - 检查输出设备选择
   - 确认音量设置
   - 检查音频数据格式

4. **高延迟或卡顿**
   - 减小chunk_size
   - 检查系统负载
   - 使用推荐的音频设备

### 调试技巧

1. **启用详细日志**
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

2. **监控音频统计**
```python
# 定期检查统计信息
stats = manager.get_audio_stats()
if stats['buffer_overruns'] > 0:
    print("警告: 音频缓冲区溢出")
```

3. **测试设备兼容性**
```python
# 生成详细的设备测试报告
report = detector.generate_device_report(config)
print(report)
```

## 性能优化

### 推荐配置

- **低延迟**: chunk_size=512, buffer_size=16000
- **高质量**: chunk_size=2048, buffer_size=64000
- **平衡**: chunk_size=1024, buffer_size=32000 (默认)

### 优化建议

1. 使用推荐的音频设备
2. 根据需求调整缓冲区大小
3. 避免在音频回调中进行重计算
4. 定期清理音频缓冲区

## API 参考

详细的API文档请参考源代码中的文档字符串和类型注解。

## 支持的平台

- ✅ Windows 10/11
- ✅ macOS 10.14+
- ✅ Linux (Ubuntu 18.04+)

## 依赖要求

- Python 3.8+
- sounddevice >= 0.4.6
- soundfile >= 0.12.1
- numpy >= 1.24.0
