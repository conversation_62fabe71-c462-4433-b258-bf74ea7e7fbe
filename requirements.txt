# 智能语音助手系统依赖包
# 基于三层架构设计的端到端语音交互系统

# ===== 核心框架 =====
asyncio-mqtt>=0.13.0
pydantic>=2.0.0
pyyaml>=6.0
python-dotenv>=1.0.0

# ===== 语音处理 =====
# FunASR - 语音识别和VAD
funasr>=1.0.0
# 音频处理
soundfile>=0.12.1
librosa>=0.10.0
pyaudio>=0.2.11
# 跨平台音频支持
sounddevice>=0.4.6

# ===== 机器学习推理 =====
# ONNX Runtime - CPU推理
onnxruntime>=1.16.0
# 模型量化工具
onnx>=1.15.0
# NumPy数组处理
numpy>=1.24.0

# ===== 语音合成 =====
# CosyVoice相关依赖
torch>=2.0.0
torchaudio>=2.0.0
# 文本处理
jieba>=0.42.1

# ===== 大语言模型集成 =====
# HTTP客户端
httpx>=0.25.0
aiohttp>=3.9.0
# Dify API客户端
requests>=2.31.0

# ===== 开发和测试 =====
# 测试框架
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
# 代码质量
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0
# 性能分析
memory-profiler>=0.61.0
psutil>=5.9.0

# ===== 日志和监控 =====
# 结构化日志
structlog>=23.1.0
# 性能监控
prometheus-client>=0.17.0

# ===== 配置管理 =====
# 配置验证
cerberus>=1.3.4
# 环境变量管理
python-decouple>=3.8
# 文件监控
watchdog>=3.0.0

# ===== 工具库 =====
# 数据类
dataclasses-json>=0.6.0
# 类型提示
typing-extensions>=4.7.0
# 路径处理
pathlib2>=2.3.7

# ===== 可选依赖 =====
# GPU支持（可选）
# torch-audio-cuda>=2.0.0  # 如果需要GPU加速

# ===== 开发工具 =====
# Jupyter支持
jupyter>=1.0.0
ipython>=8.0.0
# 文档生成
sphinx>=7.0.0
# 代码格式化
autopep8>=2.0.0
