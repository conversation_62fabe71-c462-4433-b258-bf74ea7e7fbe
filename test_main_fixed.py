#!/usr/bin/env python3
"""
修复版的完整语音助手测试
强制使用有效的音频设备
"""

import asyncio
import sys
import time
import signal
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.wake_word import WakeWordService
from src.services.asr_service import ASRService
from src.services.tts import CosyVoiceTTSService
from src.services.llm import DifyLLMService
from src.services.event_bus import EventBus
from src.core.state_machine import StateMachine, SystemState
from src.services.conversation_service import ConversationService
from src.core.unified_audio_capture import AudioDeviceManager, UnifiedAudioCapture
from src.infrastructure.audio_manager import AudioManager
from src.interfaces.audio_interface import AudioConfig


class VoiceAssistantTestFixed:
    """修复版语音助手测试"""
    
    def __init__(self):
        # 核心服务
        self.event_bus = None
        self.wake_word_service = None
        self.asr_service = None
        self.tts_service = None
        self.llm_service = None
        self.state_machine = None
        self.conversation_service = None
        
        # 音频相关
        self.audio_device_manager = AudioDeviceManager()
        self.audio_stream = None
        self.is_running = False
        self.sample_rate = 16000
        self.chunk_size = 1024
        self.channels = 1
        self.device_id = None
        
        # 状态管理
        self.is_listening_for_speech = False
        self.speech_timeout = 10.0
        self.speech_timeout_task = None
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n🛑 收到信号 {signum}，正在关闭...")
        self.is_running = False
    
    async def initialize_services(self):
        """初始化所有服务"""
        try:
            print("🚀 正在启动语音助手...")
            
            # 1. 初始化事件总线
            self.event_bus = EventBus()
            await self.event_bus.initialize()
            
            # 设置事件监听器
            await self._setup_event_listeners()
            
            # 2. 初始化唤醒词服务
            wake_word_config = {
                'enabled': True,
                'model_path': 'models/wake_word_model.onnx',
                'threshold': 0.85,
                'sample_rate': self.sample_rate,
                'chunk_size': self.chunk_size,
                'channels': self.channels,
                'device_id': None,
                'debounce_time': 2.0,
                'max_audio_length': 30.0
            }
            
            self.wake_word_service = WakeWordService(wake_word_config, self.event_bus)
            if not await self.wake_word_service.initialize():
                print("⚠️  唤醒词服务初始化失败，将跳过唤醒词检测")
            
            # 3. 初始化ASR服务（使用FunASR完整pipeline）
            asr_config = {
                'enabled': True,
                'model_name': 'models/funasr/iic/SenseVoiceSmall',
                'vad_model': 'models/funasr/fsmn-vad',
                'punc_model': 'models/funasr/ct-punc',
                'device': 'auto',
                'sample_rate': self.sample_rate,
                'max_audio_duration': 30.0,
                'min_audio_duration': 1.0,
                'auto_recognition': False,
                # 智能VAD配置
                'use_smart_vad': True,
                'waiting_timeout': 10.0,
                'speech_end_timeout': 1.2
            }
            
            self.asr_service = ASRService(asr_config, self.event_bus)
            if not await self.asr_service.initialize():
                raise RuntimeError("ASR服务初始化失败")
            
            # 4. 初始化状态机
            self.state_machine = StateMachine(self.event_bus)
            await self.state_machine.initialize()
            await self.state_machine.start()
            
            # 5. 初始化LLM服务
            llm_config = {
                'api_key': 'app-YourDifyAPIKey',
                'base_url': 'https://api.dify.ai/v1',
                'model': 'gpt-4',
                'max_tokens': 2000,
                'temperature': 0.7,
                'timeout': 30.0,
                'max_retries': 3
            }

            self.llm_service = DifyLLMService()
            if not await self.llm_service.initialize(llm_config):
                print("⚠️  LLM服务初始化失败，将使用模拟响应")
            
            # 6. 初始化音频管理器
            audio_manager_config = {
                'sample_rate': self.sample_rate,
                'channels': self.channels,
                'chunk_size': self.chunk_size,
                'input_device_id': None,
                'output_device_id': None
            }
            
            audio_config = AudioConfig(
                sample_rate=16000,
                channels=1,
                chunk_size=1024,
                input_device_id=None,
                output_device_id=None
            )
            
            self.audio_manager = AudioManager(audio_manager_config)
            if not await self.audio_manager.initialize(audio_config):
                print("⚠️  音频管理器初始化失败")
            else:
                print("✅ 音频管理器初始化成功")
            
            # 7. 初始化TTS服务
            print("🔊 正在初始化TTS服务...")
            tts_config = {
                "model_name": "CosyVoice-300M",
                "device": "cpu",
                "sample_rate": 22050,
                "streaming": True,
                "voice": "default",
                "speed": 1.0,
                "volume": 1.0,
                "enable_interruption": True,
                "enable_continuous_playback": True
            }
            
            self.tts_service = CosyVoiceTTSService(tts_config, self.event_bus)
            if hasattr(self.tts_service, 'set_audio_manager'):
                self.tts_service.set_audio_manager(self.audio_manager)
            
            if not await self.tts_service.initialize("CosyVoice-300M", tts_config):
                raise RuntimeError("TTS服务初始化失败")
            print("✅ TTS服务初始化成功")
            
            # 8. 初始化对话管理服务
            conversation_config = {
                'max_history': 10,
                'session_timeout': 300.0,
                'enable_context': True,
                'enable_interruption': True
            }

            self.conversation_service = ConversationService(conversation_config, self.event_bus, self.llm_service)
            if not await self.conversation_service.initialize():
                print("⚠️  对话管理服务初始化失败")
            
            # 9. 启动服务
            services_to_start = [
                self.asr_service,
                self.tts_service,
                self.conversation_service
            ]
            
            if self.wake_word_service:
                services_to_start.append(self.wake_word_service)
            
            for service in services_to_start:
                if service and not await service.start():
                    print(f"⚠️  服务启动失败: {service.__class__.__name__}")
            
            return True
            
        except Exception as e:
            print(f"❌ 服务初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def _setup_event_listeners(self):
        """设置事件监听器"""
        await self.event_bus.subscribe("wake_word_detected", self._on_wake_word_detected)
        await self.event_bus.subscribe("speech_recognized", self._on_speech_recognized)
        await self.event_bus.subscribe("llm_response_complete", self._on_llm_response_complete)
        await self.event_bus.subscribe("tts_playback_complete", self._on_tts_complete)
        await self.event_bus.subscribe("state_changed", self._on_state_changed)
    
    async def _on_wake_word_detected(self, data):
        """唤醒词检测事件处理"""
        confidence = data.get('confidence', 0.0)
        print(f"🎯 检测到唤醒词! 置信度: {confidence:.4f}")
        
        # 触发状态转换
        await self.state_machine.transition_to(SystemState.AWAKENED, "wake_word_detected", data)
    
    async def _on_speech_recognized(self, data):
        """语音识别事件处理"""
        text = data.get('text', '')
        confidence = data.get('confidence', 0.0)
        timeout = data.get('timeout', False)
        error = data.get('error', '')
        
        if timeout:
            print(f"⏰ 语音识别超时: {error}")
            await self.state_machine.transition_to(SystemState.WAKE_WORD_LISTENING, "speech_timeout")
            return
        
        if text.strip():
            print(f"👤 用户: {text} (置信度: {confidence:.2f})")
            
            # 发送到对话服务
            await self.event_bus.emit("user_input", {
                "text": text,
                "confidence": confidence,
                "timestamp": time.time()
            })
            
            # 转换到处理状态
            await self.state_machine.transition_to(SystemState.SPEECH_PROCESSING, "speech_recognized", data)
        else:
            print("🔇 未识别到有效语音")
            await self.state_machine.transition_to(SystemState.WAKE_WORD_LISTENING, "no_speech")
    
    async def _on_llm_response_complete(self, data):
        """LLM响应完成事件处理"""
        print("🤖 正在生成LLM响应...")
        await self.state_machine.transition_to(SystemState.ACTIVE_CONVERSATION, "llm_response_complete", data)
    
    async def _on_tts_complete(self, data):
        """TTS播放完成事件处理"""
        print("🔊 TTS播放完成")
        await self.state_machine.transition_to(SystemState.WAKE_WORD_LISTENING, "tts_complete", data)
    
    async def _on_state_changed(self, data):
        """状态变化事件处理"""
        current_state = data.get('current_state', '')
        previous_state = data.get('previous_state', '')
        event = data.get('event', '')
        timestamp = data.get('timestamp', time.time())
        
        time_str = time.strftime('%H:%M:%S', time.localtime(timestamp))
        print(f"🔄 [{time_str}] 状态变化: {previous_state} → {current_state}")
    
    def setup_audio_device(self):
        """设置音频设备 - 使用有效设备"""
        try:
            print("🎤 设置音频设备...")
            
            # 强制使用有效的音频设备
            import sounddevice as sd
            devices = sd.query_devices()
            
            # 寻找有输入通道的设备
            valid_devices = []
            for i, device in enumerate(devices):
                if device['max_input_channels'] > 0:
                    valid_devices.append((i, device))
            
            if not valid_devices:
                print("❌ 未找到有效的输入设备")
                return None, None
            
            # 优先选择默认设备或pulse设备
            selected_device = None
            for device_id, device in valid_devices:
                if 'default' in device['name'].lower() or 'pulse' in device['name'].lower():
                    selected_device = (device_id, device)
                    break
            
            # 如果没有找到默认设备，使用第一个有效设备
            if not selected_device:
                selected_device = valid_devices[0]
            
            device_id, device_info = selected_device
            self.device_id = device_id
            
            print(f"✅ 选择音频设备: [{device_id}] {device_info['name']}")
            return device_info, device_info['name']
            
        except Exception as e:
            print(f"❌ 设置音频设备失败: {e}")
            return None, None
    
    def start_audio_capture(self, device_info):
        """启动音频采集 - 简化版"""
        try:
            device_name = device_info['name']
            print(f"🎤 启动音频采集: {device_name}")
            
            # 使用简化的音频捕获，避免PipeWire问题
            import sounddevice as sd
            
            def audio_callback(indata, frames, time, status):
                """音频回调函数"""
                if status:
                    print(f"音频状态: {status}")
                
                # 将音频数据发送给唤醒词服务和ASR服务
                try:
                    audio_data = indata[:, 0] if indata.ndim > 1 else indata
                    
                    # 发送给唤醒词服务
                    if self.wake_word_service and hasattr(self.wake_word_service, 'add_audio_chunk'):
                        asyncio.create_task(self.wake_word_service.add_audio_chunk(audio_data))
                    
                    # 发送给ASR服务
                    if self.asr_service and hasattr(self.asr_service.audio_buffer, 'add_audio'):
                        self.asr_service.audio_buffer.add_audio(audio_data)
                
                except Exception as e:
                    pass  # 忽略音频回调中的错误
            
            # 启动音频流
            self.audio_stream = sd.InputStream(
                device=self.device_id,
                channels=self.channels,
                samplerate=self.sample_rate,
                blocksize=self.chunk_size,
                callback=audio_callback,
                dtype='float32'
            )
            
            self.audio_stream.start()
            print(f"✅ 音频流已启动 (设备: {self.device_id}, 采样率: {self.sample_rate}Hz)")
            return True
            
        except Exception as e:
            print(f"❌ 启动音频采集失败: {e}")
            return False
    
    async def run(self):
        """运行语音助手"""
        try:
            # 1. 初始化服务
            if not await self.initialize_services():
                return False
            
            # 2. 设置音频设备
            device_info, device_name = self.setup_audio_device()
            if device_info is None:
                print("❌ 未能设置音频设备")
                return False
            
            # 3. 启动音频采集
            if not self.start_audio_capture(device_info):
                return False
            
            # 4. 开始运行
            self.is_running = True
            print(f"🎤 语音助手已启动，正在监听唤醒词 \"hey aibi\"...")
            print(f"📱 使用设备: {device_name}")
            print("⏹️  按 Ctrl+C 退出")
            print("-" * 50)
            
            # 主循环
            while self.is_running:
                await asyncio.sleep(1)
            
            return True
            
        except KeyboardInterrupt:
            print("\n🛑 用户中断")
            return True
        except Exception as e:
            print(f"❌ 运行失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            await self.cleanup()
    
    async def cleanup(self):
        """清理资源"""
        try:
            print("\n🧹 正在清理资源...")

            # 停止音频流
            if hasattr(self, 'audio_stream') and self.audio_stream:
                self.audio_stream.stop()
                self.audio_stream.close()

            # 停止服务
            services = [
                self.conversation_service if hasattr(self, 'conversation_service') else None,
                self.tts_service if hasattr(self, 'tts_service') else None,
                self.asr_service if hasattr(self, 'asr_service') else None,
                self.wake_word_service if hasattr(self, 'wake_word_service') else None,
                self.state_machine if hasattr(self, 'state_machine') else None
            ]

            for service in services:
                if service:
                    try:
                        await service.stop()
                    except Exception as e:
                        print(f"⚠️  停止服务失败: {e}")

            # 清理音频管理器
            if hasattr(self, 'audio_manager') and self.audio_manager:
                await self.audio_manager.cleanup()

            # 关闭事件总线
            if hasattr(self, 'event_bus') and self.event_bus:
                await self.event_bus.shutdown()

            print("✅ 资源清理完成")

        except Exception as e:
            print(f"❌ 清理失败: {e}")


async def main():
    """主函数"""
    assistant = VoiceAssistantTestFixed()
    success = await assistant.run()
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
