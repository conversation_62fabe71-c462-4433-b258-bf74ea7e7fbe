[flake8]
# Flake8代码质量检查配置

# 最大行长度
max-line-length = 100

# 最大复杂度
max-complexity = 10

# 忽略的错误代码
ignore = 
    E203,  # whitespace before ':'
    E501,  # line too long (handled by black)
    W503,  # line break before binary operator
    E402,  # module level import not at top of file
    F401,  # imported but unused (in __init__.py)

# 排除的目录和文件
exclude = 
    .git,
    __pycache__,
    build,
    dist,
    .venv,
    venv,
    .tox,
    .eggs,
    *.egg,
    models,
    logs,
    htmlcov,
    .pytest_cache,
    .mypy_cache

# 每个文件的特定忽略
per-file-ignores = 
    __init__.py:F401,F403
    tests/*:F401,F811,S101
    */test_*.py:F401,F811,S101
    setup.py:F401

# 选择的错误代码
select = E,W,F,C,N

# 统计
statistics = True

# 显示源代码
show-source = True

# 显示pep8错误代码
show-pep8 = True

# 计数
count = True

# 基准
benchmark = True

# 导入顺序检查
import-order-style = google

# 应用导入顺序检查
application-import-names = src

# 文档字符串约定
docstring-convention = google
